"use client";

import { useRef, useState } from "react";
import { motion, AnimatePresence, useInView } from "framer-motion";
import { ChevronDown, HelpCircle } from "lucide-react";
import AnimatedTitle from "./animations/AnimatedTitle";
import { cn } from "@/lib/utils";

type AnimationVariant = {
  hidden: { opacity: number; y: number };
  visible: {
    opacity: number;
    y: number;
    transition: { duration: number; ease: string };
  } | ((_i: number) => {
    opacity: number;
    y: number;
    transition: { duration: number; delay: number; ease: string };
  });
};

interface FAQItem {
  question: string;
  answer: string;
}

interface EnhancedPricingFAQProps {
  faqItems: FAQItem[];
  sectionFadeIn: AnimationVariant;
  itemFadeIn: AnimationVariant;
}

export default function EnhancedPricingFAQ({
  faqItems,
  itemFadeIn,
}: EnhancedPricingFAQProps) {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section
      ref={sectionRef}
      className="py-16 px-4 md:px-6 lg:px-8 relative overflow-hidden"
    >
      <div className="max-w-4xl mx-auto">
        {/* Section Title */}
        <div className="text-center mb-12">
          <AnimatedTitle
            title="Frequently Asked Questions"
            highlightWords={["Questions"]}
            subtitle="Find answers to common questions about our pricing plans"
          />
        </div>

        {/* Decorative elements */}
        <motion.div
          className="absolute top-20 left-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5"
          initial={{ opacity: 0, scale: 0.5, rotate: -10 }}
          animate={isInView ? { opacity: 1, scale: 1, rotate: 0 } : {}}
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          <HelpCircle size={120} />
        </motion.div>
        
        <motion.div
          className="absolute bottom-20 right-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5"
          initial={{ opacity: 0, scale: 0.5, rotate: 10 }}
          animate={isInView ? { opacity: 1, scale: 1, rotate: 0 } : {}}
          transition={{ duration: 0.7, delay: 0.4 }}
        >
          <HelpCircle size={80} />
        </motion.div>

        {/* FAQ Accordion */}
        <div className="space-y-4 relative z-10">
          {faqItems.map((item, index) => (
            <motion.div
              key={index}
              variants={itemFadeIn}
              custom={index}
              initial="hidden"
              animate={isInView ? "visible" : "hidden"}
              className="border border-border rounded-lg overflow-hidden bg-card"
            >
              {/* Question button */}
              <button
                onClick={() => toggleFAQ(index)}
                className={cn(
                  "flex items-center justify-between w-full p-5 text-left transition-colors",
                  openIndex === index
                    ? "bg-muted/50"
                    : "hover:bg-muted/30"
                )}
              >
                <h3 className="text-lg font-medium text-foreground">{item.question}</h3>
                <motion.div
                  animate={{ rotate: openIndex === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                  className={cn(
                    "flex-shrink-0 ml-2 p-1 rounded-full",
                    openIndex === index
                      ? "bg-[var(--brand-gold)]/20 text-[var(--brand-gold)]"
                      : "text-muted-foreground"
                  )}
                >
                  <ChevronDown size={18} />
                </motion.div>
              </button>

              {/* Answer with animation */}
              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="p-5 pt-0 border-t border-border">
                      <motion.p
                        initial={{ y: 10, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                        className="text-muted-foreground"
                      >
                        {item.answer}
                      </motion.p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        {/* Additional help text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="text-center mt-12 text-muted-foreground"
        >
          <p>Still have questions? Contact our support team at <span className="text-[var(--brand-gold)]"><EMAIL></span></p>
        </motion.div>
      </div>
    </section>
  );
}
