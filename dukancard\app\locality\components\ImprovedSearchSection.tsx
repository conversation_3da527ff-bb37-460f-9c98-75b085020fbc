"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useLocalityContext } from "../context/LocalityContext";
import {
  BUSINESS_NAME_PARAM,
  PRODUCT_NAME_PARAM,
  VIEW_TYPE_PARAM,
} from "@/app/(main)/discover/constants/urlParamConstants";

interface ImprovedSearchSectionProps {
  initialValues?: {
    pincode?: string | null;
    city?: string | null;
    locality?: string | null;
  };
}

export default function ImprovedSearchSection({
}: ImprovedSearchSectionProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { viewType, handleBusinessSearch, handleProductSearch } = useLocalityContext();

  // Get search term from URL
  const initialSearchTerm = viewType === "cards"
    ? searchParams.get(BUSINESS_NAME_PARAM) || ""
    : searchParams.get(PRODUCT_NAME_PARAM) || "";

  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [_isSearching, setIsSearching] = useState(false);

  // Update search term when view type changes
  useEffect(() => {
    const newSearchTerm = viewType === "cards"
      ? searchParams.get(BUSINESS_NAME_PARAM) || ""
      : searchParams.get(PRODUCT_NAME_PARAM) || "";
    setSearchTerm(newSearchTerm);
  }, [viewType, searchParams]);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSearching(true);

    // Create a new URLSearchParams object based on the current URL
    const params = new URLSearchParams(searchParams.toString());

    // Update the appropriate search parameter based on view type
    if (viewType === "cards") {
      if (searchTerm) {
        params.set(BUSINESS_NAME_PARAM, searchTerm);
      } else {
        params.delete(BUSINESS_NAME_PARAM);
      }
      params.delete(PRODUCT_NAME_PARAM);
    } else {
      if (searchTerm) {
        params.set(PRODUCT_NAME_PARAM, searchTerm);
      } else {
        params.delete(PRODUCT_NAME_PARAM);
      }
      params.delete(BUSINESS_NAME_PARAM);
    }

    // Update the URL
    router.push(`/locality/${searchParams.get("localSlug")}?${params.toString()}`);

    // Call the appropriate search function
    if (viewType === "cards") {
      handleBusinessSearch(searchTerm);
    } else {
      handleProductSearch(searchTerm);
    }

    setIsSearching(false);
  };

  // Handle clearing the search
  const handleClearSearch = () => {
    setSearchTerm("");
    
    // Create a new URLSearchParams object based on the current URL
    const params = new URLSearchParams(searchParams.toString());
    
    // Remove the search parameter based on view type
    if (viewType === "cards") {
      params.delete(BUSINESS_NAME_PARAM);
    } else {
      params.delete(PRODUCT_NAME_PARAM);
    }
    
    // Update the URL
    router.push(`/locality/${searchParams.get("localSlug")}?${params.toString()}`);
    
    // Call the appropriate search function with empty string
    if (viewType === "cards") {
      handleBusinessSearch("");
    } else {
      handleProductSearch("");
    }
  };

  return (
    <div className="container mx-auto px-4 py-4">
      <div className="flex flex-col md:flex-row gap-4 items-center">
        {/* Search input */}
        <form onSubmit={handleSearch} className="w-full md:flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder={viewType === "cards" ? "Search businesses..." : "Search products..."}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-10 h-10 w-full"
            />
            {searchTerm && (
              <button
                type="button"
                onClick={handleClearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </form>

        {/* View type toggle */}
        <div className="flex items-center space-x-2 w-full md:w-auto">
          <Button
            type="button"
            variant={viewType === "cards" ? "default" : "outline"}
            size="sm"
            onClick={() => {
              const params = new URLSearchParams(searchParams.toString());
              params.set(VIEW_TYPE_PARAM, "cards");
              router.push(`/locality/${searchParams.get("localSlug")}?${params.toString()}`);
            }}
            className="flex-1 md:flex-none"
          >
            Businesses
          </Button>
          <Button
            type="button"
            variant={viewType === "products" ? "default" : "outline"}
            size="sm"
            onClick={() => {
              const params = new URLSearchParams(searchParams.toString());
              params.set(VIEW_TYPE_PARAM, "products");
              router.push(`/locality/${searchParams.get("localSlug")}?${params.toString()}`);
            }}
            className="flex-1 md:flex-none"
          >
            Products
          </Button>
        </div>
      </div>
    </div>
  );
}
