import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import { ToastProvider, useToast } from '@/src/components/ui/Toast';
import { Button, Text, View } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';

// Mock hooks and timers
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    isDark: false,
    colors: {
      card: '#ffffff',
      textPrimary: '#000000',
      textSecondary: '#666666',
      primary: '#C29D5B',
      border: '#cccccc',
      error: '#ff0000',
    },
  }),
}));

jest.useFakeTimers();

const TestComponent = () => {
  const toast = useToast();
  return (
    <View>
      <Button title="Success" onPress={() => toast.success('Success!', 'This is a success toast.')} />
      <Button title="Error" onPress={() => toast.error('Error!', 'This is an error toast.')} />
      <Button title="Dismiss" onPress={() => toast.dismiss()} />
    </View>
  );
};

const renderWithProvider = (component: React.ReactElement) => {
  return render(<ToastProvider>{component}</ToastProvider>);
};

describe('<ToastProvider /> and useToast()', () => {
  it('throws an error if useToast is used outside of a ToastProvider', () => {
    // Prevent console.error from cluttering the test output
    const consoleError = console.error;
    console.error = () => {};
    expect(() => render(<TestComponent />)).toThrow('useToast must be used within a ToastProvider');
    console.error = consoleError;
  });

  it('renders children correctly', () => {
    const { getByText } = renderWithProvider(<Text>Child Component</Text>);
    expect(getByText('Child Component')).toBeTruthy();
  });

  it('displays a success toast when the success method is called', async () => {
    const { getByText, findByText } = renderWithProvider(<TestComponent />);
    fireEvent.press(getByText('Success'));

    const title = await findByText('Success!');
    const description = await findByText('This is a success toast.');

    expect(title).toBeTruthy();
    expect(description).toBeTruthy();
  });

  it('displays an error toast when the error method is called', async () => {
    const { getByText, findByText } = renderWithProvider(<TestComponent />);
    fireEvent.press(getByText('Error'));

    const title = await findByText('Error!');
    const description = await findByText('This is an error toast.');

    expect(title).toBeTruthy();
    expect(description).toBeTruthy();
  });

  it('dismisses the toast automatically after a timeout', async () => {
    const { getByText, findByText, queryByText } = renderWithProvider(<TestComponent />);
    fireEvent.press(getByText('Success'));

    await findByText('Success!');
    
    act(() => {
      jest.runAllTimers();
    });

    expect(queryByText('Success!')).toBeNull();
  });

  it('dismisses the toast when dismiss is called', async () => {
    const { getByText, findByText, queryByText } = renderWithProvider(<TestComponent />);
    fireEvent.press(getByText('Success'));

    await findByText('Success!');
    fireEvent.press(getByText('Dismiss'));

    expect(queryByText('Success!')).toBeNull();
  });

  it('dismisses the toast when pressed', async () => {
    const { getByText, findByText, queryByText } = renderWithProvider(<TestComponent />);
    fireEvent.press(getByText('Success'));

    const toastElement = await findByText('Success!');
    fireEvent.press(toastElement);

    act(() => {
      jest.runAllTimers(); // Allow dismiss animation to complete
    });

    expect(queryByText('Success!')).toBeNull();
  });
});