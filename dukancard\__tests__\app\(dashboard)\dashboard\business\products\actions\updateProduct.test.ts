import { updateProductService } from '@/app/(dashboard)/dashboard/business/products/actions/updateProduct';
import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('updateProductService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return an error if the user is not authenticated', async () => {
    // Arrange
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
      },
    });
    const formData = new FormData();

    // Act
    const result = await updateProductService('prod-123', formData);

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('User not authenticated.');
  });

  it('should return a validation error for invalid data', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    });
    const formData = new FormData();
    formData.append('name', ''); // Invalid name

    // Act
    const result = await updateProductService('prod-123', formData);

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toContain('Invalid data');
  });

  it('should update a product successfully', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProduct = { id: 'prod-123', name: 'Updated Product' };
    const mockUpdate = jest.fn().mockReturnThis();
    const mockEq = jest.fn().mockReturnThis();
    const mockSelect = jest.fn().mockReturnThis();
    const mockSingle = jest.fn().mockResolvedValue({ data: mockProduct, error: null });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => ({
        update: mockUpdate,
        eq: mockEq,
        select: mockSelect,
        single: mockSingle,
      })),
    });

    const formData = new FormData();
    formData.append('name', 'Updated Product');
    formData.append('base_price', '150');

    // Act
    const result = await updateProductService('prod-123', formData);

    // Assert
    expect(result.success).toBe(true);
    expect(result.data?.name).toBe('Updated Product');
    expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
  });
});