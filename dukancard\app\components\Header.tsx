"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Navbar,
  NavBody,
  NavItems,
  MobileNav,
  NavbarButton,
  MobileNavHeader,
  MobileNavToggle,
  MobileNavMenu,
} from "@/components/ui/resizable-navbar";
import { ThemeToggle } from "@/app/components/ThemeToggle";
import { createClient } from "@/utils/supabase/client";
import type { User } from "@supabase/supabase-js";
import { Grid3X3, Search, Store, Users, ArrowRight } from "lucide-react";
// import { Badge } from "@/components/ui/badge";

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [userType, setUserType] = useState<"customer" | "business" | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUserAndProfile = async () => {
      const supabase = createClient();
      setLoading(true);

      const {
        data: { user: currentUser },
      } = await supabase.auth.getUser();
      setUser(currentUser);

      if (currentUser) {
        const [customerRes, businessRes] = await Promise.all([
          supabase
            .from("customer_profiles")
            .select("id")
            .eq("id", currentUser.id)
            .maybeSingle(),
          supabase
            .from("business_profiles")
            .select("id")
            .eq("id", currentUser.id)
            .maybeSingle(),
        ]);

        if (customerRes.data) {
          setUserType("customer");
        } else if (businessRes.data) {
          setUserType("business");
        } else {
          setUserType(null);
        }
      } else {
        setUserType(null);
      }
      setLoading(false);
    };

    fetchUserAndProfile();
  }, []);

  const getDashboardPath = () => {
    if (userType === "business") return "/dashboard/business";
    if (userType === "customer") return "/dashboard/customer";
    return "/dashboard/customer";
  };

  const navItems = [
    { name: "Categories", link: "/categories", icon: Grid3X3 },
    { name: "Discover", link: "/discover", icon: Search },
    { name: "Free Listing", link: "/login", badge: "Business", icon: Store },
    {
      name: user ? "Feed" : "Community",
      link: user ? getDashboardPath() : "/login",
      icon: Users,
    },
  ];

  return (
    <div className="relative w-full">
      <Navbar className="fixed top-0 left-0 right-0 z-50 bg-background/80 dark:bg-background/90 backdrop-blur-lg border-b border-border/80 dark:border-border">
        {/* Desktop Navigation */}
        <NavBody className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
          <Link href={user ? "/?view=home" : "/"} className="flex items-center shrink-0 z-[100]">
            <div className="flex flex-col">
              <span className="font-bold text-xl text-[var(--brand-gold)]">
                Dukan<span className="text-foreground">card</span>
              </span>
              <span className="text-xs text-[var(--brand-gold)]/80 -mt-1">
                Your Neighborhood, Digitally Connected
              </span>
            </div>
          </Link>
          <NavItems
            items={navItems}
            className="flex flex-1 flex-row items-center justify-center space-x-2 text-sm font-medium text-muted-foreground hover:text-[var(--brand-gold)]"
          />
          <div className="flex items-center gap-4 z-[70]">
            <ThemeToggle />
            {!loading ? (
              user ? (
                <NavbarButton
                  variant="primary"
                  as="button"
                  onClick={() => (window.location.href = getDashboardPath())}
                  className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-full px-6 font-medium shadow hover:shadow-md"
                >
                  Dashboard
                </NavbarButton>
              ) : (
                <NavbarButton
                  variant="primary"
                  as="button"
                  onClick={() => (window.location.href = "/login")}
                  className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-full px-6 font-medium shadow hover:shadow-md"
                >
                  Sign In
                </NavbarButton>
              )
            ) : (
              <div className="h-10 w-24 bg-muted rounded-full animate-pulse" />
            )}
          </div>
        </NavBody>

        {/* Mobile Navigation */}
        <MobileNav>
          <MobileNavHeader>
            <Link href={user ? "/?view=home" : "/"} className="flex items-center shrink-0 z-[100]">
              <div className="flex flex-col">
                <span className="font-bold text-xl text-[var(--brand-gold)]">
                  Dukan<span className="text-foreground">card</span>
                </span>
                <span className="text-xs text-[var(--brand-gold)]/80 -mt-1">
                  Your Neighborhood, Digitally Connected
                </span>
              </div>
            </Link>
            <MobileNavToggle
              isOpen={isMobileMenuOpen}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            />
          </MobileNavHeader>

          <MobileNavMenu
            isOpen={isMobileMenuOpen}
            onClose={() => setIsMobileMenuOpen(false)}
            className="bg-background/95 backdrop-blur-md border-t border-border"
          >
            <div className="w-full space-y-2">
              {navItems.map((item, idx) => {
                const IconComponent = item.icon;
                return (
                  <Link
                    key={`mobile-link-${idx}`}
                    href={item.link}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="group flex items-center justify-between w-full p-4 rounded-xl bg-white/50 dark:bg-neutral-800/50 hover:bg-white/80 dark:hover:bg-neutral-800/80 border border-neutral-200/50 dark:border-neutral-700/50 transition-all duration-200 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]"
                  >
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] group-hover:bg-[var(--brand-gold)]/20 transition-colors">
                        <IconComponent className="h-5 w-5" />
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium text-foreground group-hover:text-[var(--brand-gold)] transition-colors">
                          {item.name}
                        </span>
                        {item.badge && (
                          <span className="text-xs text-[var(--brand-gold)] font-medium">
                            {item.badge}
                          </span>
                        )}
                      </div>
                    </div>
                    <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-[var(--brand-gold)] group-hover:translate-x-1 transition-all" />
                  </Link>
                );
              })}
            </div>
            <div className="flex w-full flex-col gap-4 px-0">
              <ThemeToggle />
              {!loading ? (
                user ? (
                  <NavbarButton
                    variant="primary"
                    as="button"
                    className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-xl w-full mx-4 shadow-md hover:shadow-lg transition-all"
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      window.location.href = getDashboardPath();
                    }}
                  >
                    Dashboard
                  </NavbarButton>
                ) : (
                  <NavbarButton
                    variant="primary"
                    as="button"
                    className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-xl w-full mx-4 shadow-md hover:shadow-lg transition-all"
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      window.location.href = "/login";
                    }}
                  >
                    Sign In
                  </NavbarButton>
                )
              ) : (
                <div className="h-10 w-full bg-muted rounded-xl animate-pulse mx-4" />
              )}
            </div>
          </MobileNavMenu>
        </MobileNav>
      </Navbar>
    </div>
  );
};

export default Header;
