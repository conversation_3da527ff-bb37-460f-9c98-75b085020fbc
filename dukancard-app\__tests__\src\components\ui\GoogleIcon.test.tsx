import React from 'react';
import { render } from '@testing-library/react-native';
import { GoogleIcon } from '@/src/components/ui/GoogleIcon';

describe('<GoogleIcon />', () => {
  it('renders correctly with default size', () => {
    const { toJSON, getByTestId } = render(<GoogleIcon />);
    const svgElement = getByTestId('google-icon-svg');
    expect(svgElement.props.width).toBe(20);
    expect(svgElement.props.height).toBe(20);
    expect(toJSON()).toMatchSnapshot();
  });

  it('renders correctly with a custom size', () => {
    const { toJSON, getByTestId } = render(<GoogleIcon size={40} />);
    const svgElement = getByTestId('google-icon-svg');
    expect(svgElement.props.width).toBe(40);
    expect(svgElement.props.height).toBe(40);
    expect(toJSON()).toMatchSnapshot();
  });

  it('renders with a size of 0', () => {
    const { getByTestId } = render(<GoogleIcon size={0} />);
    const svgElement = getByTestId('google-icon-svg');
    expect(svgElement.props.width).toBe(0);
    expect(svgElement.props.height).toBe(0);
  });
});