"use client";

import { motion } from "framer-motion";
import { Search, X, ArrowUpDown } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useProducts } from "../../context/ProductsContext";
import { ProductSortBy } from "../../actions";

export default function ProductFilters() {
  const {
    searchTerm,
    setSearchTerm,
    sortBy,
    setSortBy,
    isLoading,
    isPending
  } = useProducts();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      {/* Full width filters container */}
      <div className="flex flex-col sm:flex-row gap-4 p-6 rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm">

        {/* Search Section - Takes most of the width */}
        <div className="flex-1 space-y-3">
          <Label
            htmlFor="search-products"
            className="text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2"
          >
            <Search className="h-4 w-4 text-primary" />
            Search Products
          </Label>

          <div className="relative group">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400 dark:text-neutral-500 transition-colors group-focus-within:text-primary" />

            <Input
              id="search-products"
              type="text"
              placeholder="Search by name, description, or SKU..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-10 h-14 rounded-xl border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 placeholder:text-neutral-400"
              disabled={isLoading}
            />

            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-all duration-200"
                onClick={() => setSearchTerm("")}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Sort Section - Fixed width on the right */}
        <div className="w-full sm:w-80 space-y-3">
          <Label
            htmlFor="sort-by"
            className="text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2"
          >
            <ArrowUpDown className="h-4 w-4 text-primary" />
            Sort By
          </Label>

          <Select
            value={sortBy}
            onValueChange={(value) => setSortBy(value as ProductSortBy)}
            disabled={isLoading || isPending}
          >
            <SelectTrigger
              id="sort-by"
              className="w-full h-14 rounded-xl border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
            >
              <SelectValue placeholder="Choose sorting..." />
            </SelectTrigger>
            <SelectContent className="border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-xl bg-white dark:bg-neutral-900 p-2">
              <SelectItem value="created_desc" className="text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3">
                Newest First
              </SelectItem>
              <SelectItem value="created_asc" className="text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3">
                Oldest First
              </SelectItem>
              <SelectItem value="name_asc" className="text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3">
                Name (A-Z)
              </SelectItem>
              <SelectItem value="name_desc" className="text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3">
                Name (Z-A)
              </SelectItem>
              <SelectItem value="price_asc" className="text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3">
                Price (Low to High)
              </SelectItem>
              <SelectItem value="price_desc" className="text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3">
                Price (High to Low)
              </SelectItem>
              <SelectItem value="available_first" className="text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3">
                Available First
              </SelectItem>
              <SelectItem value="unavailable_first" className="text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3">
                Unavailable First
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </motion.div>
  );
}
