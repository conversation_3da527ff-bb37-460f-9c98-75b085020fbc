import { Suspense } from "react";
import LandingPageClient from "./LandingPageClient"; // Import the client component
import { Metadata } from "next";
import { redirect } from "next/navigation";
import { getHomepageUserInfo } from "./actions/getHomepageBusinessCard";

export async function generateMetadata(): Promise<Metadata> {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const siteTitle = "Dukancard - Discover Local Businesses, Products & Services Near You";
  const siteDescription =
    "Find local businesses, products, and services in your area with Dukancard. Search by pincode or city to discover nearby shops, compare offerings, and connect with local merchants across India.";
  const ogImage = `${siteUrl}/opengraph-image.png`; // Assuming this exists

  return {
    title: {
      absolute: siteTitle, // Use absolute to override the template from root layout
    },
    description: siteDescription,
    alternates: {
      canonical: "/", // Canonical path for the homepage relative to metadataBase
    },
    // Explicitly define OG/Twitter for clarity, even if similar to root
    openGraph: {
      title: siteTitle,
      description: siteDescription,
      url: siteUrl, // URL of the homepage
      siteName: "Dukancard",
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "Dukancard - Local Business Discovery Platform",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: siteTitle,
      description: siteDescription,
      images: [ogImage], // Use 'images' array
    },
    // Add Organization Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Organization",
        name: "Dukancard",
        url: siteUrl,
        logo: `${siteUrl}/logo.png`, // TODO: Replace with actual logo URL if available
        description: siteDescription,
        // Add contact points, address, etc. if available and relevant
        // contactPoint: {
        //   "@type": "ContactPoint",
        //   telephone: "+91-XXX-XXXXXXX", // Add phone if available
        //   contactType: "Customer Service"
        // },
        // sameAs: [ // Add social media links
        //   "https://www.facebook.com/yourpage",
        //   "https://www.twitter.com/yourhandle",
        // ]
      }),
    },
  };
}

// Server Component - handles authentication and redirection server-side
export default async function Home({
  searchParams,
}: {
  searchParams: Promise<{ view?: string }>;
}) {
  const { view } = await searchParams;

  // Check if user wants to explicitly view the homepage
  const viewHome = view === "home";

  // Only redirect if user is not explicitly viewing homepage
  if (!viewHome) {
    const userInfo = await getHomepageUserInfo();

    if (userInfo.isAuthenticated && userInfo.userType) {
      // Redirect authenticated users to their respective dashboard
      if (userInfo.userType === "business") {
        redirect("/dashboard/business");
      } else if (userInfo.userType === "customer") {
        redirect("/dashboard/customer");
      }
    }
  }

  // Render the client component for homepage display
  // Wrap LandingPageClient with Suspense as it might use useSearchParams
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-pulse w-16 h-16 rounded-full bg-[var(--brand-gold)]/20"></div>
        </div>
      }
    >
      <LandingPageClient />
    </Suspense>
  );
}
