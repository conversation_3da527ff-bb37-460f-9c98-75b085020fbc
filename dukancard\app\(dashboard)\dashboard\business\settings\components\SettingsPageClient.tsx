"use client";

import { motion } from "framer-motion";
import { Settings } from "lucide-react";
import LinkEmailSection from "./LinkEmailSection";
import LinkPhoneSection from "./LinkPhoneSection";
import PasswordUpdateSection from "./PasswordUpdateSection";
import CardEditorLinkSection from "./CardEditorLinkSection";
import AccountDeletionSection from "./AccountDeletionSection";

interface SettingsPageClientProps {
  currentEmail: string | undefined;
  currentPhone: string | null | undefined;
  registrationType: 'google' | 'email' | 'phone';
}

export default function SettingsPageClient({
  currentEmail,
  currentPhone,
  registrationType,
}: SettingsPageClientProps) {
  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Settings Header - Full Width */}
      <motion.div variants={itemVariants}>
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
          <div className="p-3 rounded-xl bg-muted hidden sm:block">
            <Settings className="w-6 h-6 text-foreground" />
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-foreground">
              Account Settings
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage your account preferences and security settings
            </p>
          </div>
        </div>
      </motion.div>

      {/* Settings Sections - Full Width */}
      <div className="space-y-8">
        {/* Link Email - Show for all users with different behaviors */}
        <motion.div variants={itemVariants}>
          <LinkEmailSection
            currentEmail={currentEmail}
            currentPhone={currentPhone}
            registrationType={registrationType}
          />
        </motion.div>

        {/* Link Phone - Show for all users with different behaviors */}
        <motion.div variants={itemVariants}>
          <LinkPhoneSection
            currentEmail={currentEmail}
            currentPhone={currentPhone}
            registrationType={registrationType}
          />
        </motion.div>

        {/* Password Management - Not for Google users */}
        {registrationType !== 'google' && (
          <motion.div variants={itemVariants}>
            <PasswordUpdateSection
              registrationType={registrationType}
            />
          </motion.div>
        )}

        <motion.div variants={itemVariants}>
          <CardEditorLinkSection />
        </motion.div>

        <motion.div variants={itemVariants}>
          <AccountDeletionSection />
        </motion.div>
      </div>
    </motion.div>
  );
}
