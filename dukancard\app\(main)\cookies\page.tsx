import { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { Loader2 } from "lucide-react";
import ModernCookiePolicyClient from "./ModernCookiePolicyClient";

export const metadata: Metadata = {
  title: "Cookie Policy",
  description: "Learn about how Dukancard uses cookies and similar technologies to enhance your experience on our digital business card platform.",
};

export default function CookiePolicyPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense
        fallback={
          <div className="min-h-screen flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-[var(--brand-gold)]" />
          </div>
        }
      >
        <ModernCookiePolicyClient />
      </Suspense>
    </div>
  );
}
