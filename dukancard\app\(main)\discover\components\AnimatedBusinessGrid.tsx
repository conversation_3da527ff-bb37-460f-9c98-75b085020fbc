"use client";

import { useRef } from "react";
import { motion } from "framer-motion";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import AnimatedBusinessCard from "./AnimatedBusinessCard";

interface AnimatedBusinessGridProps {
  businesses: BusinessCardData[];
  isAuthenticated?: boolean;
}

export default function AnimatedBusinessGrid({
  businesses,
}: AnimatedBusinessGridProps) {
  const gridRef = useRef<HTMLDivElement>(null);

  return (
    <motion.div
      ref={gridRef}
      className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {businesses.map((business, index) => (
        <AnimatedBusinessCard
          key={business.id}
          business={business}
          index={index}
        />
      ))}
    </motion.div>
  );
}
