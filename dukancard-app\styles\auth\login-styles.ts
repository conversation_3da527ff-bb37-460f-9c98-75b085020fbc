import { useTheme } from '@/src/hooks/useTheme';
import { StyleSheet } from 'react-native';
import { EdgeInsets } from 'react-native-safe-area-context';
import { responsiveFontSize } from '@/lib/theme/colors';

export const createLoginStyles = (theme: ReturnType<typeof useTheme>, insets?: EdgeInsets) => {

  return StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
      paddingTop: 0, // Remove safe area padding to allow full screen layout
      paddingBottom: 0,
    },
    // New two-row layout styles
    twoRowContainer: {
      flex: 1,
      flexDirection: 'column',
    },
    upperRow: {
      height: '50%', // Exactly 50% of screen height
      position: 'relative',
      overflow: 'hidden',
    },
    backgroundImage: {
      width: '100%',
      height: `${100 + ((insets?.top || 0) / 8)}%`, // Extend height to cover notch
      position: 'absolute',
      top: -(insets?.top || 0), // Extend into notch area
      left: 0,
      right: 0,
    },
    logoOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: (insets?.top || 0) + theme.spacing.md, // Account for notch/status bar
    },
    lowerRow: {
      height: '50%', // Exactly 50% of screen height
      backgroundColor: theme.colors.background,
      paddingHorizontal: theme.spacing.lg,
      paddingTop: theme.spacing.lg,
      paddingBottom: Math.max(theme.spacing.xl, (insets?.bottom || 0) + theme.spacing.lg),
      justifyContent: 'space-between',
    },
    // Legacy styles for backward compatibility
    mainContainer: {
      flex: 1,
      paddingHorizontal: theme.spacing.lg,
      justifyContent: 'space-between',
    },
    mainContentContainer: {
      flex: 1,
      paddingHorizontal: theme.spacing.lg,
      justifyContent: 'space-between',
    },
    logoWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    formAndFooterWrapper: {
      flex: 0,
      justifyContent: 'flex-end',
      marginBottom: theme.spacing.xl,
    },
    formContainer: {
      gap: theme.spacing.md,
      marginBottom: theme.spacing.lg,
    },
    dividerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: theme.spacing.md,
    },
    dividerLine: {
      flex: 1,
      height: 1,
      backgroundColor: theme.colors.border,
    },
    dividerText: {
      color: theme.colors.textSecondary,
      marginHorizontal: theme.spacing.sm,
      fontSize: theme.typography.fontSize.sm,
    },
    // Simplified OTP Design
    otpMainContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
      paddingHorizontal: theme.spacing.md,
      flexDirection: 'column',
      justifyContent: 'space-between',
    },
    otpFullScreenContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    otpScrollContent: {
      flexGrow: 1,
      paddingVertical: theme.spacing.xl,
      paddingHorizontal: theme.spacing.md,
      minHeight: '100%',
    },

    // OTP Header Section
    otpHeaderSection: {
      flex: 0,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: theme.spacing.lg,
    },
    otpIconContainer: {
      marginBottom: theme.spacing.lg,
    },
    otpIcon: {
      width: responsiveFontSize(64),
      height: responsiveFontSize(64),
      borderRadius: responsiveFontSize(32),
      backgroundColor: theme.colors.muted,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: theme.colors.textSecondary,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 4,
      elevation: 2,
    },
    otpIconText: {
      fontSize: responsiveFontSize(20),
    },
    otpMainTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.textPrimary,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    otpSubtitle: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.base,
    },
    otpEmailDisplay: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.primary,
      textAlign: 'center',
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      backgroundColor: theme.colors.primary + '10',
      borderRadius: theme.borderRadius.md,
      marginTop: theme.spacing.xs,
    },

    // OTP Input Section
    otpInputSection: {
      alignItems: 'center',
      width: '100%',
      marginBottom: theme.spacing.xl,
    },
    otpInputLabel: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.textPrimary,
      textAlign: 'center',
      marginBottom: theme.spacing.xl,
    },
    otpInputWrapper: {
      position: 'relative',
      width: '100%',
      alignItems: 'center',
    },
    otpLoadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: theme.colors.background + 'CC',
      alignItems: 'center',
      justifyContent: 'center',
    },

    // OTP Actions Section
    otpActionsSection: {
      alignItems: 'center',
      gap: theme.spacing.lg,
      width: '100%',
      paddingBottom: Math.max(theme.spacing.xl, (insets?.bottom || 0) + theme.spacing.lg),
    },
    otpResendButton: {
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.xl,
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.md,
      minWidth: 140,
    },
    otpResendButtonText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.primaryForeground,
      textAlign: 'center',
    },
    otpChangeEmailButton: {
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
    },
    otpChangeEmailText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    otpButtonDisabled: {
      opacity: 0.5,
      backgroundColor: theme.colors.muted,
    },
    otpButtonTextDisabled: {
      color: theme.colors.mutedForeground,
    },

    // Legacy OTP styles (keeping for compatibility)
    otpContainer: {
      gap: theme.spacing.xl,
    },
    otpInfoContainer: {
      alignItems: 'center',
      marginBottom: theme.spacing.xxxl,
      paddingHorizontal: theme.spacing.md,
    },
    otpInfoText: {
      fontSize: theme.typography.fontSize.base,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.base,
    },
    otpEmailText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.textPrimary,
      textAlign: 'center',
    },
    otpInputContainer: {
      marginBottom: theme.spacing.xxxl,
    },
    otpActionsContainer: {
      alignItems: 'center',
      gap: theme.spacing.lg,
    },
    otpActionButton: {
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.md,
    },
    otpActionButtonDisabled: {
      opacity: 0.5,
    },
    resendText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.primary,
      textAlign: 'center',
    },
    changeEmailText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    // Auth method toggle styles
    authToggleContainer: {
      flexDirection: 'row',
      backgroundColor: theme.colors.background,
      borderRadius: theme.borderRadius.xl, // Softer look
      padding: 6, // Increased padding
      marginBottom: theme.spacing.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    authToggleButton: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.lg, // Match container radius
      alignItems: 'center',
    },
    authToggleButtonActive: {
      backgroundColor: theme.colors.primary, // Use primary color for active background
      shadowColor: theme.colors.primary, // Keep a subtle shadow for depth
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 3,
    },
    authToggleText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.textSecondary,
    },
    authToggleTextActive: {
      color: theme.colors.primaryForeground, // Ensure contrasting text color
      fontWeight: theme.typography.fontWeight.semibold,
    },

    // Mobile input styles
    mobileInputContainer: {
      marginBottom: 0, // Remove bottom margin, let formContainer handle spacing
    },
    inputLabel: {
      fontSize: theme.typography.fontSize.base, // Match Input component label size
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.foreground,
      marginBottom: theme.spacing.sm,
      letterSpacing: 0.5,
    },
    mobileInputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 12, // Match Input component border radius
      backgroundColor: theme.colors.card, // Use theme-aware background
      height: 56, // Fixed height to match Input component
    },
    countryCodeContainer: {
      paddingHorizontal: theme.spacing.md,
      borderRightWidth: 1,
      borderRightColor: theme.colors.border,
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    countryCode: {
      fontSize: theme.typography.fontSize.base, // Match Input component font size
      fontWeight: '500', // Match Input component font weight
      color: theme.colors.textSecondary, // Use theme-aware text color
      letterSpacing: 0.5, // Match Input component letter spacing
    },
    mobileInput: {
      flex: 1,
      borderWidth: 0,
      backgroundColor: 'transparent',
      paddingHorizontal: theme.spacing.md,
      paddingVertical: 0, // Remove vertical padding to rely on wrapper height
      fontSize: theme.typography.fontSize.base, // Match Input component font size
      fontWeight: '500', // Match Input component font weight
      height: '100%', // Take full height of wrapper
      textAlignVertical: 'center', // Android specific vertical centering
      letterSpacing: 0.5, // Match Input component letter spacing
      color: theme.colors.textPrimary, // Use theme-aware text color
    },
    errorText: {
      fontSize: theme.typography.fontSize.sm,
      color: '#ef4444', // Red color for errors
      marginTop: theme.spacing.xs,
    },
    loadingContainer: {
      alignItems: 'center',
      gap: theme.spacing.md,
      paddingVertical: theme.spacing.xxl,
    },
    loadingText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    footerContainer: {
      paddingHorizontal: theme.spacing.md,
      marginTop: theme.spacing.xl, // Add spacing above the footer
    },
    footerLinksContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
      alignItems: 'center',
    },
    footerText: {
      fontSize: theme.typography.fontSize.xs,
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.xs,
      color: theme.colors.textMuted,
    },
    footerLinkText: {
      fontSize: theme.typography.fontSize.xs,
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.xs,
      color: theme.colors.primary,
      fontWeight: theme.typography.fontWeight.medium,
      textDecorationLine: 'underline',
    },
  });
};

// Dummy default export to satisfy Expo Router
export default function LoginStylesPlaceholder() {
  return null;
}
