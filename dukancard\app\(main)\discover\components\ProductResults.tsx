"use client";

import { useRef, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { motion } from "framer-motion";
import { NearbyProduct } from "../actions/types";
import ProductGrid from "./ProductGrid";
import { ProductSortOption, ProductFilterOption } from "../context/types";

interface ProductResultsProps {
  products: NearbyProduct[];
  totalCount: number;
  hasMore: boolean;
  isLoadingMore: boolean;
  onLoadMore: () => void;
  onSortChange: (_sortBy: ProductSortOption) => void;
  onFilterChange: (_filterBy: ProductFilterOption) => void;
  onSearch: (_searchTerm: string) => void;
  currentSortBy: ProductSortOption;
  currentFilterBy: ProductFilterOption;
  isLoading: boolean;
  initialSearchTerm?: string | null;
}

export default function ProductResults({
  products,
  hasMore,
  isLoadingMore,
  onLoadMore,
  onSortChange,
  onFilterChange,
  onSearch,
  currentSortBy,
  currentFilterBy,
  isLoading,
  initialSearchTerm,
}: ProductResultsProps) {
  const observerTarget = useRef<HTMLDivElement>(null);

  // Set up intersection observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          onLoadMore();
        }
      },
      { threshold: 0.1, rootMargin: "100px" }
    );

    const currentTarget = observerTarget.current;
    if (currentTarget) {
      observer.observe(currentTarget);
    }

    return () => {
      if (currentTarget) {
        observer.unobserve(currentTarget);
      }
    };
  }, [hasMore, isLoadingMore, onLoadMore]);

  // We'll always show the ProductGrid with filters, regardless of loading state or empty results
  return (
    <div>
      <ProductGrid
        products={products}
        onSortChange={onSortChange}
        onFilterChange={onFilterChange}
        onSearch={onSearch}
        currentSortBy={currentSortBy}
        currentFilterBy={currentFilterBy}
        isLoading={isLoading}
        initialSearchTerm={initialSearchTerm}
      />

      {/* Loading More Indicator - Only render when not loading */}
      {!isLoading && (
        /* Loading More Indicator - Only render when we have products and not loading */
        <div
          ref={observerTarget}
          className="flex justify-center items-center py-8"
        >
          {isLoadingMore ? (
            <motion.div
              className="flex flex-col items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <Loader2 className="h-6 w-6 animate-spin text-[var(--brand-gold)]" />
              <span className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">
                Loading more products...
              </span>
              <span className="text-xs text-neutral-500 dark:text-neutral-500 mt-1">
                Loading more items...
              </span>
            </motion.div>
          ) : hasMore ? (
            <div className="h-10 w-full bg-transparent" />
          ) : (
            <motion.div
              className="text-sm text-neutral-500 dark:text-neutral-400 py-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              {products.length > 0 ? "You've reached the end of the list" : ""}
            </motion.div>
          )}
        </div>
      )}
    </div>
  );
}
