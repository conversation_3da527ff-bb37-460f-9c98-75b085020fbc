"use client";

import { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  FileText,
  CreditCard,
  Calendar,
  CheckCircle,
  RefreshCw,
  Clock,
  AlertTriangle,
  Settings,
  Mail
} from "lucide-react";
import { Card } from "@/components/ui/card";
import PolicyHeroSection from "../components/policy/PolicyHeroSection";
import PolicySection from "../components/policy/PolicySection";
import PolicyNavigation from "../components/policy/PolicyNavigation";
import PolicyCTASection from "../components/policy/PolicyCTASection";
import SectionDivider from "../components/landing/SectionDivider";
import { siteConfig } from "@/lib/site-config";

// Navigation items for the policy
const navItems = [
  { id: "introduction", title: "Introduction" },
  { id: "subscription", title: "Subscription Cancellation" },
  { id: "refund-eligibility", title: "Refund Eligibility" },
  { id: "refund-process", title: "Refund Process" },
  { id: "processing-time", title: "Processing Time" },
  { id: "cancellation-effects", title: "Cancellation Effects" },
  { id: "non-refundable", title: "Non-Refundable Items" },
  { id: "policy-changes", title: "Policy Changes" },
  { id: "contact", title: "Contact Us" },
];

// Related links
const relatedLinks = [
  { title: "Terms of Service", href: "/terms" },
  { title: "Privacy Policy", href: "/privacy" },
  { title: "Cookie Policy", href: "/cookies" },
];

export default function ModernRefundPolicyClient() {
  const pageRef = useRef<HTMLDivElement>(null);

  // Removed scroll-based fade effect

  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div
      ref={pageRef}
      className="min-h-screen bg-white dark:bg-black"
    >
      <div className="relative">
        {/* Hero Section */}
        <PolicyHeroSection
          title="Cancellation and Refund Policy"
          lastUpdated="May 19, 2025"
          variant="purple"
        />

        <div className="container mx-auto px-4 max-w-4xl pb-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left column: Navigation (sticky on desktop) */}
            <div className="w-full lg:w-1/4 order-1">
              <div className="lg:sticky lg:top-24 self-start">
                <PolicyNavigation items={navItems} />
              </div>
            </div>

            {/* Right column: Content */}
            <div className="w-full lg:w-3/4 order-2">
              <Card className="p-6 md:p-8 border border-border shadow-sm mb-8">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="prose prose-neutral dark:prose-invert max-w-none"
                >
                  <p className="text-lg">
                    At Dukancard, we strive to ensure complete satisfaction with our digital business card services. This Cancellation and Refund Policy outlines the terms and conditions regarding subscription cancellations and refunds for our services.
                  </p>
                </motion.div>
              </Card>

              {/* Introduction Section */}
              <PolicySection
                id="introduction"
                title="1. Introduction"
                icon={<FileText className="h-6 w-6" />}
                delay={0}
              >
                <p>
                  At Dukancard, we strive to ensure complete satisfaction with our
                  digital business card services. This Cancellation and Refund Policy
                  outlines the terms and conditions regarding subscription cancellations
                  and refunds for our services.
                </p>
              </PolicySection>

              {/* Subscription Cancellation Section */}
              <PolicySection
                id="subscription"
                title="2. Subscription Cancellation"
                icon={<CreditCard className="h-6 w-6" />}
                delay={1}
              >
                <p>
                  You may cancel your subscription at any time through your Dukancard
                  dashboard. Here&apos;s what you need to know about cancellation:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>
                    <strong>Cancellation Process:</strong> Log in to your Dukancard
                    dashboard, navigate to the Business Plan page, and click on the
                    &quot;Cancel Subscription&quot; button. Your subscription will be cancelled through our Razorpay payment gateway.
                  </li>
                  <li>
                    <strong>End-of-Term Cancellation:</strong> When you cancel your subscription,
                    it will remain active until the end of your current billing cycle, and you
                    will not be charged for the next billing cycle.
                  </li>
                  <li>
                    <strong>No Partial Refunds:</strong> We do not provide partial
                    refunds for unused portions of your subscription.
                  </li>
                </ul>
              </PolicySection>

              {/* Refund Eligibility Section */}
              <PolicySection
                id="refund-eligibility"
                title="3. Refund Eligibility"
                icon={<CheckCircle className="h-6 w-6" />}
                delay={2}
              >
                <p>
                  Dukancard currently offers refunds only in specific circumstances:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>
                    <strong>Failed Payments:</strong> If you were charged for a subscription but the payment failed to process correctly.
                  </li>
                  <li>
                    <strong>Subscription Issues:</strong> If there were technical issues with your subscription that prevented you from accessing the service.
                  </li>
                  <li>
                    <strong>Double Charges:</strong> If you were charged twice for the same subscription period.
                  </li>
                  <li>
                    <strong>Contact Required:</strong> All refund requests must be submitted by contacting our support team directly. We review each request on a case-by-case basis.
                  </li>
                  <li>
                    <strong>Plan Downgrade:</strong> Upon refund approval, your account will be
                    downgraded to the Free plan. You will still be able to maintain your online presence
                    with the features available in the Free plan.
                  </li>
                </ul>
              </PolicySection>

              {/* Section Divider */}
              <SectionDivider variant="purple" className="my-12" />

              {/* Refund Process Section */}
              <PolicySection
                id="refund-process"
                title="4. Refund Process"
                icon={<RefreshCw className="h-6 w-6" />}
                delay={3}
              >
                <p>
                  To request a refund for eligible circumstances, follow these steps:
                </p>
                <ol className="list-decimal pl-6 mb-6">
                  <li>Contact our support team via email at {siteConfig.contact.email}.</li>
                  <li>
                    Include the following information in your request:
                    <ul className="list-disc pl-6 mt-2">
                      <li>Your account email address</li>
                      <li>Business name</li>
                      <li>Date of payment</li>
                      <li>Reason for refund request</li>
                      <li>Any relevant screenshots or documentation</li>
                    </ul>
                  </li>
                  <li>Our team will review your request and respond within 2-3 business days.</li>
                  <li>If approved, the refund will be processed to your original payment method.</li>
                </ol>
              </PolicySection>

              {/* Processing Time Section */}
              <PolicySection
                id="processing-time"
                title="5. Processing Time"
                icon={<Clock className="h-6 w-6" />}
                delay={4}
              >
                <p>
                  All payments are processed through Razorpay, our trusted payment gateway partner. Refund processing times vary depending on your payment method:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>
                    <strong>Credit/Debit Cards:</strong> 5-7 business days, depending
                    on your card issuer.
                  </li>
                  <li>
                    <strong>UPI:</strong> 1-3 business days.
                  </li>
                  <li>
                    <strong>Net Banking:</strong> 3-5 business days.
                  </li>
                  <li>
                    <strong>Wallet:</strong> 1-2 business days.
                  </li>
                </ul>
                <p>
                  Please note that while Dukancard processes refunds promptly through Razorpay, the
                  actual time for the refund to appear in your account depends on your
                  payment provider&apos;s processing times.
                </p>
              </PolicySection>

              {/* Cancellation Effects Section */}
              <PolicySection
                id="cancellation-effects"
                title="6. Cancellation Effects"
                icon={<Settings className="h-6 w-6" />}
                delay={5}
              >
                <p>
                  When you cancel your subscription or receive a refund, the following
                  changes will take effect:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>
                    <strong>Approved Refund:</strong> If your refund request is approved, your account will
                    be downgraded to the Free plan immediately. You will still be able to maintain your online presence
                    with the features available in the Free plan.
                  </li>
                  <li>
                    <strong>End-of-Term Cancellation:</strong> You will retain access to
                    your current plan features until the end of your billing cycle, at
                    which point your account will be downgraded to the Free plan.
                  </li>
                  <li>
                    <strong>Data Retention:</strong> Your business profile and data will
                    be retained according to our Privacy Policy, but certain premium
                    features will no longer be accessible.
                  </li>
                  <li>
                    <strong>Reactivation:</strong> You can reactivate your subscription
                    at any time by purchasing a new plan.
                  </li>
                </ul>
              </PolicySection>

              {/* Non-Refundable Items Section */}
              <PolicySection
                id="non-refundable"
                title="7. Non-Refundable Items"
                icon={<AlertTriangle className="h-6 w-6" />}
                delay={6}
              >
                <p>The following are generally not eligible for refunds:</p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Regular subscription cancellations (cancellations without technical issues).</li>
                  <li>Subscriptions cancelled due to lack of usage or change of mind.</li>
                  <li>Accounts that have violated our Terms of Service.</li>
                  <li>Requests made without proper documentation or evidence of payment issues.</li>
                </ul>
              </PolicySection>

              {/* Policy Changes Section */}
              <PolicySection
                id="policy-changes"
                title="8. Changes to This Policy"
                icon={<Calendar className="h-6 w-6" />}
                delay={7}
              >
                <p>
                  We may update our Cancellation and Refund Policy from time to time. We
                  will notify you of any changes by posting the new policy on this page
                  and updating the &quot;Last Updated&quot; date at the top of this
                  policy.
                </p>
                <p>
                  You are advised to review this policy periodically for any changes.
                  Changes to this policy are effective when they are posted on this
                  page.
                </p>
              </PolicySection>

              {/* Contact Us Section */}
              <PolicySection
                id="contact"
                title="9. Contact Us"
                icon={<Mail className="h-6 w-6" />}
                delay={8}
              >
                <p>
                  If you have any questions about our Cancellation and Refund Policy,
                  please contact us:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>By email: {siteConfig.contact.email}</li>
                  <li>By phone: {siteConfig.contact.phone}</li>
                  <li>By mail: {siteConfig.contact.address.full}</li>
                </ul>
              </PolicySection>
            </div>
          </div>
        </div>

        {/* Section Divider */}
        <SectionDivider variant="gold" className="my-8" />

        {/* CTA Section */}
        <PolicyCTASection relatedLinks={relatedLinks} />
      </div>
    </div>
  );
}
