import React from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { CategoryCombobox } from "@/components/ui/category-combobox";
import { 
  User, 
  Briefcase, 
  Phone, 
  Link, 
  Loader2, 
  AlertCircle, 
  CheckCircle2, 
  Check 
} from "lucide-react";
import { StepComponentProps } from "../../types/onboarding";

export function CardInformationStep({
  form,
  isSubmitting,
  slugAvailable,
  isCheckingSlug,
  setSlugToCheck,
  setSlugAvailable,
  user
}: StepComponentProps) {
  // Check if user is mobile registered (has phone in auth.users)
  const isMobileUser = Boolean(user?.phone && user?.phone.trim() !== '');
  return (
    <>
      <FormField
        control={form.control}
        name="memberName"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <User className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Your Name
            </FormLabel>
            <FormControl>
              <div className="relative">
                <Input
                  placeholder="e.g., John Doe"
                  {...field}
                  className="bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg"
                  disabled={isSubmitting}
                />
                <User className="absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400" />
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="title"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <Briefcase className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Your Title/Designation
            </FormLabel>
            <FormControl>
              <div className="relative">
                <Input
                  placeholder="e.g., Founder, Manager"
                  {...field}
                  className="bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg"
                  disabled={isSubmitting}
                />
                <Briefcase className="absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400" />
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="phone"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <Phone className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Primary Phone Number
              {isMobileUser && (
                <span className="text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full">
                  Mobile Account
                </span>
              )}
            </FormLabel>
            <FormControl>
              <div className="relative">
                <Input
                  placeholder="e.g., **********"
                  type="tel"
                  pattern="[0-9]*"
                  inputMode="numeric"
                  {...field}
                  onChange={(e) => {
                    // Remove any +91 prefix if user enters it
                    let value = e.target.value.replace(/^\+91/, '');
                    // Only allow numeric input
                    value = value.replace(/\D/g, '');
                    // Limit to 10 digits
                    if (value.length > 10) {
                      value = value.slice(0, 10);
                    }
                    field.onChange(value);
                  }}
                  onKeyDown={(e) => {
                    // Prevent non-numeric input (except for control keys like backspace, arrows, etc.)
                    const isNumeric = /^[0-9]$/.test(e.key);
                    const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);

                    if (!isNumeric && !isControl) {
                      e.preventDefault();
                    }
                  }}
                  className="bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg"
                  disabled={isSubmitting}
                />
                <Phone className="absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400" />
              </div>
            </FormControl>

            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="businessCategory"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <Briefcase className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Business Category
            </FormLabel>
            <FormControl>
              <CategoryCombobox
                value={field.value}
                onChange={field.onChange}
                placeholder="Select a business category"
                disabled={isSubmitting}
                className="w-full bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 text-foreground transition-all rounded-lg"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="businessSlug"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <Link className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Desired Card URL
            </FormLabel>
            <div className="flex flex-col space-y-2">
              {/* Custom URL input with domain prefix outside the input */}
              <div className="flex flex-col space-y-2">
                <div className="flex items-center rounded-lg overflow-hidden">
                  {/* Domain prefix as a separate element */}
                  <div className="bg-muted dark:bg-neutral-800 px-3 py-3 flex items-center border-y border-l border-border dark:border-neutral-700 rounded-l-lg">
                    <Link className="w-4 h-4 text-muted-foreground dark:text-neutral-400 mr-1" />
                    <span className="text-muted-foreground dark:text-neutral-400 text-sm whitespace-nowrap">dukancard.in/</span>
                  </div>

                  {/* The actual input field with more space */}
                  <div className="relative flex-1">
                    <FormControl>
                      <Input
                        placeholder="your-business-name"
                        {...field}
                        onChange={(e) => {
                          // Normalize the slug: lowercase, replace spaces with hyphens, remove non-alphanumeric chars
                          const slug = e.target.value
                            .toLowerCase()
                            .replace(/\s+/g, "-")
                            .replace(/[^a-z0-9-]/g, "");

                          // Always update the form field first
                          field.onChange(slug);

                          // Only trigger a check if the slug is valid and at least 3 chars
                          if (slug.length >= 3 && /^[a-z0-9-]+$/.test(slug) && setSlugToCheck && setSlugAvailable) {
                            console.log("Client: Setting slug to check:", slug);
                            setSlugToCheck(slug);
                            setSlugAvailable(null);
                          } else if (setSlugAvailable) {
                            // Reset availability state for invalid slugs
                            setSlugAvailable(null);
                          }
                        }}
                        className="bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 h-12 text-foreground transition-all rounded-none rounded-r-lg border-l-0 pl-3 pr-10"
                        disabled={isSubmitting}
                      />
                    </FormControl>

                    {/* Status indicator positioned on the right */}
                    <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center w-5 h-5">
                      {isCheckingSlug && <Loader2 className="w-5 h-5 text-muted-foreground animate-spin" />}
                      {!isCheckingSlug && slugAvailable === true && <CheckCircle2 className="w-5 h-5 text-green-500" />}
                      {!isCheckingSlug && slugAvailable === false && <AlertCircle className="w-5 h-5 text-red-500" />}
                    </div>
                  </div>
                </div>

                {/* Status message below the input */}
                <div className="flex items-center h-5 px-1">
                  {isCheckingSlug && <p className="text-xs text-muted-foreground">Checking availability...</p>}
                  {!isCheckingSlug && slugAvailable === true && !form.formState.errors.businessSlug && (
                    <p className="text-xs text-green-500 flex items-center gap-1">
                      <Check className="w-3 h-3" /> URL is available!
                    </p>
                  )}
                </div>
              </div>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
}
