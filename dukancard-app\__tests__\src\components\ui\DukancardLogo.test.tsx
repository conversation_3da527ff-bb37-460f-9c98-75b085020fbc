import React from 'react';
import { render } from '@testing-library/react-native';
import { DukancardLogo } from '@/src/components/ui/DukancardLogo';

// Mock the useColorScheme hook
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

describe('<DukancardLogo />', () => {
  it('renders correctly with default props', () => {
    const { getByText, toJSON } = render(<DukancardLogo />);
    expect(getByText('Dukan')).toBeTruthy();
    expect(getByText('card')).toBeTruthy();
    expect(getByText('Connect • Discover • Grow')).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it('does not render the text when showText is false', () => {
    const { queryByText } = render(<DukancardLogo showText={false} />);
    expect(queryByText('Dukan')).toBeNull();
    expect(queryByText('card')).toBeNull();
  });

  it('does not render the tagline when showTagline is false', () => {
    const { queryByText } = render(<DukancardLogo showTagline={false} />);
    expect(queryByText('Connect • Discover • Grow')).toBeNull();
  });

  it('renders correctly with showText false and showTagline true, tagline should not be visible', () => {
    const { queryByText, toJSON } = render(
      <DukancardLogo showText={false} showTagline={true} />
    );
    expect(queryByText('Dukan')).toBeNull();
    expect(queryByText('card')).toBeNull();
    expect(queryByText('Connect • Discover • Grow')).toBeNull();
    expect(toJSON()).toMatchSnapshot();
  });

  describe('logo sizes', () => {
    const sizes: ('small' | 'medium' | 'large' | 'auth' | 'hero')[] = [
      'small',
      'medium',
      'large',
      'auth',
      'hero',
    ];

    sizes.forEach((size) => {
      it(`renders correctly with size: ${size}`, () => {
        const { toJSON } = render(<DukancardLogo size={size} />);
        expect(toJSON()).toMatchSnapshot();
      });
    });
  });
});