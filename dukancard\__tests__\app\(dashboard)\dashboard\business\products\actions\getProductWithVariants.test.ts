import { getProductWithVariants } from '@/app/(dashboard)/dashboard/business/products/actions/getProductWithVariants';
import { createClient } from '@/utils/supabase/server';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('getProductWithVariants', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return an error if the user is not authenticated', async () => {
    // Arrange
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
      },
    });

    // Act
    const result = await getProductWithVariants('prod-123');

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('User not authenticated.');
  });

  it('should fetch a product with variants successfully', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockRpc = jest.fn().mockResolvedValue({ data: [{ product_id: 'prod-123', product_name: 'Test' }], error: null });
    const mockSingle = jest.fn().mockResolvedValue({ data: { business_id: 'user-123' }, error: null });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      rpc: mockRpc,
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: mockSingle,
    });

    // Act
    const result = await getProductWithVariants('prod-123');

    // Assert
    expect(result.success).toBe(true);
    expect(result.data?.id).toBe('prod-123');
    expect(mockRpc).toHaveBeenCalledWith('get_product_with_variants', { product_uuid: 'prod-123' });
  });
});