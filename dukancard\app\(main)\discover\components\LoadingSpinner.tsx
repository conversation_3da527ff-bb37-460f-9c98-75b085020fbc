"use client";

import { Loader2 } from "lucide-react";
import styles from "./LoadingSpinner.module.css";

export default function LoadingSpinner() {
  return (
    <div className="relative">
      <div
        className={`absolute inset-0 rounded-full bg-[var(--brand-gold)]/20 ${styles.pulseRing1}`}
      />
      <div
        className={`absolute inset-0 rounded-full bg-[var(--brand-gold)]/10 ${styles.pulseRing2}`}
      />
      <Loader2 className="relative z-10 h-6 w-6 animate-spin text-[var(--brand-gold)]" />
    </div>
  );
}
