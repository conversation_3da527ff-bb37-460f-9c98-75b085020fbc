import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { BusinessProfile } from "./types";

/**
 * Validates the subscription ID from the URL parameters
 */
export async function validateSubscriptionId(params: Promise<{ id: string }>) {
  const { id: subscriptionId } = await params;
  if (!subscriptionId) {
    return {
      valid: false,
      response: NextResponse.json(
        { success: false, error: "Subscription ID is required" },
        { status: 400 }
      ),
    };
  }
  return { valid: true, subscriptionId: subscriptionId as string };
}

/**
 * Validates the user authentication and returns the user
 */
export async function validateUser() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      valid: false,
      response: NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      ),
    };
  }
  return { valid: true, user, supabase };
}

/**
 * Validates that the user owns the subscription
 */
export async function validateUserOwnsSubscription(
  supabase: SupabaseClient,
  userId: string,
  subscriptionId: string
) {
  // First check if the user has an active business profile
  const { data: profile, error: profileError } = await supabase
    .from("business_profiles")
    .select("id, has_active_subscription")
    .eq("id", userId)
    .single();

  if (profileError || !profile) {
    console.error("Error fetching business profile:", profileError);
    return {
      valid: false,
      response: NextResponse.json(
        { success: false, error: "Could not fetch business profile" },
        { status: 500 }
      ),
    };
  }

  // Check if the user has an active subscription
  if (!profile.has_active_subscription) {
    return {
      valid: false,
      response: NextResponse.json(
        { success: false, error: "User does not have an active subscription" },
        { status: 403 }
      ),
    };
  }

  // Now check if the subscription ID matches in the payment_subscriptions table
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("id, razorpay_subscription_id")
    .eq("business_profile_id", userId)
    .eq("razorpay_subscription_id", subscriptionId)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription:", subscriptionError);
    return {
      valid: false,
      response: NextResponse.json(
        { success: false, error: "Could not fetch subscription details" },
        { status: 500 }
      ),
    };
  }

  // Verify that the subscription ID matches
  if (!subscription) {
    return {
      valid: false,
      response: NextResponse.json(
        { success: false, error: "Subscription ID does not match" },
        { status: 403 }
      ),
    };
  }

  return { valid: true, profile: profile as BusinessProfile, subscription };
}

/**
 * Validates the refund speed from the request body
 */
export async function validateRefundSpeed(request: NextRequest) {
  const body = await request.json();
  const speed = body.speed === "optimum" ? "optimum" : "normal";
  return { speed };
}
