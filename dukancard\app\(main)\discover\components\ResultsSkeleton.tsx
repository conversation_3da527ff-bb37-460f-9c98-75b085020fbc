import { Skeleton } from "@/components/ui/skeleton";

export default function ResultsSkeleton() {
  return (
    <div className="space-y-8">
      {/* Search Form Skeleton */}
      <div className="relative p-6 md:p-8 rounded-xl bg-white dark:bg-neutral-900 shadow-lg dark:shadow-neutral-900/20 border border-neutral-100 dark:border-neutral-800 backdrop-blur-sm max-w-4xl mx-auto overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-neutral-100/50 dark:bg-neutral-800/20 blur-2xl"></div>
          <div className="absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-neutral-100/50 dark:bg-neutral-800/20 blur-2xl"></div>
        </div>

        <div className="relative z-10">
          <div className="flex justify-center mb-6">
            <Skeleton className="h-8 w-64" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-5 mb-6">
            <div className="space-y-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-11 w-full rounded-lg" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-11 w-full rounded-lg" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-5 w-36" />
              <Skeleton className="h-11 w-full rounded-lg" />
            </div>
          </div>

          <div className="flex justify-center">
            <Skeleton className="h-11 w-36 rounded-lg" />
          </div>
        </div>
      </div>

      {/* View Toggle Skeleton */}
      <div className="flex justify-center mb-8">
        <div className="bg-white dark:bg-neutral-900 p-1.5 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 flex gap-1">
          <Skeleton className="h-10 w-36 rounded-md" />
          <Skeleton className="h-10 w-36 rounded-md" />
        </div>
      </div>

      {/* Sorting Controls Skeleton */}
      <div className="flex justify-end mb-6 px-4">
        <div className="inline-flex items-center bg-white dark:bg-neutral-900 px-3 py-2 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800">
          <Skeleton className="h-4 w-4 mr-2 rounded-full" />
          <Skeleton className="h-4 w-16 mr-2" />
          <Skeleton className="h-6 w-32" />
        </div>
      </div>

      {/* Results Count Skeleton */}
      <div className="flex items-center justify-between px-2 mb-4">
        <Skeleton className="h-5 w-48" />
      </div>

      {/* Results Skeleton - Products */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
        {Array(8)
          .fill(0)
          .map((_, i) => (
            <div key={`product-${i}`} className="border border-neutral-200 dark:border-neutral-800 rounded-xl p-4 bg-white dark:bg-neutral-900 shadow-sm hover:shadow-md transition-all duration-300">
              <div className="space-y-4">
                <Skeleton className="h-48 w-full rounded-lg" />
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <div className="flex justify-between">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-16" />
                </div>
              </div>
            </div>
          ))}
      </div>

      {/* Results Skeleton - Businesses */}
      <div className="hidden grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 mt-8">
        {Array(8)
          .fill(0)
          .map((_, i) => (
            <div key={`business-${i}`} className="relative h-full overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-sm p-4">
              <div className="flex items-center mb-3">
                <Skeleton className="h-12 w-12 rounded-full mr-3" />
                <div className="space-y-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
              <div className="flex items-start mb-4">
                <Skeleton className="h-4 w-4 mt-0.5 mr-2 rounded-full flex-shrink-0" />
                <div className="space-y-2 w-full">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-3/4" />
                </div>
              </div>
              <div className="flex justify-between items-center pt-3 border-t border-neutral-100 dark:border-neutral-800">
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-4 w-12" />
              </div>
            </div>
          ))}
      </div>
    </div>
  );
}
