"use client";

import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function BusinessCardTableSkeleton() {
  return (
    <div className="w-full overflow-auto rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900">
      <Table>
        <TableHeader>
          <TableRow className="bg-neutral-50 dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-800">
            <TableHead className="w-[50px]"></TableHead>
            <TableHead>
              <Skeleton className="h-8 w-20" />
            </TableHead>
            <TableHead>
              <Skeleton className="h-8 w-16" />
            </TableHead>
            <TableHead className="text-center">
              <Skeleton className="h-8 w-10 mx-auto" />
            </TableHead>
            <TableHead className="text-center">
              <Skeleton className="h-8 w-10 mx-auto" />
            </TableHead>
            <TableHead className="text-center">
              <Skeleton className="h-8 w-10 mx-auto" />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 5 }).map((_, index) => (
            <TableRow key={index} className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50">
              <TableCell className="p-2">
                <Skeleton className="h-10 w-10 rounded-full" />
              </TableCell>
              <TableCell>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                </div>
              </TableCell>
              <TableCell>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-28" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </TableCell>
              <TableCell className="text-center">
                <Skeleton className="h-6 w-12 mx-auto" />
              </TableCell>
              <TableCell className="text-center">
                <Skeleton className="h-6 w-12 mx-auto" />
              </TableCell>
              <TableCell className="text-center">
                <Skeleton className="h-6 w-12 mx-auto" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
