"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect } from "react";
import dynamic from "next/dynamic";

// Dynamically import the dialog to prevent SSR issues
const PaymentMethodLimitationsDialog = dynamic(
  () => import("@/app/(dashboard)/dashboard/business/plan/components/PaymentMethodLimitationsDialog").then(mod => ({ default: mod.PaymentMethodLimitationsDialog })),
  {
    ssr: false,
    loading: () => null // Return null during loading to prevent hydration issues
  }
);

interface PaymentMethodLimitationsContextType {
  openDialog: (_paymentMethod?: string, _onContinueAction?: () => void) => void;
}

const PaymentMethodLimitationsContext = createContext<
  PaymentMethodLimitationsContextType | undefined
>(undefined);

export function PaymentMethodLimitationsProvider({
  children,
}: {
  children: ReactNode;
}) {
  const [isClient, setIsClient] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<string>("UPI");
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);

  // Set client flag to prevent SSR issues
  useEffect(() => {
    setIsClient(true);
  }, []);

  const openDialog = (
    method: string = "UPI",
    onContinueAction?: () => void
  ) => {
    setPaymentMethod(method);
    setDialogOpen(true);
    if (onContinueAction) {
      setPendingAction(() => onContinueAction);
    }
  };

  const handleContinue = () => {
    setDialogOpen(false);
    if (pendingAction) {
      pendingAction();
      setPendingAction(null);
    }
  };

  return (
    <PaymentMethodLimitationsContext.Provider
      value={{
        openDialog,
      }}
    >
      {children}
      {isClient && (
        <PaymentMethodLimitationsDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          onContinue={handleContinue}
          paymentMethod={paymentMethod}
        />
      )}
    </PaymentMethodLimitationsContext.Provider>
  );
}

export function usePaymentMethodLimitations() {
  const context = useContext(PaymentMethodLimitationsContext);
  if (context === undefined) {
    throw new Error(
      "usePaymentMethodLimitations must be used within a PaymentMethodLimitationsProvider"
    );
  }
  return context;
}
