import { MobileAuthService } from '@/backend/supabase/services/auth/mobileAuthService';
import { supabase } from '@/lib/supabase';

// Mock the supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      signInWithPassword: jest.fn(),
    },
  },
}));

describe('MobileAuthService', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('signInWithMobilePassword', () => {
    it('should call signInWithPassword with the correct phone number and password', async () => {
      const mobile = '1234567890';
      const password = 'password123';
      const expectedPhoneNumber = `+91${mobile}`;

      (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({ data: {}, error: null });

      await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
        phone: expectedPhoneNumber,
        password: password,
      });
    });

    it('should return the result from signInWithPassword', async () => {
        const mobile = '1234567890';
        const password = 'password123';
        const mockResult = { data: { user: { id: '123' } }, error: null };
  
        (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue(mockResult);
  
        const result = await MobileAuthService.signInWithMobilePassword(mobile, password);
  
        expect(result).toEqual(mockResult);
      });
  });
});
