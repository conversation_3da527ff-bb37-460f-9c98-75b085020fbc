import { AuthService } from '@/backend/supabase/services/auth/authService';
import { supabase } from '@/lib/supabase';

// Mock the Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
      signOut: jest.fn(),
      signInWithPassword: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
    })),
  },
}));

describe('AuthService Integration Tests', () => {
  const mockUser = { id: '123', email: '<EMAIL>' };
  const mockSession = { access_token: 'abc-123', refresh_token: 'def-456', user: mockUser };

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('signInWithMobilePassword', () => {
    it('should return user and session on successful sign-in', async () => {
      const mobile = '+1234567890';
      const password = 'password123';
      (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      });

      const result = await AuthService.signInWithMobilePassword(mobile, password);

      expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
        phone: mobile,
        password: password,
      });
      expect(result.data.user).toEqual(mockUser);
      expect(result.data.session).toEqual(mockSession);
    });

    it('should return an error on failed sign-in', async () => {
      const mobile = '+1234567890';
      const password = 'wrongpassword';
      const mockError = { message: 'Invalid login credentials', status: 400 };
      (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
        data: { user: null, session: null },
        error: mockError,
      });

      const result = await AuthService.signInWithMobilePassword(mobile, password);

      expect(result.error).toEqual(mockError);
    });

    it('should throw an error on network failure', async () => {
        const mobile = '+1234567890';
        const password = 'password123';
        const mockError = new Error('Network error');
        (supabase.auth.signInWithPassword as jest.Mock).mockRejectedValue(mockError);
  
        await expect(AuthService.signInWithMobilePassword(mobile, password)).rejects.toThrow('Network error');
      });

    it('should handle timeout errors', async () => {
        const mobile = '+1234567890';
        const password = 'password123';
        const mockError = new Error('Request timed out');
        (supabase.auth.signInWithPassword as jest.Mock).mockRejectedValue(mockError);

        await expect(AuthService.signInWithMobilePassword(mobile, password)).rejects.toThrow('Request timed out');
    });

    it('should handle rate limiting errors', async () => {
        const mobile = '+1234567890';
        const password = 'password123';
        const mockError = { message: 'Too many requests', status: 429 };
        (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
            data: { user: null, session: null },
            error: mockError,
        });

        const result = await AuthService.signInWithMobilePassword(mobile, password);
        expect(result.error).toEqual(mockError);
    });

    it('should pass credentials to Supabase without modification', async () => {
        const mobile = ' +1234567890 ';
        const password = ' password123 ';
        (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
            data: { user: mockUser, session: mockSession },
            error: null,
        });

        await AuthService.signInWithMobilePassword(mobile, password);

        expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
            phone: mobile,
            password: password,
        });
    });
  });

  describe('getCurrentUser', () => {
    it('should return current user if a valid session exists', async () => {
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const result = await AuthService.getCurrentUser();

      expect(supabase.auth.getUser).toHaveBeenCalled();
      expect(result.data.user).toEqual(mockUser);
    });

    it('should sign out user if session is invalid', async () => {
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({
        data: { user: null },
        error: { message: 'session_not_found', code: 'session_not_found' },
      });

      await AuthService.getCurrentUser();

      expect(supabase.auth.signOut).toHaveBeenCalled();
    });
  });

  describe('signOut', () => {
    it('should call supabase.auth.signOut', async () => {
      await AuthService.signOut();
      expect(supabase.auth.signOut).toHaveBeenCalled();
    });
  });
});