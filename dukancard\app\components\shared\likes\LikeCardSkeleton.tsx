"use client";

import { motion } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface LikeCardSkeletonProps {
  index?: number;
  variant?: 'default' | 'compact';
}

export default function LikeCardSkeleton({ 
  index = 0, 
  variant = 'default' 
}: LikeCardSkeletonProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.05 }}
      className={cn(
        "rounded-lg border p-0 overflow-hidden transition-all duration-300",
        "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",
        "shadow-sm",
        variant === 'compact' && "max-w-sm"
      )}
    >
      {/* Card background with subtle pattern */}
      <div
        className="absolute inset-0 pointer-events-none opacity-5 dark:opacity-10"
        style={{
          backgroundImage: `url("/decorative/card-texture.svg")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      ></div>

      <div className="relative z-10 p-4">
        <div className="flex items-start gap-3">
          <Skeleton className="h-12 w-12 rounded-full" />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <Skeleton className="h-5 w-32 mb-2" />
                <div className="flex items-center gap-1 mt-1">
                  <Skeleton className="h-3 w-3 rounded-full" />
                  <Skeleton className="h-3 w-16" />
                </div>
                <Skeleton className="h-3 w-24 mt-1" />
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-16" />
        </div>
      </div>
    </motion.div>
  );
}

export function LikeListSkeleton({ 
  variant = 'default',
  count = 6 
}: { 
  variant?: 'default' | 'compact';
  count?: number;
}) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <LikeCardSkeleton 
          key={index} 
          index={index} 
          variant={variant}
        />
      ))}
    </div>
  );
}
