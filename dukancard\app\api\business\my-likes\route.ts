import { createClient } from '@/utils/supabase/server';
import { createAdminClient } from '@/utils/supabase/admin';
import { NextRequest, NextResponse } from 'next/server';

// Define interfaces for the expected data structure
interface BusinessProfileDataForLike {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface BusinessMyLike {
  id: string;
  business_profiles: BusinessProfileDataForLike | null;
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  perPage: number;
}

export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = 12; // Optimized for 3-column grid (1x12, 2x6, 3x4)
    const searchQuery = searchParams.get('search') || '';

    // Use admin client for cross-table queries
    const supabaseAdmin = createAdminClient();

    // Build the count query first
    let countQuery = supabaseAdmin
      .from('likes')
      .select(`
        id,
        business_profiles!inner (
          id,
          business_name
        )
      `, { count: 'exact', head: true })
      .eq('user_id', user.id);

    // Apply search filter to count query if provided
    if (searchQuery) {
      countQuery = countQuery.ilike('business_profiles.business_name', `%${searchQuery}%`);
    }

    // Get total count first for pagination
    const { count: totalCount, error: countError } = await countQuery;

    if (countError) {
      return NextResponse.json(
        { error: 'Failed to count likes' },
        { status: 500 }
      );
    }

    if (!totalCount || totalCount === 0) {
      return NextResponse.json({
        likes: [],
        pagination: {
          currentPage: page,
          totalPages: 0,
          totalCount: 0,
          perPage
        }
      });
    }

    // Build the data query separately for pagination
    let dataQuery = supabaseAdmin
      .from('likes')
      .select(`
        id,
        business_profiles!inner (
          id,
          business_name,
          business_slug,
          logo_url,
          city,
          state,
          pincode,
          address_line
        )
      `)
      .eq('user_id', user.id);

    // Apply search filter to data query if provided
    if (searchQuery) {
      dataQuery = dataQuery.ilike('business_profiles.business_name', `%${searchQuery}%`);
    }

    // Apply pagination to the data query (database-level pagination)
    const from = (page - 1) * perPage;
    dataQuery = dataQuery.range(from, from + perPage - 1);

    const { data: likesWithProfiles, error: likesError } = await dataQuery;

    if (likesError) {
      return NextResponse.json(
        { error: 'Failed to fetch likes' },
        { status: 500 }
      );
    }

    // Transform the data to match the expected format
    const transformedLikes = (likesWithProfiles || []).map(item => ({
      id: item.id,
      business_profiles: Array.isArray(item.business_profiles)
        ? item.business_profiles[0] || null
        : item.business_profiles
    })) as BusinessMyLike[];

    const totalPages = Math.ceil(totalCount / perPage);

    const paginationData: PaginationData = {
      currentPage: page,
      totalPages,
      totalCount,
      perPage
    };

    return NextResponse.json({
      likes: transformedLikes,
      pagination: paginationData
    });

  } catch (error) {
    console.error('Error fetching business my likes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
