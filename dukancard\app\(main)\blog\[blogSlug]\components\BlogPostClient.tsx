"use client";

import { motion } from "framer-motion";
import {
  Calendar,
  Clock,
  User,
  Share2,
  Tag,
  ChevronRight,
  BookOpen,
  Eye,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Blog } from "@/lib/types/blog";
import Link from "next/link";
import Image from "next/image";
import { format } from "date-fns";
import BlogContent from "@/components/blog/BlogContent";
import BlogCard from "@/components/blog/BlogCard";
import { toast } from "sonner";

interface BlogPostClientProps {
  blog: Blog;
  relatedBlogs: Blog[];
}

export default function BlogPostClient({
  blog,
  relatedBlogs,
}: BlogPostClientProps) {
  const publishedDate = blog.published_at ? new Date(blog.published_at) : null;
  const formattedDate = publishedDate
    ? format(publishedDate, "MMMM dd, yyyy")
    : null;

  const handleShare = async () => {
    const url = window.location.href;
    const title = blog.title;
    const text = blog.excerpt || `Check out this blog post: ${title}`;

    if (navigator.share) {
      try {
        await navigator.share({ title, text, url });
      } catch (error) {
        // User cancelled sharing or error occurred
        console.log("Share cancelled or failed:", error);
      }
    } else {
      // Fallback to copying URL to clipboard
      try {
        await navigator.clipboard.writeText(url);
        toast.success("Link copied to clipboard!");
      } catch (error) {
        console.error("Failed to copy link:", error);
        toast.error("Failed to copy link");
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header Section */}
      <div className="bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumb */}
          <motion.nav
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 text-sm pt-6 mb-8"
          >
            <Link
              href="/"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Home
            </Link>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
            <Link
              href="/blog"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Blog
            </Link>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
            <span className="text-foreground truncate">{blog.title}</span>
          </motion.nav>

          {/* Article Header */}
          <motion.header
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="pb-8"
          >
            {/* Categories */}
            {blog.categories && blog.categories.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {blog.categories.map((category) => (
                  <Badge key={category} variant="secondary" className="text-sm">
                    {category}
                  </Badge>
                ))}
              </div>
            )}

            {/* Title */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-foreground">
              {blog.title}
            </h1>

            {/* Excerpt */}
            {blog.excerpt && (
              <p className="text-lg sm:text-xl mb-8 leading-relaxed max-w-3xl text-muted-foreground">
                {blog.excerpt}
              </p>
            )}

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-4 sm:gap-6 text-sm mb-8 text-muted-foreground">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>{blog.author_name}</span>
              </div>

              {formattedDate && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{formattedDate}</span>
                </div>
              )}

              {blog.reading_time_minutes && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>{blog.reading_time_minutes} min read</span>
                </div>
              )}
            </div>

            {/* Share Button */}
            <div className="flex items-center gap-4">
              <Button onClick={handleShare} variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </motion.header>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="relative bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Desktop Layout: Sidebar + Content */}
          <div className="max-w-7xl mx-auto">
            <div className="lg:grid lg:grid-cols-12 lg:gap-12 xl:gap-16">
              {/* Main Content */}
              <div className="lg:col-span-8 xl:col-span-9">
                {/* Featured Image */}
                {blog.featured_image_url ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.2 }}
                    className="relative w-full rounded-xl overflow-hidden mb-12 shadow-2xl bg-muted/30"
                  >
                    <Image
                      src={blog.featured_image_url}
                      alt={blog.title}
                      width={1200}
                      height={600}
                      className="w-full h-auto object-contain"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
                      priority
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.2 }}
                    className="relative h-64 md:h-96 rounded-xl overflow-hidden mb-12 shadow-2xl"
                  >
                    <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/5 flex items-center justify-center">
                      <BookOpen className="h-16 w-16 text-primary/40" />
                    </div>
                  </motion.div>
                )}

                {/* Article Content */}
                <motion.article
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="mb-16"
                >
                  <BlogContent content={blog.content} />
                </motion.article>

                {/* Tags */}
                {blog.tags && blog.tags.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="mb-16"
                  >
                    <Card className="p-6 border-0 shadow-lg bg-card/50 backdrop-blur-sm">
                      <div className="flex items-center mb-4">
                        <Tag className="h-5 w-5 mr-2 text-primary" />
                        <h3 className="font-semibold text-lg">Tags</h3>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {blog.tags?.map((tag) => (
                          <Badge
                            key={tag}
                            variant="outline"
                            className="hover:bg-primary hover:text-primary-foreground cursor-pointer transition-colors"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </Card>
                  </motion.div>
                )}
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-4 xl:col-span-3">
                <div className="sticky top-32 space-y-8">
                  {/* Author Card */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <Card className="p-6 border-0 shadow-lg bg-card/50 backdrop-blur-sm">
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                          <User className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">
                            {blog.author_name}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            Author
                          </p>
                        </div>
                      </div>
                      {blog.excerpt && (
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {blog.excerpt.slice(0, 120)}...
                        </p>
                      )}
                    </Card>
                  </motion.div>

                  {/* Article Stats */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <Card className="p-6 border-0 shadow-lg bg-card/50 backdrop-blur-sm">
                      <h3 className="font-semibold text-lg mb-4 flex items-center">
                        <Eye className="h-5 w-5 mr-2 text-primary" />
                        Article Info
                      </h3>
                      <div className="space-y-3">
                        {formattedDate && (
                          <div className="flex items-center text-sm">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>Published {formattedDate}</span>
                          </div>
                        )}
                        {blog.reading_time_minutes && (
                          <div className="flex items-center text-sm">
                            <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>{blog.reading_time_minutes} minute read</span>
                          </div>
                        )}
                        <div className="flex items-center text-sm">
                          <BookOpen className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>
                            {blog.categories && blog.categories.length > 0
                              ? blog.categories.join(", ")
                              : "General"}
                          </span>
                        </div>
                      </div>
                    </Card>
                  </motion.div>

                  {/* Share Card */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <Card className="p-6 border-0 shadow-lg bg-card/50 backdrop-blur-sm">
                      <h3 className="font-semibold text-lg mb-4 flex items-center">
                        <Share2 className="h-5 w-5 mr-2 text-primary" />
                        Share Article
                      </h3>
                      <Button
                        onClick={handleShare}
                        className="w-full"
                        size="sm"
                      >
                        <Share2 className="h-4 w-4 mr-2" />
                        Share this post
                      </Button>
                    </Card>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Related Posts Section */}
            {relatedBlogs.length > 0 && (
              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
                className="mt-20 mb-16"
              >
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                    Related Articles
                  </h2>
                  <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Discover more insights and stories from our blog
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {relatedBlogs.map((relatedBlog) => (
                    <BlogCard key={relatedBlog.id} blog={relatedBlog} />
                  ))}
                </div>
              </motion.section>
            )}

            {/* Explore More CTA */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
              className="text-center py-16"
            >
              <div className="max-w-md mx-auto">
                <h3 className="text-2xl font-bold mb-4">
                  Explore More Stories
                </h3>
                <p className="text-muted-foreground mb-8">
                  Discover more insights, tips, and stories from our blog
                </p>
                <Link href="/blog">
                  <Button size="lg" className="min-w-48">
                    <BookOpen className="h-4 w-4 mr-2" />
                    View All Posts
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
