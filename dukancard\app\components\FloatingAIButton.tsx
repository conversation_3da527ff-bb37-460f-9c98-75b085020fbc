"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Store } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function FloatingAIButton() {
  const [isDesktop, setIsDesktop] = useState(false);

  // Check if we're on desktop (>= 1024px)
  useEffect(() => {
    const checkDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };

    // Initial check
    checkDesktop();

    // Add event listener for resize
    window.addEventListener('resize', checkDesktop);

    // Cleanup
    return () => window.removeEventListener('resize', checkDesktop);
  }, []);

  // Only render on desktop
  if (!isDesktop) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        className="fixed bottom-24 right-6 z-50"
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.5 }}
        transition={{ duration: 0.3 }}
      >
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="relative group">
                <motion.button
                  className="relative flex items-center justify-center p-3 rounded-full bg-[var(--brand-gold)]/80 text-[var(--brand-gold-foreground)] shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-[var(--brand-gold)] focus:ring-offset-2 dark:focus:ring-offset-black cursor-not-allowed opacity-90"
                  whileHover={{ scale: 1.05 }}
                  disabled={true}
                >
                  {/* Small "soon" indicator dot */}
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white dark:border-black"></span>
                  {/* Button glow effect */}
                  <div className="absolute inset-0 rounded-full bg-[var(--brand-gold)]/40 blur-md -z-10"></div>

                  {/* Animated sparkles */}
                  <motion.div
                    animate={{
                      rotate: [0, 15, -15, 0],
                      scale: [1, 1.1, 1]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "loop",
                      ease: "easeInOut"
                    }}
                  >
                    <Store className="h-6 w-6" />
                  </motion.div>
                </motion.button>

                {/* No floating label, using tooltip instead */}
              </div>
            </TooltipTrigger>
            <TooltipContent side="right" className="bg-black/90 dark:bg-white/90 text-white dark:text-black border-none px-3 py-2 font-medium">
              <p className="flex items-center gap-1.5">
                <Store className="h-4 w-4" />
                <span>Dukan AI - Coming Soon!</span>
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </motion.div>
    </AnimatePresence>
  );
}
