import React from 'react';
import { renderHook, act, waitFor } from '@testing-library/react-native';
import { AuthProvider, useAuth, UserRoleStatus } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { getUserRoleStatus } from '@/backend/supabase/services/common/userRoleStatusService';
import { Session, User } from '@supabase/supabase-js';

// Mock the dependencies
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn(),
      signInWithOAuth: jest.fn(),
      getUser: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
    })),
  },
}));

jest.mock('@/backend/supabase/services/common/userRoleStatusService', () => ({
  getUserRoleStatus: jest.fn(),
}));

jest.mock('@/backend/supabase/services/auth/nativeGoogleAuth2025', () => ({
  signOutFromGoogle: jest.fn(),
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockGetUserRoleStatus = getUserRoleStatus as jest.Mock;

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (mockSupabase.auth.onAuthStateChange as jest.Mock).mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    });
  });

  it('should provide initial loading state and then user and session as null', async () => {
    (mockSupabase.auth.getSession as jest.Mock).mockResolvedValue({ data: { session: null }, error: null });

    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.user).toBeNull();
    expect(result.current.session).toBeNull();
    expect(result.current.profileStatus.loading).toBe(false);
  });

  it('should handle successful sign-in', async () => {
    const mockUser = { id: '123', email: '<EMAIL>' };
    const mockSession = { access_token: 'abc', user: mockUser as User };
    const mockRoleStatus: UserRoleStatus = {
      hasCustomerProfile: true,
      hasBusinessProfile: false,
      businessOnboardingCompleted: false,
      customerProfileComplete: true,
      role: 'customer',
      needsRoleSelection: false,
      needsOnboarding: false,
      needsProfileCompletion: false,
    };

    (mockSupabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
      data: { session: mockSession },
      error: null,
    });
    (mockSupabase.auth.getSession as jest.Mock).mockResolvedValue({ data: { session: mockSession }, error: null });
    mockGetUserRoleStatus.mockResolvedValue({ success: true, data: mockRoleStatus });

    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    await act(async () => {
      await result.current.signIn('<EMAIL>', 'password');
    });
    
    // Manually trigger the onAuthStateChange callback
    const onAuthStateChangeCallback = (mockSupabase.auth.onAuthStateChange as jest.Mock).mock.calls[0][0];
    await act(async () => {
      await onAuthStateChangeCallback('SIGNED_IN', mockSession);
    });

    await waitFor(() => {
        expect(result.current.user).toEqual(mockUser);
        expect(result.current.session).toEqual(mockSession);
        expect(result.current.profileStatus.roleStatus).toEqual(mockRoleStatus);
    });
  });

  it('should handle sign-out', async () => {
    const mockUser = { id: '123', email: '<EMAIL>' };
    const mockSession = { access_token: 'abc', user: mockUser as User };

    (mockSupabase.auth.getSession as jest.Mock).mockResolvedValue({ data: { session: mockSession }, error: null });
    (mockSupabase.auth.signOut as jest.Mock).mockResolvedValue({ error: null });

    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    await waitFor(() => {
        expect(result.current.loading).toBe(false)
    });

    await act(async () => {
      await result.current.signOut();
    });
    
    // Manually trigger the onAuthStateChange callback
    const onAuthStateChangeCallback = (mockSupabase.auth.onAuthStateChange as jest.Mock).mock.calls[0][0];
    await act(async () => {
      await onAuthStateChangeCallback('SIGNED_OUT', null);
    });

    await waitFor(() => {
        expect(result.current.user).toBeNull();
        expect(result.current.session).toBeNull();
        expect(result.current.profileStatus.roleStatus).toBeNull();
    });
  });
});