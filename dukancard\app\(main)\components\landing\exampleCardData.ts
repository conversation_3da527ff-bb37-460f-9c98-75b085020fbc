"use client";

import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

// Example data for the preview card
export const exampleCardData: BusinessCardData = {
  id: "preview-id", // Add a dummy ID
  member_name: "<PERSON><PERSON>",
  title: "Owner",
  business_name: "Mahapatra Kirana & General Store",
  address_line: "Shop No. 12, XYZ Nagar",
  locality: "Bhubaneswar",
  city: "Bhubaneswar",
  state: "Odisha",
  pincode: "751001",
  phone: "+91 700*****89",
  business_category: "Retail & General Store",
  about_bio:
    "Your friendly neighborhood store for daily needs and essentials. Serving the community since 1985.",
  business_slug: "mahapatra-store",
  logo_url: "/Dukancard Homepage Card Profile Photo Old Man.webp", // Profile image for card demo
  instagram_url: "#",
  facebook_url: "#",
  whatsapp_number: "7000000089",
  status: "online",
  established_year: 1985, // Example established year matching the bio
  
  // Add other required fields with default/null values if needed by the type
  contact_email: "<EMAIL>",
  has_active_subscription: true,
  trial_end_date: new Date(Date.now() + (15 * 24 * 60 * 60 * 1000)).toISOString(), // 15 days from now for demo
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};
