"use client";

// import Link from "next/link";
import Image from "next/image";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Heart, MapPin, Star, UserPlus } from "lucide-react";
import { formatIndianNumberShort } from "@/lib/utils";

interface BusinessCardTableProps {
  businesses: BusinessCardData[];
  _isAuthenticated?: boolean;
  _totalLikes?: Record<string, number>;
  _totalSubscriptions?: Record<string, number>;
  _averageRatings?: Record<string, number>;
}

export default function BusinessCardTable({
  businesses,
}: BusinessCardTableProps) {
  return (
    <div className="w-full overflow-auto rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900">
      <Table>
        <TableHeader>
          <TableRow className="bg-neutral-50 dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-800">
            <TableHead className="w-[50px]"></TableHead>
            <TableHead>Business Name</TableHead>
            <TableHead>Location</TableHead>
            <TableHead className="text-center">Likes</TableHead>
            <TableHead className="text-center">Subscribers</TableHead>
            <TableHead className="text-center">Rating</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {businesses.map((business) => (
            <TableRow
              key={business.id}
              className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50 cursor-pointer"
              onClick={() => {
                window.location.href = `/${business.business_slug}`;
              }}
            >
              {/* Logo */}
              <TableCell className="p-2">
                <div className="relative h-10 w-10 rounded-full overflow-hidden bg-neutral-100 dark:bg-neutral-800">
                  {business.logo_url && business.logo_url.trim() !== "" ? (
                    <Image
                      src={business.logo_url}
                      alt={`${business.business_name} logo`}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-neutral-400 dark:text-neutral-600 text-sm font-semibold">
                      {business.business_name.charAt(0)}
                    </div>
                  )}
                </div>
              </TableCell>

              {/* Business Name */}
              <TableCell>
                <span className="font-medium text-neutral-900 dark:text-neutral-100">
                  {business.business_name}
                </span>
              </TableCell>

              {/* Location */}
              <TableCell>
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 text-neutral-500 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-neutral-700 dark:text-neutral-300 line-clamp-1">
                      {business.address_line || ""}
                      {business.locality && `, ${business.locality}`}
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 line-clamp-1">
                      {business.city && `${business.city}, `}
                      {business.state}
                      {business.pincode && ` - ${business.pincode}`}
                    </p>
                  </div>
                </div>
              </TableCell>

              {/* Likes */}
              <TableCell className="text-center">
                <div className="flex flex-col items-center">
                  <div className="flex items-center">
                    <Heart className="h-4 w-4 text-red-500 mr-1" />
                    <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                      {formatIndianNumberShort(business.total_likes || 0)}
                    </span>
                  </div>
                </div>
              </TableCell>

              {/* Subscribers */}
              <TableCell className="text-center">
                <div className="flex flex-col items-center">
                  <div className="flex items-center">
                    <UserPlus className="h-4 w-4 text-blue-500 mr-1" />
                    <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                      {formatIndianNumberShort(
                        business.total_subscriptions || 0
                      )}
                    </span>
                  </div>
                </div>
              </TableCell>

              {/* Rating */}
              <TableCell className="text-center">
                <div className="flex flex-col items-center">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-500 mr-1" />
                    <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                      {(business.average_rating || 0).toFixed(1)}
                    </span>
                  </div>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
