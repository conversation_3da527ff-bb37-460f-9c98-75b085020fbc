import {
  getSubscriptionDetails,
  getInvoicesForSubscription,
  getPayments,
  processSubscriptionRefund,
  cancelSubscription,
} from "./razorpayApi";

// Export the processSubscriptionRefund and cancelSubscription functions
export { processSubscriptionRefund, cancelSubscription };

/**
 * Finds a payment ID for a subscription
 */
export async function findPaymentIdForSubscription(
  subscriptionId: string
): Promise<
  { success: true; paymentId: string } | { success: false; error: string }
> {
  try {
    // 1. First, check subscription details
    const subscriptionResult = await getSubscriptionDetails(subscriptionId);
    if (!subscriptionResult.success) {
      return { success: false, error: subscriptionResult.error || "Failed to get subscription details" };
    }

    // 2. Get the invoices for this subscription
    const invoicesResult = await getInvoicesForSubscription(subscriptionId);
    if (!invoicesResult.success) {
      return { success: false, error: invoicesResult.error || "Failed to get subscription invoices" };
    }

    const invoices = invoicesResult.invoices;

    // Check if invoices has items property and it's an array
    if (
      !invoices ||
      !("items" in invoices) ||
      !Array.isArray(invoices.items) ||
      invoices.items.length === 0
    ) {
      return {
        success: false,
        error: "No invoices found for this subscription",
      };
    }

    // Get the most recent paid invoice
    const paidInvoice = invoices.items.find(
      (invoice: { status: string; payment_id?: string }) =>
        invoice.status === "paid"
    );

    let paymentId: string | null = null;

    // Try to get payment ID from the invoice
    if (paidInvoice && paidInvoice.payment_id) {
      paymentId = paidInvoice.payment_id;
      return { success: true, paymentId: paymentId as string };
    }

    // If we can't find a paid invoice, try to get the payment directly from the subscription

    try {
      // Get the subscription details again to check for payment_id
      const subDetailsResult = await getSubscriptionDetails(subscriptionId);

      if (subDetailsResult.success) {
        const subDetails = subDetailsResult.data;

        // Check if there's a current_invoice with a payment_id
        if (
          subDetails.current_invoice &&
          typeof subDetails.current_invoice === "object" &&
          "payment_id" in subDetails.current_invoice &&
          typeof subDetails.current_invoice.payment_id === "string"
        ) {
          paymentId = subDetails.current_invoice.payment_id;
          return { success: true, paymentId: paymentId as string };
        }
      }

      // If we still don't have a payment ID, try to get all payments
      if (!paymentId) {
        const paymentsResult = await getPayments(10);

        if (paymentsResult.success) {
          const paymentsData = paymentsResult.payments;

          // Find a payment that matches this subscription
          if (paymentsData.items && paymentsData.items.length > 0) {
            // Look for payments with notes containing this subscription ID
            const matchingPayment = paymentsData.items.find(
              (payment: {
                id: string;
                notes?: { subscription_id?: string };
                description?: string;
              }) =>
                payment.notes &&
                (payment.notes.subscription_id === subscriptionId ||
                  payment.description?.includes(subscriptionId))
            );

            if (matchingPayment) {
              paymentId = matchingPayment.id;
              return { success: true, paymentId: paymentId as string };
            }
          }
        }
      }
    } catch (error) {
      console.error("Error getting payment ID from subscription:", error);
    }

    // If we still don't have a payment ID, return failure
    if (!paymentId) {
      return { success: false, error: "No payment ID found" };
    }

    return { success: true, paymentId: paymentId as string };
  } catch (error) {
    console.error("Error finding payment ID for subscription:", error);
    return {
      success: false,
      error: "An error occurred while finding the payment ID",
    };
  }
}

/**
 * Gets the invoice amount for a payment
 */
export async function getInvoiceAmount(
  subscriptionId: string,
  paymentId: string
): Promise<
  { success: true; amount: number | null } | { success: false; error: string }
> {
  try {
    // Get the invoice details to find the exact amount that was charged
    const invoiceDetailsResult = await getInvoicesForSubscription(
      subscriptionId
    );

    if (!invoiceDetailsResult.success) {
      return { success: false, error: invoiceDetailsResult.error || "Failed to get invoice details" };
    }

    const invoiceDetails = invoiceDetailsResult.invoices;

    // Safely access count property - not used but kept for reference
    // const invoiceCount = "count" in invoiceDetails ? invoiceDetails.count : 0;

    let amount: number | null = null;

    // Check if invoices has items property and it's an array
    if (
      "items" in invoiceDetails &&
      Array.isArray(invoiceDetails.items) &&
      invoiceDetails.items.length > 0
    ) {
      // Type guard for invoice items
      type InvoiceItem = {
        payment_id?: string;
        status: string;
        amount?: number;
        line_items?: Array<{ amount: number }>;
        issued_at?: number;
        id?: string;
      };

      const items = invoiceDetails.items as InvoiceItem[];

      // Find the matching invoice
      const matchingInvoice = items.find(
        (invoice) =>
          invoice.payment_id === paymentId && invoice.status === "paid"
      );

      if (matchingInvoice && typeof matchingInvoice.amount === "number") {
        // Use the invoice amount directly
        amount = matchingInvoice.amount;
      } else {
        // If we can't find the exact matching invoice, use the most recent paid invoice
        const paidInvoices = items.filter(
          (invoice) => invoice.status === "paid"
        );

        if (paidInvoices.length > 0) {
          // Sort by issued_at in descending order to get the most recent
          const invoicesWithIssuedAt = paidInvoices.filter(
            (invoice): invoice is InvoiceItem & { issued_at: number } =>
              typeof invoice.issued_at === "number"
          );

          if (invoicesWithIssuedAt.length > 0) {
            invoicesWithIssuedAt.sort((a, b) => b.issued_at - a.issued_at);

            const recentInvoice = invoicesWithIssuedAt[0];

            if (typeof recentInvoice.amount === "number") {
              amount = recentInvoice.amount;
              // Use the amount from the most recent invoice
            }
          }
        }
      }
    }

    return { success: true, amount };
  } catch (error) {
    console.error("Error getting invoice amount:", error);
    return {
      success: false,
      error: "An error occurred while getting the invoice amount",
    };
  }
}
