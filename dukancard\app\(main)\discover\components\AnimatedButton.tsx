"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ReactNode, ButtonHTMLAttributes, useEffect, useState } from "react";
import styles from "./AnimatedButton.module.css";

interface AnimatedButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  className?: string;
}

export default function AnimatedButton({
  children,
  className = "",
  ...props
}: AnimatedButtonProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);
  return (
    <div className="relative group w-full">
      <div className="absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)] via-purple-600 to-[var(--brand-gold)] rounded-lg blur opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-gradient-x"></div>
      <Button
        {...props}
        className={`relative w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-8 py-6 h-12 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer disabled:opacity-70 disabled:pointer-events-none ${className}`}
      >
        <div className="absolute inset-0 rounded-lg overflow-hidden">
          {isClient && (
            <div
              className={`absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent ${styles.shimmerEffect}`}
              style={{ transform: 'translateX(-100%)' }}
            />
          )}
        </div>
        <div className="relative flex items-center justify-center">
          {children}
        </div>
      </Button>
    </div>
  );
}
