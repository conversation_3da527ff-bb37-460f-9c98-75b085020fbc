import React from "react";
import Header from "@/app/components/Header"; // Adjust path if necessary
import Footer from "@/app/components/Footer"; // Adjust path if necessary
import BottomNav from "@/app/components/BottomNav"; // Import the BottomNav component
import MobileFooter from "@/app/components/MobileFooter"; // Import the MobileFooter component
import AdvertiseButton from "@/app/components/AdvertiseButton"; // Import the AdvertiseButton component
import FloatingAIButton from "@/app/components/FloatingAIButton"; // Import the FloatingAIButton component
import ScrollToTopButton from "@/components/ui/scroll-to-top-button"; // Import the reusable ScrollToTopButton
import { ThemeProvider } from "next-themes";

export default function MainLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      {/* Allow system/user preference for theme */}
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
        <div className="min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300">
          <Header />
          {/* Added pt-24 (padding-top) to create space between header and content */}
          <main className="flex-1 pt-24 pb-20 md:pb-0">{children}</main>
          {/* Added main tag for semantics and bottom padding for mobile */}
          <Footer />
          <MobileFooter />
          <BottomNav />
          <AdvertiseButton />
          <FloatingAIButton />
          <ScrollToTopButton excludePaths={["/dashboard"]} />
        </div>
      </ThemeProvider>
    </>
  );
}
