import React from 'react';

// Mock motion components to be simple divs or spans that pass through props
const MockmotionComponent = React.forwardRef<any, any>(({ children, ...props }, ref) => {
  return React.createElement('div', { ...props, ref }, children);
});

export const motion = {
  div: MockmotionComponent,
  span: MockmotionComponent,
  button: MockmotionComponent,
  p: MockmotionComponent,
  h1: MockmotionComponent,
  h2: MockmotionComponent,
  h3: MockmotionComponent,
};

export const AnimatePresence = ({ children }: { children: React.ReactNode }) => {
  return React.createElement(React.Fragment, null, children);
};