"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { AlertCircle, Star } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

import ReviewSortDropdown, { ReviewSortOption } from "@/app/components/shared/reviews/ReviewSortDropdown";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import ReviewCard from "@/app/components/shared/reviews/ReviewCard";
import ReviewCardSkeleton from "@/app/components/shared/reviews/ReviewCardSkeleton";

interface BusinessProfileDataForReview {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: BusinessProfileDataForReview | null;
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  perPage: number;
}

export default function EnhancedReviewListClient() {
  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [sortBy, setSortBy] = useState<ReviewSortOption>("newest");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    perPage: 8 // Optimized for 2-column grid
  });



  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  // Fetch reviews from the API
  const fetchReviews = useCallback(async (page: number, sort: ReviewSortOption) => {
    setIsLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        sort: sort,
      });

      const response = await fetch(`/api/customer/reviews?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch reviews');
      }

      const data = await response.json();
      setReviews(data.reviews);
      setPagination(data.pagination);
    } catch (_err) {
      setError('Failed to load reviews. Please try again.');
      setReviews([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch reviews when page or sort changes
  useEffect(() => {
    fetchReviews(pagination.currentPage, sortBy);
  }, [pagination.currentPage, sortBy, fetchReviews]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  // Handle review deletion
  const handleDeleteSuccess = (reviewId: string) => {
    setReviews(prevReviews => prevReviews.filter(r => r.id !== reviewId));

    // If we deleted the last review on the page, go to previous page
    if (reviews.length === 1 && pagination.currentPage > 1) {
      handlePageChange(pagination.currentPage - 1);
    } else {
      // Refresh the current page
      fetchReviews(pagination.currentPage, sortBy);
    }
  };

  // Handle sort change
  const handleSortChange = (newSortBy: ReviewSortOption) => {
    setSortBy(newSortBy);
    // Reset to first page when sorting changes
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };



  // Generate pagination items
  const generatePaginationItems = () => {
    const items = [];
    const { currentPage, totalPages } = pagination;

    // Always show first page
    items.push(
      <PaginationItem key="page-1">
        <PaginationLink
          href="#"
          onClick={(e) => {
            e.preventDefault();
            handlePageChange(1);
          }}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );

    // Show ellipsis if needed
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Show pages around current page
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i <= 1 || i >= totalPages) continue; // Skip first and last pages as they're always shown
      items.push(
        <PaginationItem key={`page-${i}`}>
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(i);
            }}
            isActive={currentPage === i}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    // Show ellipsis if needed
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Always show last page if there's more than one page
    if (totalPages > 1) {
      items.push(
        <PaginationItem key={`page-${totalPages}`}>
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(totalPages);
            }}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    <div className="space-y-4">
      {/* Sort Controls */}
      <div className="flex justify-end mb-6">
        {/* Sort dropdown */}
        <ReviewSortDropdown
          sortBy={sortBy}
          onSortChange={handleSortChange}
          className="sm:w-auto"
        />
      </div>



      {/* Error message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Loading state */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <ReviewCardSkeleton key={index} index={index} />
          ))}
        </div>
      ) : reviews.length > 0 ? (
        <>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
          >
            {reviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
                onDeleteSuccess={handleDeleteSuccess}
              />
            ))}
          </motion.div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="mt-6">
              <Pagination>
                <PaginationContent>
                  {pagination.currentPage > 1 && (
                    <PaginationItem>
                      <motion.div
                        whileHover={{ x: -2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <PaginationPrevious
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(pagination.currentPage - 1);
                          }}
                          className="border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200"
                        />
                      </motion.div>
                    </PaginationItem>
                  )}

                  {generatePaginationItems().map((item, index) => (
                    <motion.div
                      key={`pagination-${index}`}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
                    >
                      {item}
                    </motion.div>
                  ))}

                  {pagination.currentPage < pagination.totalPages && (
                    <PaginationItem>
                      <motion.div
                        whileHover={{ x: 2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <PaginationNext
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(pagination.currentPage + 1);
                          }}
                          className="border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200"
                        />
                      </motion.div>
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="mb-4">
              <div className="w-16 h-16 mx-auto bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center">
                <Star className="w-6 h-6 text-amber-600 dark:text-amber-400" />
              </div>
            </div>
            <h3 className="text-lg font-medium text-neutral-800 dark:text-neutral-100 mb-2">
              No reviews found
            </h3>
            <p className="text-neutral-500 dark:text-neutral-400 mb-6">
              You haven&apos;t written any reviews yet.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
