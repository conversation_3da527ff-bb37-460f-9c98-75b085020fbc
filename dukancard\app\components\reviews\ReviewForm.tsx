"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Star, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface ReviewFormProps {
  userHasReviewed: boolean;
  ratingValue: number;
  reviewText: string;
  isSubmittingReview: boolean;
  onRatingChange: (_rating: number) => void;
  onReviewTextChange: (_text: string) => void;
  onSubmit: (_e: React.FormEvent) => void;
  onDelete: () => void;
}

export default function ReviewForm({
  userHasReviewed,
  ratingValue,
  reviewText,
  isSubmittingReview,
  onRatingChange,
  onReviewTextChange,
  onSubmit,
  onDelete,
}: ReviewFormProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="relative p-4 sm:p-6 md:p-8 border rounded-xl bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-800/90 dark:to-neutral-900/90 shadow-lg hover:shadow-xl transition-all duration-300 border-neutral-200 dark:border-neutral-700/80 mb-6 sm:mb-10 overflow-hidden"
    >
      {/* Background decorative elements */}
      <div className="absolute -top-12 -right-12 w-40 h-40 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-2xl pointer-events-none" />
      <div className="absolute -bottom-16 -left-16 w-48 h-48 bg-blue-500/5 dark:bg-blue-500/10 rounded-full blur-3xl pointer-events-none" />

      {/* Header with icon */}
      <div className="flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
        <motion.div
          initial={{ scale: 0, rotate: -20 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
            delay: 0.2
          }}
          className="p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-[var(--brand-gold)]/20 to-amber-500/20 text-[var(--brand-gold)] shadow-sm border border-[var(--brand-gold)]/10"
        >
          {userHasReviewed ? (
            <Star className="w-5 h-5 sm:w-6 sm:h-6 fill-[var(--brand-gold)]" />
          ) : (
            <Star className="w-5 h-5 sm:w-6 sm:h-6" />
          )}
        </motion.div>
        <h3 className="text-lg sm:text-xl font-bold text-neutral-800 dark:text-neutral-100">
          {userHasReviewed ? 'Your Review' : 'Write a Review'}
        </h3>
        {userHasReviewed && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            <Badge className="bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] border-[var(--brand-gold)]/20">
              Submitted
            </Badge>
          </motion.div>
        )}
      </div>

      {/* Helpful tip */}
      {!userHasReviewed && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
          className="mb-4 sm:mb-6 bg-blue-50 dark:bg-blue-900/20 p-3 sm:p-4 rounded-lg border border-blue-100 dark:border-blue-800/30 flex items-start gap-2 sm:gap-3"
        >
          <div className="text-blue-500 dark:text-blue-400 mt-0.5">
            <Star className="w-4 h-4 sm:w-5 sm:h-5" />
          </div>
          <div>
            <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">Your review matters!</p>
            <p className="text-xs text-blue-600/80 dark:text-blue-400/80 mt-0.5">
              Honest feedback helps others discover great businesses and helps improve services.
            </p>
          </div>
        </motion.div>
      )}

      <form onSubmit={onSubmit} className="space-y-6">
        {/* Rating stars with enhanced animation */}
        <div>
          <Label htmlFor="rating" className="mb-3 text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2">
            <span>How would you rate your experience?</span>
            <span className="text-red-500">*</span>
          </Label>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.4 }}
            className="flex items-center justify-center bg-gradient-to-r from-neutral-50 to-white dark:from-neutral-800/50 dark:to-neutral-800/30 p-2 sm:p-4 rounded-xl border border-neutral-200 dark:border-neutral-700/50 mb-2 shadow-sm overflow-hidden"
          >
            {[1, 2, 3, 4, 5].map((star, index) => (
              <motion.button
                key={star}
                type="button"
                initial={{ opacity: 0, scale: 0.5, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{
                  duration: 0.4,
                  delay: 0.5 + index * 0.1,
                  type: "spring",
                  stiffness: 200,
                }}
                whileHover={{
                  scale: 1.2,
                  transition: { duration: 0.15 }
                }}
                whileTap={{ scale: 0.9, rotate: 0 }}
                onClick={() => onRatingChange(star)}
                className={cn(
                  "p-1 sm:p-2 mx-1 sm:mx-2 rounded-full transition-all duration-300 cursor-pointer",
                  star <= ratingValue
                    ? "text-yellow-400 hover:text-yellow-300 scale-110 drop-shadow-md"
                    : "text-neutral-300 dark:text-neutral-600 hover:text-neutral-400 dark:hover:text-neutral-500"
                )}
                aria-label={`Rate ${star} out of 5 stars`}
              >
                <>
                  {star <= ratingValue ? (
                    <Star className="w-6 h-6 sm:w-8 sm:h-8 fill-current" />
                  ) : (
                    <Star className="w-6 h-6 sm:w-8 sm:h-8 fill-current drop-shadow-sm" />
                  )}
                  {star <= ratingValue && (
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                      <div className="w-full h-full absolute bg-yellow-300/20 dark:bg-yellow-500/30 rounded-full blur-md" />
                    </div>
                  )}
                </>
              </motion.button>
            ))}
          </motion.div>

          {/* Rating label with animation */}
          <AnimatePresence mode="wait">
            {ratingValue > 0 ? (
              <motion.div
                key="rating-label"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex justify-center"
              >
                <Badge className={cn(
                  "px-3 py-1 text-sm font-medium",
                  ratingValue === 5 ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" :
                  ratingValue === 4 ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300" :
                  ratingValue === 3 ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300" :
                  ratingValue === 2 ? "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300" :
                  "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                )}>
                  {ratingValue === 5 ? "Excellent" :
                   ratingValue === 4 ? "Very Good" :
                   ratingValue === 3 ? "Good" :
                   ratingValue === 2 ? "Fair" :
                   "Poor"}
                </Badge>
              </motion.div>
            ) : (
              <motion.p
                key="select-rating"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center text-sm text-neutral-500 dark:text-neutral-400"
              >
                Select a rating
              </motion.p>
            )}
          </AnimatePresence>
        </div>

        {/* Review text with enhanced styling */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
        >
          <Label htmlFor="reviewText" className="mb-2 block text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Tell us more (Optional)
          </Label>
          <div className="relative">
            <Textarea
              id="reviewText"
              value={reviewText}
              onChange={(e) => onReviewTextChange(e.target.value)}
              placeholder="What did you like or dislike? What stood out about your experience?"
              rows={4}
              className="bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 rounded-lg focus:ring-[var(--brand-gold)] focus:border-[var(--brand-gold)] pr-3 transition-all duration-200 resize-none shadow-sm"
              maxLength={500}
            />
            <div className="absolute bottom-3 right-3 text-xs text-neutral-400 dark:text-neutral-500">
              {reviewText.length}/500
            </div>
          </div>
        </motion.div>

        {/* Action buttons with enhanced styling */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.7 }}
          className="flex flex-col sm:flex-row justify-end items-center gap-3 pt-2"
        >
          {userHasReviewed && (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="w-full sm:w-auto text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 hover:bg-red-50 dark:border-red-800/50 dark:hover:bg-red-900/20 transition-all duration-300 font-medium cursor-pointer h-10"
                onClick={onDelete}
                disabled={isSubmittingReview}
              >
                {isSubmittingReview ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Delete Review
              </Button>
            </motion.div>
          )}

          <motion.div
            whileHover={{ scale: 1.02, translateY: -2 }}
            whileTap={{ scale: 0.98 }}
            className="w-full sm:w-auto order-1 sm:order-2"
          >
            <Button
              type="submit"
              disabled={isSubmittingReview || ratingValue === 0}
              className="w-full sm:w-auto bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] cursor-pointer shadow-md hover:shadow-lg transition-all duration-300 py-3 px-8 text-base font-medium rounded-lg h-12"
            >
              {isSubmittingReview ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  {userHasReviewed ? 'Updating...' : 'Submitting...'}
                </>
              ) : (
                <>
                  {userHasReviewed ? 'Update Review' : 'Submit Review'}
                </>
              )}
            </Button>
          </motion.div>
        </motion.div>
      </form>
    </motion.div>
  );
}
