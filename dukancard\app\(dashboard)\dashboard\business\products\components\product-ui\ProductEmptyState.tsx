"use client";

import { Package } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useProducts } from "../../context/ProductsContext";

interface ProductEmptyStateProps {
  view: "table" | "grid";
}

export default function ProductEmptyState({ view }: ProductEmptyStateProps) {
  const {
    searchTerm,
    handleAddNew,
    canAddMore
  } = useProducts();

  if (view === "table") {
    return (
      <div className="h-32 flex flex-col items-center justify-center text-neutral-500 dark:text-neutral-400">
        <Package className="h-6 sm:h-8 w-6 sm:w-8 mb-2 opacity-40" />
        <p className="text-xs sm:text-sm text-center max-w-xs">
          {searchTerm
            ? "No products match your current filters"
            : "You haven't added any products or services yet"}
        </p>
        {!searchTerm && (
          <Button
            variant="link"
            className="mt-2 text-xs sm:text-sm text-primary hover:text-primary/80"
            onClick={handleAddNew}
            disabled={!canAddMore}
          >
            Add your first item
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="col-span-full h-40 flex flex-col items-center justify-center text-neutral-500 dark:text-neutral-400 border border-dashed border-neutral-200 dark:border-neutral-700 rounded-xl bg-neutral-50 dark:bg-neutral-800/50">
      <Package className="h-6 sm:h-8 w-6 sm:w-8 mb-2 opacity-40" />
      <p className="text-xs sm:text-sm text-center max-w-xs">
        {searchTerm
          ? "No products match your current filters"
          : "You haven't added any products or services yet"}
      </p>
      {!searchTerm && (
        <Button
          variant="link"
          className="mt-2 text-xs sm:text-sm text-primary hover:text-primary/80"
          onClick={handleAddNew}
          disabled={!canAddMore}
        >
          Add your first item
        </Button>
      )}
    </div>
  );
}
