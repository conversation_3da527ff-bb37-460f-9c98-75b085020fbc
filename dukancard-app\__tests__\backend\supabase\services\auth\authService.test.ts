import { AuthService } from '@/backend/supabase/services/auth/authService';
import { supabase } from '@/lib/supabase';

// Mock the supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
      signOut: jest.fn(),
      signInWithPassword: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
    })),
  },
}));

describe('AuthService', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getCurrentUser', () => {
    it('should return user data on successful fetch', async () => {
      const mockUser = { id: '123', email: '<EMAIL>' };
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: mockUser }, error: null });

      const result = await AuthService.getCurrentUser();

      expect(result.data.user).toEqual(mockUser);
      expect(supabase.auth.getUser).toHaveBeenCalledTimes(1);
    });

    it('should sign out and return error if session is invalid', async () => {
      const error = { message: 'User from sub claim in JWT does not exist', code: 'session_not_found' };
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: null }, error });
      (supabase.auth.signOut as jest.Mock).mockResolvedValue({ error: null });

      const result = await AuthService.getCurrentUser();

      expect(result.error).toEqual(error);
      expect(supabase.auth.signOut).toHaveBeenCalledTimes(1);
    });

    it('should handle unexpected errors and attempt to sign out', async () => {
        const error = new Error('invalid_token');
        (supabase.auth.getUser as jest.Mock).mockRejectedValue(error);
        (supabase.auth.signOut as jest.Mock).mockResolvedValue({ error: null });
  
        await expect(AuthService.getCurrentUser()).rejects.toThrow('invalid_token');
  
        expect(supabase.auth.signOut).toHaveBeenCalledTimes(1);
      });
  });

  describe('getCustomerProfile', () => {
    it('should return customer profile data', async () => {
      const mockProfile = { id: '123', name: 'Test User' };
      const singleMock = jest.fn().mockResolvedValue({ data: mockProfile, error: null });
      const eqMock = jest.fn().mockReturnValue({ single: singleMock });
      const selectMock = jest.fn().mockReturnValue({ eq: eqMock });
      (supabase.from as jest.Mock).mockReturnValue({ select: selectMock });

      const result = await AuthService.getCustomerProfile('123');

      expect(result.data).toEqual(mockProfile);
      expect(supabase.from).toHaveBeenCalledWith('customer_profiles');
      expect(selectMock).toHaveBeenCalledWith('*, latitude, longitude');
      expect(eqMock).toHaveBeenCalledWith('id', '123');
    });
  });

  describe('hasCustomerProfile', () => {
    it('should return true if customer profile exists', async () => {
      const singleMock = jest.fn().mockResolvedValue({ data: { id: '123' }, error: null });
      const eqMock = jest.fn().mockReturnValue({ single: singleMock });
      const selectMock = jest.fn().mockReturnValue({ eq: eqMock });
      (supabase.from as jest.Mock).mockReturnValue({ select: selectMock });

      const result = await AuthService.hasCustomerProfile('123');

      expect(result).toBe(true);
    });

    it('should return false if customer profile does not exist', async () => {
      const singleMock = jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } });
      const eqMock = jest.fn().mockReturnValue({ single: singleMock });
      const selectMock = jest.fn().mockReturnValue({ eq: eqMock });
      (supabase.from as jest.Mock).mockReturnValue({ select: selectMock });

      const result = await AuthService.hasCustomerProfile('123');

      expect(result).toBe(false);
    });
  });

  describe('hasBusinessProfile', () => {
    it('should return true if business profile exists', async () => {
        const singleMock = jest.fn().mockResolvedValue({ data: { id: '123' }, error: null });
        const eqMock = jest.fn().mockReturnValue({ single: singleMock });
        const selectMock = jest.fn().mockReturnValue({ eq: eqMock });
        (supabase.from as jest.Mock).mockReturnValue({ select: selectMock });
  
        const result = await AuthService.hasBusinessProfile('123');
  
        expect(result).toBe(true);
      });
  
      it('should return false if business profile does not exist', async () => {
        const singleMock = jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } });
        const eqMock = jest.fn().mockReturnValue({ single: singleMock });
        const selectMock = jest.fn().mockReturnValue({ eq: eqMock });
        (supabase.from as jest.Mock).mockReturnValue({ select: selectMock });
  
        const result = await AuthService.hasBusinessProfile('123');
  
        expect(result).toBe(false);
      });
  });

  describe('getCustomerProfileForValidation', () => {
    it('should return specific fields for customer profile validation', async () => {
        const mockProfile = { name: 'Test User', pincode: '123456' };
        const singleMock = jest.fn().mockResolvedValue({ data: mockProfile, error: null });
        const eqMock = jest.fn().mockReturnValue({ single: singleMock });
        const selectMock = jest.fn().mockReturnValue({ eq: eqMock });
        (supabase.from as jest.Mock).mockReturnValue({ select: selectMock });
  
        const result = await AuthService.getCustomerProfileForValidation('123');
  
        expect(result.data).toEqual(mockProfile);
        expect(selectMock).toHaveBeenCalledWith('name, pincode, state, city, locality, address, latitude, longitude');
      });
  });

  describe('signOut', () => {
    it('should call supabase.auth.signOut', async () => {
      (supabase.auth.signOut as jest.Mock).mockResolvedValue({ error: null });
      await AuthService.signOut();
      expect(supabase.auth.signOut).toHaveBeenCalledTimes(1);
    });
  });

  describe('signInWithMobilePassword', () => {
    it('should call supabase.auth.signInWithPassword with correct credentials', async () => {
      const mobile = '1234567890';
      const password = 'password';
      (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({ data: {}, error: null });

      await AuthService.signInWithMobilePassword(mobile, password);

      expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
        phone: mobile,
        password: password,
      });
    });
  });
});