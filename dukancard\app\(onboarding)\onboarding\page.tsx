import React from "react"; // Import the new client component
import OnboardingClient from "./OnboardingClient";

// Metadata is handled by app/(onboarding)/onboarding/layout.tsx
// export const metadata: Metadata = {
//   title: 'Business Onboarding - Dukancard',
//   description: 'Set up your Dukancard business profile.',
// };

// This is now a Server Component responsible for rendering the client part.
export default async function OnboardingPage({
  searchParams,
}: {
  searchParams: Promise<{ redirect?: string; message?: string }>;
}) {
  // Get the redirect and message parameters from the URL
  const { redirect, message } = await searchParams;
  const redirectSlug = redirect || null;
  const messageParam = message || null;

  // Any server-side logic or data fetching for onboarding could happen here
  // For example, fetching plan details if they weren't static

  return (
    // Use min-height instead of fixed height to allow scrolling when needed
    <div className="w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center pt-6 pb-20 md:pb-6">
      <OnboardingClient redirectSlug={redirectSlug} message={messageParam} />
    </div>
  );
}
