"use client";

import { useTransition } from "react";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchLocalityCombined } from "../actions/combinedActions";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import { mapProductSortToBackend } from "@/app/(main)/discover/utils/sortMappings";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import {
  LocalitySearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
  SerializableLocality,
} from "./types";

export function useCommonContextFunctions(
  locality: SerializableLocality,
  viewType: ViewType,
  setViewType: React.Dispatch<React.SetStateAction<ViewType>>,
  setIsSearching: React.Dispatch<React.SetStateAction<boolean>>,
  setSearchResult: React.Dispatch<React.SetStateAction<LocalitySearchResult | null>>,
  setIsAuthenticated: React.Dispatch<React.SetStateAction<boolean>>,
  setBusinesses: React.Dispatch<React.SetStateAction<BusinessProfilePublicData[] | BusinessCardData[]>>,
  setProducts: React.Dispatch<React.SetStateAction<NearbyProduct[]>>,
  setHasMore: React.Dispatch<React.SetStateAction<boolean>>,
  setTotalCount: React.Dispatch<React.SetStateAction<number>>,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>,
  setSearchError: React.Dispatch<React.SetStateAction<string | null>>,
  sortBy: BusinessSortBy,
  productSortBy: ProductSortOption,
  productFilterBy: ProductFilterOption
) {
  const [isPending, startTransition] = useTransition();

  // Handle view change
  const handleViewChange = (newViewType: ViewType) => {
    if (newViewType === viewType) return;

    setViewType(newViewType);
    startTransition(async () => {
      await performSearch();
    });
  };

  // Perform search based on current state
  const performSearch = async () => {
    setIsSearching(true);
    setSearchError(null);

    try {
      const currentViewType = viewType;
      const result = await searchLocalityCombined({
        localityName: locality.name,
        pincode: locality.pincode,
        viewType: currentViewType,
        sortBy: currentViewType === "cards" ? sortBy : mapProductSortToBackend(productSortBy),
        productType: currentViewType === "products" && productFilterBy !== "all" ? productFilterBy : null,
      });

      if (result.error) {
        setSearchError(result.error);
        return;
      }

      if (result.data) {
        setSearchResult(result.data);
        if (currentViewType === "cards") {
          setBusinesses(result.data.businesses || []);
        } else {
          setProducts(result.data.products || []);
        }
        setIsAuthenticated(result.data.isAuthenticated);
        setHasMore(result.data.hasMore);
        setTotalCount(result.data.totalCount);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error("Error in performSearch:", error);
      setSearchError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  // Load more items based on current view type
  const loadMore = async () => {
    if (viewType === "cards") {
      // Load more businesses
      // const { loadMoreBusinesses } = await import("./businessContextFunctions");
      // This is a placeholder - we'll need to call the actual function
    } else {
      // Load more products
      // const { loadMoreProducts } = await import("./productContextFunctions");
      // This is a placeholder - we'll need to call the actual function
    }
  };

  return {
    isPending,
    handleViewChange,
    performSearch,
    loadMore,
  };
}
