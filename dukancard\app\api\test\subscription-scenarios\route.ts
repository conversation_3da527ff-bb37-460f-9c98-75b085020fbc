import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { SubscriptionScenarioTester, ScenarioTestResult } from "@/lib/testing/SubscriptionScenarioTester";

// Define a type for the test results
type TestResult = ScenarioTestResult;

/**
 * GET /api/test/subscription-scenarios
 * 
 * Run comprehensive end-to-end subscription scenario tests with webhook simulations.
 * This endpoint tests complete subscription flows including:
 * 1. Trial user authorizes payment for post-trial
 * 2. Active user pauses and resumes subscription
 * 3. Active user cancels subscription
 */
export async function GET(request: NextRequest) {
  try {
    // Check if this is a development environment
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (!isDevelopment) {
      return NextResponse.json(
        { success: false, error: "Test endpoints only available in development" },
        { status: 403 }
      );
    }

    // Get bypass parameter for internal testing
    const { searchParams } = new URL(request.url);
    const bypassAuth = searchParams.get('bypass') === 'internal-testing';

    if (!bypassAuth) {
      // Verify authentication for external requests
      const supabase = await createClient();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        return NextResponse.json(
          { success: false, error: "Authentication required" },
          { status: 401 }
        );
      }
    }

    // Get test type and business ID from query parameters
    const testType = searchParams.get('type') || 'all';
    const businessId = searchParams.get('businessId');

    // Validate business ID if provided
    if (businessId) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(businessId)) {
        return NextResponse.json(
          { success: false, error: "Invalid business ID format. Must be a valid UUID." },
          { status: 400 }
        );
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { success: false, error: "Business ID is required" },
        { status: 400 }
      );
    }

    const tester = new SubscriptionScenarioTester(businessId);

    let results: TestResult[];
    
    switch (testType) {
      case 'trial-auth':
        results = [await tester.testTrialUserAuthorizesPayment()];
        break;
      case 'pause-resume':
        results = [await tester.testPauseResumeFlow()];
        break;
      case 'cancellation':
        results = [await tester.testSubscriptionCancellation()];
        break;
      case 'all':
      default:
        results = await tester.runAllScenarios();
        break;
    }

    // Generate summary
    const summary = {
      timestamp: new Date().toISOString(),
      totalScenarios: results.length,
      successfulScenarios: results.filter((r) => r.success).length,
      failedScenarios: results.filter((r) => !r.success).length,
      results
    };

    return NextResponse.json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error("[SCENARIO_TEST_API] Error running subscription scenario tests:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}


