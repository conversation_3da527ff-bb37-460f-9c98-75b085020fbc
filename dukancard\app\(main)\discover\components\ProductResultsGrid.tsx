"use client";

import { NearbyProduct } from "../actions/types";
import ProductListItem from "@/app/components/ProductListItem";
import Link from "next/link";
import { motion } from "framer-motion";

interface ProductResultsGridProps {
  products: NearbyProduct[];
  lastItemRef: (_node: HTMLDivElement | null) => void;
}

export default function ProductResultsGrid({
  products,
  lastItemRef,
}: ProductResultsGridProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4">
      {products.map((product, index) => {
        const uniqueKey = `product-${product.id}`;

        const productElement = (
          <motion.div
            key={uniqueKey}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
            className="group"
            ref={index === products.length - 1 ? lastItemRef : undefined}
          >
            {product.business_slug ? (
              <Link
                href={`/${product.business_slug}/product/${product.slug || product.id}`}
                className="block h-full"
              >
                <div className="h-full">
                  <ProductListItem product={product} isLink={false} />
                </div>
              </Link>
            ) : (
              <div className="relative h-full">
                <ProductListItem product={product} isLink={false} />
                <div className="absolute bottom-0 left-0 right-0 bg-red-500/80 text-white text-xs py-1 px-2 text-center rounded-b-md">
                  Unable to link to business
                </div>
              </div>
            )}
          </motion.div>
        );

        return productElement;
      })}
    </div>
  );
}
