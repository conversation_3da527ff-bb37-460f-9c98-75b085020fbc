import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { LoginForm } from '@/app/(main)/login/LoginForm';
import { sendOTP, verifyOTP, loginWithMobilePassword } from '@/app/(main)/login/actions';
import { createClient } from '@/utils/supabase/client';
import { getPostLoginRedirectPath } from '@/lib/actions/redirectAfterLogin';
import { toast } from 'sonner';

// Mock child components
jest.mock('@/app/(main)/login/components/EmailOTPForm', () => ({
  EmailOTPForm: jest.fn(({ onEmailSubmit, onOTPSubmit, onResendOTP, onBackToEmail, step, email, countdown, isPending }) => (
    <div>
      EmailOTPForm Mock
      <div data-testid="email-otp-form-step">{step}</div>
      <div data-testid="email-otp-form-email">{email}</div>
      <div data-testid="email-otp-form-countdown">{countdown}</div>
      <div data-testid="email-otp-form-pending">{isPending ? 'true' : 'false'}</div>
      <button onClick={() => onEmailSubmit({ email: '<EMAIL>' })}>Submit Email</button>
      <button onClick={() => onOTPSubmit({ otp: '123456' })}>Submit OTP</button>
      <button onClick={onResendOTP}>Resend OTP</button>
      <button onClick={onBackToEmail}>Back to Email</button>
    </div>
  )),
}));

jest.mock('@/app/(main)/login/components/MobilePasswordForm', () => ({
  MobilePasswordForm: jest.fn(({ onSubmit, isPending }) => (
    <div>
      MobilePasswordForm Mock
      <div data-testid="mobile-password-form-pending">{isPending ? 'true' : 'false'}</div>
      <button onClick={() => onSubmit({ mobile: '1234567890', password: 'password123' })}>Submit Mobile/Password</button>
    </div>
  )),
}));

jest.mock('@/app/(main)/login/components/AuthMethodToggle', () => ({
  AuthMethodToggle: jest.fn(({ onMethodChange, authMethod, step }) => (
    <div>
      AuthMethodToggle Mock
      <div data-testid="auth-method-toggle-method">{authMethod}</div>
      <div data-testid="auth-method-toggle-step">{step}</div>
      <button onClick={() => onMethodChange('email-otp')}>Email OTP</button>
      <button onClick={() => onMethodChange('mobile-password')}>Mobile Password</button>
    </div>
  )),
}));

jest.mock('@/app/(main)/login/components/SocialLoginButton', () => ({
  SocialLoginButton: jest.fn(({ redirectSlug, message, disabled }) => (
    <div>
      SocialLoginButton Mock
      <div data-testid="social-login-button-redirect-slug">{redirectSlug}</div>
      <div data-testid="social-login-button-message">{message}</div>
      <div data-testid="social-login-button-disabled">{disabled ? 'true' : 'false'}</div>
      <button>Login with Google</button>
    </div>
  )),
}));



// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(() => ({
    get: jest.fn((param) => {
      if (param === 'redirect') return null;
      if (param === 'message') return null;
      return null;
    }),
    entries: jest.fn(() => []), // Mock entries for Object.fromEntries
  })),
}));

// Mock Supabase client
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'test-user-id' } }, error: null })),
    },
  })),
}));

// Mock server actions
jest.mock('@/app/(main)/login/actions', () => ({
  sendOTP: jest.fn(),
  verifyOTP: jest.fn(),
  loginWithMobilePassword: jest.fn(),
}));

// Mock redirectAfterLogin action
jest.mock('@/lib/actions/redirectAfterLogin', () => ({
  getPostLoginRedirectPath: jest.fn(() => Promise.resolve('/dashboard/customer')),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

describe('Login Page Integration Tests', () => {
  const mockPush = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('should allow email OTP login and redirect to dashboard', async () => {
    // Mock successful OTP send and verification
    (sendOTP as jest.Mock).mockResolvedValue({ success: true, message: 'OTP sent!' });
    (verifyOTP as jest.Mock).mockResolvedValue({ success: true });
    (getPostLoginRedirectPath as jest.Mock).mockResolvedValue('/dashboard/customer');

    render(<LoginForm />);

    // Simulate email submission by clicking the mocked button
    fireEvent.click(screen.getByRole('button', { name: 'Submit Email' }));

    await waitFor(() => {
      expect(sendOTP).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(screen.getByText('Enter Verification Code')).toBeInTheDocument();
    });

    // Simulate OTP submission by clicking the mocked button
    fireEvent.click(screen.getByRole('button', { name: 'Submit OTP' }));

    await waitFor(() => {
      expect(verifyOTP).toHaveBeenCalledWith({ email: '<EMAIL>', token: '123456' });
      expect(toast.success).toHaveBeenCalledWith('Sign in successful!', { description: 'Redirecting to your dashboard...' });
      expect(mockPush).toHaveBeenCalledWith('/dashboard/customer');
    });
  });

  it('should allow mobile/password login and redirect to dashboard', async () => {
    // Mock successful mobile/password login
    (loginWithMobilePassword as jest.Mock).mockResolvedValue({ success: true });
    (getPostLoginRedirectPath as jest.Mock).mockResolvedValue('/dashboard/business');

    render(<LoginForm />);

    // Switch to mobile/password method
    fireEvent.click(screen.getByRole('button', { name: /Mobile & Password/i }));

    // Simulate mobile/password submission
    fireEvent.click(screen.getByRole('button', { name: 'Submit Mobile/Password' }));

    await waitFor(() => {
      expect(loginWithMobilePassword).toHaveBeenCalledWith({ mobile: '9876543210', password: 'password123' });
      expect(toast.success).toHaveBeenCalledWith('Sign in successful!', { description: 'Redirecting to your dashboard...' });
      expect(mockPush).toHaveBeenCalledWith('/dashboard/business');
    });
  });

  it('should handle social login and redirect to choose-role for new users', async () => {
    // Mock Supabase social login to return a URL
    const mockSignInWithOAuth = jest.fn(() => Promise.resolve({ data: { url: 'https://mock-auth-url.com' }, error: null }));
    (createClient as jest.Mock).mockReturnValue({
      auth: {
        signInWithOAuth: mockSignInWithOAuth,
        getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'new-user-id' } }, error: null })),
      },
    });
    (getPostLoginRedirectPath as jest.Mock).mockResolvedValue('/choose-role'); // Simulate new user

    render(<LoginForm />);

    fireEvent.click(screen.getByRole('button', { name: /Login with Google/i }));

    await waitFor(() => {
      expect(mockSignInWithOAuth).toHaveBeenCalled();
      expect(toast.info).toHaveBeenCalledWith('Google sign-in opened in a new tab', expect.any(Object));
    });

    // Simulate the callback redirecting to /choose-role
    // In a real scenario, this would be handled by the AuthCallbackClient
    // For integration test, we directly assert the expected redirect based on getPostLoginRedirectPath mock
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/choose-role');
    });
  });

  it('should display error toast on failed login attempt', async () => {
    // Mock failed login attempt
    (sendOTP as jest.Mock).mockResolvedValue({ success: false, error: 'Invalid credentials' });

    render(<LoginForm />);

    fireEvent.change(screen.getByLabelText(/Email/i), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: /Continue with Email/i }));

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to send OTP', { description: 'Invalid credentials' });
      expect(mockPush).not.toHaveBeenCalled();
    });
  });

  it('should handle redirect slug from URL after successful login', async () => {
    // Mock successful OTP send and verification
    (sendOTP as jest.Mock).mockResolvedValue({ success: true, message: 'OTP sent!' });
    (verifyOTP as jest.Mock).mockResolvedValue({ success: true });

    // Mock useSearchParams to return a redirect slug
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    (require('next/navigation').useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn((param) => {
        if (param === 'redirect') return 'my-custom-page';
        if (param === 'message') return 'WelcomeBack';
        return null;
      }),
      entries: jest.fn(() => []), // Mock entries for Object.fromEntries
    });

    render(<LoginForm />);

    // Simulate email submission
    fireEvent.change(screen.getByLabelText(/Email/i), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: /Continue with Email/i }));

    await waitFor(() => {
      expect(sendOTP).toHaveBeenCalledWith({ email: '<EMAIL>' });
    });

    // Simulate OTP submission
    fireEvent.change(screen.getByLabelText(/6-digit code/i), { target: { value: '123456' } });
    fireEvent.click(screen.getByRole('button', { name: /Verify OTP/i }));

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/my-custom-page?message=WelcomeBack');
    });
  });
});
