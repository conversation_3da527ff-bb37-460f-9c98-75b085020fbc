import BusinessCardSetupClient from "./BusinessCardSetupClient";
import { Metadata } from "next";
import { siteConfig } from "@/lib/site-config";

export async function generateMetadata(): Promise<Metadata> {
  const title = `Business Card Setup`;
  const description =
    "Learn how to create and customize your digital business card with Dukancard. Get step-by-step instructions for setting up your profile, adding your logo, contact information, and more.";
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || siteConfig.url;
  const pageUrl = `${siteUrl}/support/business-card-setup`;
  const ogImage = `${siteUrl}/opengraph-image.png`;

  return {
    title,
    description,
    keywords: [
      "digital business card setup",
      "Dukancard profile",
      "business card customization",
      "logo upload",
      "contact information",
      "business profile setup",
    ],
    alternates: {
      canonical: "/support/business-card-setup",
    },
    openGraph: {
      title,
      description,
      url: pageUrl,
      siteName: siteConfig.name,
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: `${siteConfig.name} Business Card Setup Guide`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [ogImage],
    },
    // Add WebPage Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: siteConfig.name,
          url: siteUrl,
        },
      }),
    },
  };
}

export default function BusinessCardSetupPage() {
  return <BusinessCardSetupClient />;
}
