"use client";

import { motion, AnimatePresence } from "framer-motion";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useDiscoverContext } from "../context/DiscoverContext";

export default function ErrorSection() {
  const { searchError, isSearching } = useDiscoverContext();

  return (
    <AnimatePresence mode="wait">
      {!isSearching && searchError && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
          key="error-state"
          className="my-12"
        >
          <Alert
            variant="destructive"
            className="max-w-lg mx-auto border-red-200 dark:border-red-900/50 backdrop-blur-sm overflow-hidden"
          >
            <div className="absolute inset-0 bg-red-50/50 dark:bg-red-950/20 pointer-events-none"></div>
            <div className="relative z-10 flex items-start">
              <motion.div
                className="mr-2"
                initial={{ scale: 1 }}
                animate={{ scale: 1.1 }}
                transition={{
                  duration: 0.5,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                <AlertCircle className="h-6 w-6" />
              </motion.div>
              <div>
                <AlertTitle className="text-lg font-semibold mb-1">
                  Error Occurred
                </AlertTitle>
                <AlertDescription className="text-red-700 dark:text-red-300">
                  {searchError}
                </AlertDescription>
              </div>
            </div>
          </Alert>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
