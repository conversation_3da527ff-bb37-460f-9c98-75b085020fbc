"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Megaphone } from "lucide-react";
import { motion } from "framer-motion";
import { siteConfig } from "@/lib/site-config";
import { usePathname } from "next/navigation";

// CSS for vertical text
const verticalTextStyle = {
  writingMode: 'vertical-rl' as const,
  textOrientation: 'mixed' as const,
  transform: 'rotate(180deg)',
  letterSpacing: '0.05em',
  fontSize: '0.8rem',
  fontWeight: 600,
};

export default function AdvertiseButton() {
  const [isHovered, setIsHovered] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const pathname = usePathname();

  // Use useEffect to detect client-side rendering
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't show on dashboard pages or advertise page
  const isDashboardPage = pathname?.includes("/dashboard");
  const isAdvertisePage = pathname === "/advertise";

  if (!isClient || isDashboardPage || isAdvertisePage) {
    return null; // Don't render during SSR, on dashboard pages, or on advertise page
  }

  return (
    <>
      {/* Desktop and tablet version - side button */}
      <div className="fixed right-0 top-1/2 -translate-y-1/2 z-40 hidden sm:block">
        <div className="relative group">
          {/* Button glow effect */}
          <motion.div
            className="absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-l-lg blur-md"
            animate={{ opacity: isHovered ? 0.8 : 0.5 }}
            transition={{ duration: 0.3 }}
          />

          <Link href={siteConfig.advertising.page}>
            <motion.div
              className="relative"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
            >
              <div
                className="cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 px-2 py-6 rounded-l-lg font-medium text-xs relative overflow-hidden shadow-lg flex flex-col items-center justify-center w-10 h-32"
              >
                {/* Button content with animated icon */}
                <div className="flex flex-col items-center justify-center gap-2">
                  <Megaphone className="w-4 h-4" />
                  <span style={verticalTextStyle}>Advertise</span>
                </div>

                {/* Shimmer effect */}
                <motion.div
                  className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none"
                  initial={{ x: "-100%" }}
                  animate={{ x: "100%" }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "linear",
                    repeatDelay: 1
                  }}
                />
              </div>
            </motion.div>
          </Link>
        </div>
      </div>

      {/* Mobile version - floating button */}
      <div className="fixed left-4 bottom-20 z-40 sm:hidden">
        <div className="relative group">
          {/* Button glow effect */}
          <motion.div
            className="absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full blur-md"
            animate={{ opacity: 0.6 }}
            transition={{ duration: 0.3 }}
          />

          <Link href={siteConfig.advertising.page}>
            <motion.div
              className="relative"
              whileTap={{ scale: 0.95 }}
            >
              <div
                className="cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 p-2 rounded-full font-medium text-xs relative overflow-hidden shadow-md flex items-center justify-center w-10 h-10"
              >
                <Megaphone className="w-4 h-4" />

                {/* Shimmer effect */}
                <motion.div
                  className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none"
                  initial={{ x: "-100%" }}
                  animate={{ x: "100%" }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "linear",
                    repeatDelay: 1
                  }}
                />
              </div>
            </motion.div>
          </Link>
        </div>
      </div>
    </>
  );
}
