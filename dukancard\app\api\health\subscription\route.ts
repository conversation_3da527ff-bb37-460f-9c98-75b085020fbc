import { NextRequest, NextResponse } from "next/server";
import { runHealthCheck } from "@/lib/razorpay/webhooks/monitoring";

/**
 * Subscription Health Check API
 * 
 * This endpoint provides comprehensive health monitoring for the subscription system.
 * It checks webhook processing, subscription consistency, and overall system health.
 * 
 * @param req The incoming request
 * @returns Health check results with metrics and alerts
 * 
 * Example response:
 * ```json
 * {
 *   "healthy": true,
 *   "timestamp": "2024-01-15T10:30:00Z",
 *   "metrics": {
 *     "webhook": {
 *       "total_events": 1250,
 *       "successful_events": 1200,
 *       "failed_events": 30,
 *       "retrying_events": 20,
 *       "success_rate": 96.00
 *     },
 *     "subscriptions": {
 *       "total_subscriptions": 500,
 *       "active_subscriptions": 350,
 *       "trial_subscriptions": 50,
 *       "inconsistent_subscriptions": 2,
 *       "health_score": 99.60
 *     }
 *   },
 *   "alerts": [
 *     {
 *       "type": "SUBSCRIPTION_INCONSISTENCY",
 *       "severity": "HIGH",
 *       "message": "2 subscription inconsistencies detected",
 *       "count": 2
 *     }
 *   ],
 *   "recommendations": [
 *     "Review subscription inconsistencies",
 *     "Monitor webhook failure rate"
 *   ]
 * }
 * ```
 */
export async function GET(req: NextRequest) {
  try {
    // Verify API key for security
    const apiKey = req.headers.get("x-api-key") || "";
    const expectedApiKey = process.env.HEALTH_CHECK_API_KEY || process.env.WEBHOOK_RETRY_API_KEY;
    
    if (!expectedApiKey || apiKey !== expectedApiKey) {
      console.error("[HEALTH_CHECK] Invalid or missing API key");
      return NextResponse.json(
        { success: false, message: "Invalid or missing API key" },
        { status: 401 }
      );
    }

    console.log("[HEALTH_CHECK] Starting comprehensive health check");

    // Run comprehensive health check
    const healthResult = await runHealthCheck();

    // Generate recommendations based on health status
    const recommendations: string[] = [];
    
    if (!healthResult.healthy) {
      recommendations.push("Immediate attention required - critical issues detected");
    }
    
    if (healthResult.metrics.success_rate < 95) {
      recommendations.push("Investigate webhook processing issues");
    }
    
    if (healthResult.alerts.some(alert => alert.type === 'SUBSCRIPTION_INCONSISTENCY')) {
      recommendations.push("Review and fix subscription inconsistencies");
    }
    
    if (healthResult.metrics.average_processing_time && healthResult.metrics.average_processing_time > 30000) {
      recommendations.push("Optimize webhook processing performance");
    }

    // Get additional subscription health metrics
    const { createAdminClient } = await import("@/utils/supabase/admin");
    const adminClient = createAdminClient();
    
    const { data: subscriptionHealth, error: healthError } = await adminClient
      .rpc('get_subscription_health_metrics');

    if (healthError) {
      console.error("[HEALTH_CHECK] Error getting subscription health metrics:", healthError);
    }

    // Format response
    const response = {
      healthy: healthResult.healthy,
      timestamp: new Date().toISOString(),
      metrics: {
        webhook: {
          total_events: healthResult.metrics.total_events,
          successful_events: healthResult.metrics.successful_events,
          failed_events: healthResult.metrics.failed_events,
          retrying_events: healthResult.metrics.retrying_events,
          success_rate: healthResult.metrics.success_rate,
          ...(healthResult.metrics.average_processing_time && {
            average_processing_time_ms: Math.round(healthResult.metrics.average_processing_time)
          })
        },
        ...(subscriptionHealth && subscriptionHealth.length > 0 && {
          subscriptions: {
            total_subscriptions: Number(subscriptionHealth[0].total_subscriptions),
            active_subscriptions: Number(subscriptionHealth[0].active_subscriptions),
            trial_subscriptions: Number(subscriptionHealth[0].trial_subscriptions),
            cancelled_subscriptions: Number(subscriptionHealth[0].cancelled_subscriptions),
            expired_subscriptions: Number(subscriptionHealth[0].expired_subscriptions),
            inconsistent_subscriptions: Number(subscriptionHealth[0].inconsistent_subscriptions),
            health_score: Number(subscriptionHealth[0].health_score)
          }
        })
      },
      alerts: healthResult.alerts.map(alert => ({
        type: alert.type,
        severity: alert.severity,
        message: alert.message,
        ...(alert.entity_id && { entity_id: alert.entity_id }),
        ...(alert.subscription_id && { subscription_id: alert.subscription_id }),
        ...(alert.metadata && { metadata: alert.metadata })
      })),
      recommendations,
      summary: {
        critical_alerts: healthResult.alerts.filter(a => a.severity === 'CRITICAL').length,
        high_alerts: healthResult.alerts.filter(a => a.severity === 'HIGH').length,
        medium_alerts: healthResult.alerts.filter(a => a.severity === 'MEDIUM').length,
        low_alerts: healthResult.alerts.filter(a => a.severity === 'LOW').length
      }
    };

    console.log(`[HEALTH_CHECK] Health check completed - Healthy: ${healthResult.healthy}, Alerts: ${healthResult.alerts.length}`);

    return NextResponse.json(response, { 
      status: healthResult.healthy ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error("[HEALTH_CHECK] Error during health check:", error);
    
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    return NextResponse.json(
      {
        healthy: false,
        timestamp: new Date().toISOString(),
        error: "Health check failed",
        message: errorMessage,
        recommendations: ["Check system logs", "Verify database connectivity", "Review monitoring setup"]
      },
      { status: 500 }
    );
  }
}

/**
 * Get health check status summary
 * Lighter endpoint for quick status checks
 */
export async function HEAD(req: NextRequest) {
  try {
    // Verify API key
    const apiKey = req.headers.get("x-api-key") || "";
    const expectedApiKey = process.env.HEALTH_CHECK_API_KEY || process.env.WEBHOOK_RETRY_API_KEY;
    
    if (!expectedApiKey || apiKey !== expectedApiKey) {
      return new NextResponse(null, { status: 401 });
    }

    // Quick health check - just check for critical alerts
    const { createAdminClient } = await import("@/utils/supabase/admin");
    const adminClient = createAdminClient();
    
    const { data: criticalAlerts, error } = await adminClient
      .from('system_alerts')
      .select('id')
      .eq('severity', 'CRITICAL')
      .eq('resolved', false)
      .limit(1);

    if (error) {
      return new NextResponse(null, { status: 500 });
    }

    const hasCriticalAlerts = criticalAlerts && criticalAlerts.length > 0;
    
    return new NextResponse(null, { 
      status: hasCriticalAlerts ? 503 : 200,
      headers: {
        'X-Health-Status': hasCriticalAlerts ? 'unhealthy' : 'healthy',
        'X-Critical-Alerts': String(criticalAlerts?.length || 0)
      }
    });

  } catch (error) {
    console.error("[HEALTH_CHECK] Error during HEAD health check:", error);
    return new NextResponse(null, { status: 500 });
  }
}
