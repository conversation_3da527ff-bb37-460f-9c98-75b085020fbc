"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { ProductServiceData } from "@/app/(dashboard)/dashboard/business/products/actions";
import { Skeleton } from "@/components/ui/skeleton";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";

interface ProductListItemProps {
  product: ProductServiceData | NearbyProduct;
  isLink?: boolean;
}

// Helper to format currency
const formatCurrency = (amount: number | null | undefined) => {
  if (amount === null || amount === undefined) return null;
  return amount.toLocaleString("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
};

// Animation variants
const cardContainerVariants = {
  hidden: { opacity: 0, y: 10 },
  show: { opacity: 1, y: 0 },
};

const discountBadgeVariants = {
  initial: { scale: 0.9, opacity: 0 },
  animate: {
    scale: 1,
    opacity: 1,
    transition: {
      duration: 0.5,
      type: "spring",
      stiffness: 400,
      damping: 10,
    },
  },
  hover: {
    scale: 1.05,
    rotate: -2,
    transition: { type: "spring", stiffness: 500 },
  },
};

export default function ProductListItem({
  product,
  isLink = true,
}: ProductListItemProps) {
  // If isLink is true, we'll wrap the content in a link
  const [imageError, setImageError] = useState(false);

  const formattedBasePrice = formatCurrency(product.base_price);
  const formattedDiscountedPrice = formatCurrency(product.discounted_price); // Format discounted price

  // Determine final price and if there's a discount shown
  let finalPrice = formattedBasePrice;
  let priceToShowStrikethrough: string | null = null;
  let discountPercentage = 0;

  const hasDiscountedPrice =
    typeof product.discounted_price === "number" &&
    product.discounted_price > 0;
  const hasBasePrice =
    typeof product.base_price === "number" && product.base_price > 0;

  if (
    hasDiscountedPrice &&
    hasBasePrice &&
    product.discounted_price! < product.base_price!
  ) {
    // Scenario 1: Discounted price is valid and less than base price
    finalPrice = formattedDiscountedPrice;
    priceToShowStrikethrough = formattedBasePrice; // Strike through base price
    discountPercentage = Math.round(
      ((product.base_price! - product.discounted_price!) /
        product.base_price!) *
        100
    );
  } else {
    // Scenario 2: No discount applicable, show base price
    finalPrice = formattedBasePrice;
    priceToShowStrikethrough = null;
    discountPercentage = 0;
  }

  // Ensure finalPrice has a fallback if both prices are null/undefined
  if (!finalPrice) {
    finalPrice = "Price unavailable";
  }

  const showDiscountBadge = discountPercentage > 0;

  // State for lazy loading images
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  // Check if product is out of stock
  const isOutOfStock = !product.is_available;

  // Ensure we're not using business_id as a key
  // Use the product's own ID for any keys needed
  const content = (
    <motion.div
      variants={cardContainerVariants}
      initial="hidden"
      animate="show"
      className="w-full overflow-hidden"
    >
      <div className="relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg">
        <div className="relative w-full overflow-hidden rounded-lg">
          {/* Image container */}
          <div className="relative w-full overflow-hidden rounded-t-xl">
            {/* Get the featured image from the images array if available, otherwise fall back to image_url */}
            {/* Determine the image URL to use */}
            {(() => {
              // Get the image URL to display
              let imageUrl = product.image_url;

              // If product has images array and it's not empty, use the featured image
              if (product.images && Array.isArray(product.images) && product.images.length > 0) {
                const featuredIndex = typeof product.featured_image_index === 'number'
                  ? Math.min(product.featured_image_index, product.images.length - 1)
                  : 0;
                imageUrl = product.images[featuredIndex];
              }

              if (imageUrl && !imageError) {
                return (
                  <div className="overflow-hidden">
                    {!isImageLoaded && (
                      <Skeleton className="absolute inset-0 rounded-t-xl" />
                    )}
                    <motion.div className="w-full">
                      <Image
                        src={imageUrl}
                        alt={product.name ?? "Product image"}
                        width={500}
                        height={750}
                        className={`w-full aspect-square object-cover ${
                          isOutOfStock
                            ? "filter grayscale opacity-70 transition-all duration-500"
                            : ""
                        } ${
                          isImageLoaded ? "opacity-100" : "opacity-0"
                        } max-w-full`}
                        loading="lazy"
                        onError={() => setImageError(true)}
                        onLoad={() => setIsImageLoaded(true)}
                        quality={80}
                        blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
                        placeholder="blur"
                        style={{ objectFit: "cover" }}
                      />
                    </motion.div>
                  </div>
                );
              } else {
                return (
                  <div className="w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl">
                    <svg
                      className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                );
              }
            })()}

            {/* Out of Stock Overlay */}
            {isOutOfStock && (
              <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40">
                <div className="px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground">
                  <span className="font-medium tracking-wide uppercase text-xs sm:text-sm">
                    Out of Stock
                  </span>
                </div>
              </div>
            )}

            {/* Discount Badge Overlay */}
            {showDiscountBadge && (
              <AnimatePresence>
                <motion.div
                  key={`discount-badge-${product.id}`}
                  variants={discountBadgeVariants}
                  initial="initial"
                  animate="animate"
                  whileHover="hover"
                  className={cn(
                    "absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg",
                    "bg-destructive",
                    "text-destructive-foreground border border-destructive-foreground/20",
                    "transform-gpu"
                  )}
                >
                  <div className="flex flex-col items-center justify-center">
                    <span className="text-[7px] sm:text-[9px] md:text-[10px] font-medium">
                      SAVE
                    </span>
                    <span className="text-[9px] sm:text-xs md:text-sm leading-none">
                      {discountPercentage}%
                    </span>
                  </div>
                </motion.div>
              </AnimatePresence>
            )}
          </div>

          {/* Content Section */}
          <div className="px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1">
            {/* Title */}
            <p className="font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden">
              {product.name ?? "Unnamed Product"}
            </p>

            {/* Description (optional) */}
            {product.description && (
              <p className="line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate">
                {product.description}
              </p>
            )}

            {/* Price and Badge Container */}
            <div className="flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1">
              {/* Price Group */}
              <div className="flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full">
                {finalPrice && (
                  <p className="truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full">
                    {finalPrice}
                  </p>
                )}
                {priceToShowStrikethrough && (
                  <p className="line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500">
                    {priceToShowStrikethrough}
                  </p>
                )}
              </div>

              {/* Product Type Badge removed as per instructions */}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );

  // If isLink is true, wrap the content in a link to the product detail page
  if (isLink && "business_slug" in product && product.business_slug) {
    return (
      <Link
        href={`/${product.business_slug}/product/${product.slug || product.id}`}
        className="block h-full"
      >
        {content}
      </Link>
    );
  }

  // Otherwise, just return the content
  return content;
}
