import { BusinessSortBy, BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

// Define the view type
export type ViewType = "cards" | "products";

// Define product filter options
export type ProductFilterOption = "all" | "physical" | "service";

// Define product sort options
export type ProductSortOption = "newest" | "price_low" | "price_high" | "name_asc" | "name_desc";

// Define the search result type
export interface LocalitySearchResult {
  location: {
    type: "pincode" | "city";
    value: string;
    locality?: string | null;
  };
  businesses?: BusinessProfilePublicData[] | BusinessCardData[];
  products?: NearbyProduct[];
  isAuthenticated: boolean;
  totalCount: number;
  hasMore: boolean;
  nextPage: number | null;
}

// Define the serializable locality data
export interface SerializableLocality {
  name: string;
  pincode: string;
  divisionName: string;
  district: string;
  stateName: string;
  slug: string;
}

// Define the context type
export type LocalityContextType = {
  // State
  locality: SerializableLocality;
  viewType: ViewType;
  sortBy: BusinessSortBy;
  isSearching: boolean;
  isPending: boolean;
  isLoadingMore: boolean;
  searchError: string | null;
  productFilterBy: ProductFilterOption;
  productSortBy: ProductSortOption;
  searchResult: LocalitySearchResult | null;
  businesses: BusinessProfilePublicData[] | BusinessCardData[];
  products: NearbyProduct[];
  currentPage: number;
  hasMore: boolean;
  totalCount: number;
  isAuthenticated: boolean;

  // Actions
  performSearch: () => Promise<void>;
  handleViewChange: (_newViewType: ViewType) => void;
  handleBusinessSortChange: (_newSortBy: BusinessSortBy) => void;
  handleBusinessSearch: (_businessName: string) => Promise<void>;
  handleProductSearch: (_productName: string) => Promise<void>;
  handleProductSortChange: (_newSortBy: ProductSortOption) => void;
  handleProductFilterChange: (_newFilterBy: ProductFilterOption) => void;
  loadMore: () => Promise<void>;
};
