# Page snapshot

```yaml
- link "Dukancard Your Neighborhood, Digitally Connected":
  - /url: /
- link "Categories":
  - /url: /categories
- link "Discover":
  - /url: /discover
- link "Business Free Listing":
  - /url: /login
- link "Community":
  - /url: /login
- button "Toggle theme":
  - img
  - img
  - text: Toggle theme
- button "Sign In"
- main:
  - img
  - heading "Welcome to Dukancard" [level=1]
  - paragraph: Sign in or create your account with email
  - button "Email OTP"
  - button "Mobile + Password"
  - button "Login with Google":
    - img
    - text: Login with Google
  - text: Or continue with email Email Address
  - textbox "<EMAIL>": <EMAIL>
  - button "Continue":
    - text: Continue
    - img
  - text: New to Dukancard? No worries! We'll create your account automatically.
- contentinfo:
  - heading "Dukan card" [level=2]
  - paragraph: Elevate your business with our premium digital card solution. Connect with customers, showcase your offerings, and grow your brand.
  - heading "Company" [level=3]
  - list:
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Product" [level=3]
  - list:
    - listitem:
      - link "Features":
        - /url: /features
    - listitem:
      - link "Pricing":
        - /url: /pricing
  - heading "Resources" [level=3]
  - list:
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Support":
        - /url: /support
    - listitem:
      - link "Advertise":
        - /url: /advertise
  - heading "Legal" [level=3]
  - list:
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Refund Policy":
        - /url: /refund
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
  - paragraph: © 2025 Dukancard. All rights reserved.
- link "Advertise":
  - /url: /advertise
  - img
  - text: Advertise
- button [disabled]:
  - img
- region "Notifications alt+T"
- alert
- button "Open Next.js Dev Tools":
  - img
```