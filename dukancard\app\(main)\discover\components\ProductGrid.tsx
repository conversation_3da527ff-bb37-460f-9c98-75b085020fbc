"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { NearbyProduct } from "../actions/types";
import ProductListItem from "@/app/components/ProductListItem";
import ProductGridSkeleton from "./ProductGridSkeleton";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter, Search, SortAsc, X } from "lucide-react";
import { ProductSortOption, ProductFilterOption } from "../context/types";

interface ProductGridProps {
  products: NearbyProduct[];
  onSortChange: (_sortBy: ProductSortOption) => void;
  onFilterChange: (_filterBy: ProductFilterOption) => void;
  currentSortBy: ProductSortOption;
  currentFilterBy: ProductFilterOption;
  isLoading: boolean;
  onSearch: (_searchTerm: string) => void;
  initialSearchTerm?: string | null;
}

export default function ProductGrid({
  products,
  onSortChange,
  onFilterChange,
  currentSortBy,
  currentFilterBy,
  isLoading,
  onSearch,
  initialSearchTerm,
}: ProductGridProps) {
  const [searchQuery, setSearchQuery] = useState(initialSearchTerm || "");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(
    initialSearchTerm || ""
  );
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isFirstRender = useRef(true);
  const prevSearchQueryRef = useRef(searchQuery);
  const wasInitiallyPopulated = useRef(!!initialSearchTerm);

  // Update prevSearchQueryRef when component mounts or searchQuery changes
  useEffect(() => {
    // Skip the first update if initialSearchTerm was provided
    if (isFirstRender.current && initialSearchTerm) {
      isFirstRender.current = false;
      wasInitiallyPopulated.current = true;
      return;
    }

    prevSearchQueryRef.current = searchQuery;
  }, [searchQuery, initialSearchTerm]);

  // Prevent infinite loops by tracking if we've already processed the initial search term
  useEffect(() => {
    // This effect runs only once on mount
    if (initialSearchTerm) {
      // Mark that we've already processed the initial search term
      wasInitiallyPopulated.current = true;
      isFirstRender.current = false;
    }
  }, [initialSearchTerm]); // Include initialSearchTerm as a dependency

  // Update isFirstRender when data is loaded
  useEffect(() => {
    // If we have products or isLoading is false, we're no longer on first render
    if (products.length > 0 || !isLoading) {
      isFirstRender.current = false;
    }
  }, [products, isLoading]);

  // Handle input change with manual debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    // Check if the user has manually cleared the search
    const wasManuallyCleared =
      prevSearchQueryRef.current.length > 0 && query === "";

    // If the search was manually cleared, trigger search immediately
    if (wasManuallyCleared) {
      setDebouncedSearchQuery("");
      // Add a small visual delay before triggering the search
      setTimeout(() => {
        onSearch("");
      }, 100);
      return;
    }

    // Only set a new timeout if the query is empty or at least 3 characters
    if (query === "" || query.length >= 3) {
      searchTimeoutRef.current = setTimeout(() => {
        setDebouncedSearchQuery(query);
        // Skip search on initial render if initialSearchTerm was provided
        if (!isFirstRender.current) {
          // Call the server-side search function
          onSearch(query);
        } else {
          isFirstRender.current = false;
        }
      }, 500);
    }
  };

  // Handle clear search when clicking the X button
  const handleClearSearch = () => {
    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    setSearchQuery("");
    setDebouncedSearchQuery("");

    // Add a small visual delay before triggering the search
    setTimeout(() => {
      onSearch("");
    }, 100);
  };

  return (
    <div className="space-y-6 container mx-auto px-4">
      {/* Search and Filter Controls */}
      <motion.div
        className="relative overflow-hidden bg-transparent p-5 rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80 transition-all duration-300"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        whileHover={{ borderColor: "rgba(var(--brand-gold-rgb), 0.3)" }}
      >
        {/* Decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-70">
          <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/5 to-amber-500/5 blur-2xl dark:from-[var(--brand-gold)]/10 dark:to-amber-500/10"></div>
          <div className="absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-gradient-to-tr from-purple-500/5 to-blue-500/5 blur-2xl dark:from-purple-500/10 dark:to-blue-500/10"></div>
        </div>

        <div className="relative z-10 flex flex-col sm:flex-row gap-3 sm:gap-5 items-start sm:items-center justify-between">
          {/* Search Input */}
          <div className="relative w-full sm:w-auto sm:flex-1 max-w-full sm:max-w-md">
            <form
              onSubmit={(e) => {
                e.preventDefault();
                // Only trigger search if query is empty or at least 3 characters
                if (searchQuery === "" || searchQuery.length >= 3) {
                  // Clear any existing timeout
                  if (searchTimeoutRef.current) {
                    clearTimeout(searchTimeoutRef.current);
                    searchTimeoutRef.current = null;
                  }
                  setDebouncedSearchQuery(searchQuery);
                  onSearch(searchQuery);
                }
              }}
            >
              <div className="relative group">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--brand-gold)] transition-all duration-200" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="pl-10 h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm"
                />
                {searchQuery && (
                  <button
                    type="button"
                    onClick={handleClearSearch}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors duration-200"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
                {/* Hidden submit button for form submission */}
                <button type="submit" className="hidden">
                  Search
                </button>
              </div>
            </form>
          </div>

          <div className="flex flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            {/* Filter Dropdown */}
            <Select
              value={currentFilterBy}
              onValueChange={(value) =>
                onFilterChange(value as ProductFilterOption)
              }
              disabled={isLoading}
            >
              <SelectTrigger className="w-full sm:min-w-[140px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm">
                <Filter className="mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]" />
                <SelectValue
                  placeholder="Filter"
                  className="text-xs sm:text-sm"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5">
                    Product Type
                  </SelectLabel>
                  <SelectItem value="all" className="relative pl-8">
                    All Products
                  </SelectItem>
                  <SelectItem value="physical" className="relative pl-8">
                    Physical Items
                  </SelectItem>
                  <SelectItem value="service" className="relative pl-8">
                    Services
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>

            {/* Sort Dropdown */}
            <Select
              value={currentSortBy}
              onValueChange={(value) =>
                onSortChange(value as ProductSortOption)
              }
              disabled={isLoading}
            >
              <SelectTrigger className="w-full sm:min-w-[160px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm">
                <SortAsc className="mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]" />
                <SelectValue
                  placeholder="Sort by"
                  className="text-xs sm:text-sm"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5">
                    Date
                  </SelectLabel>
                  <SelectItem value="newest" className="relative pl-8">
                    Newest First
                  </SelectItem>
                </SelectGroup>
                <SelectGroup>
                  <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">
                    Price
                  </SelectLabel>
                  <SelectItem value="price_low" className="relative pl-8">
                    Price: Low to High
                  </SelectItem>
                  <SelectItem value="price_high" className="relative pl-8">
                    Price: High to Low
                  </SelectItem>
                </SelectGroup>
                <SelectGroup>
                  <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">
                    Name
                  </SelectLabel>
                  <SelectItem value="name_asc" className="relative pl-8">
                    Name: A to Z
                  </SelectItem>
                  <SelectItem value="name_desc" className="relative pl-8">
                    Name: Z to A
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
      </motion.div>

      {/* Results Count */}
      <motion.div
        className="flex items-center justify-between px-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <div className="text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 truncate">
          {debouncedSearchQuery && (
            <span>
              Showing results for{" "}
              <span className="font-medium text-neutral-700 dark:text-neutral-300 max-w-[150px] sm:max-w-none inline-block truncate">
                &quot;{debouncedSearchQuery}&quot;
              </span>
            </span>
          )}
          {currentFilterBy !== "all" && (
            <span>
              {" "}
              •{" "}
              <span className="font-medium text-neutral-700 dark:text-neutral-300">
                {currentFilterBy === "physical" ? "Physical Items" : "Services"}
              </span>
            </span>
          )}
        </div>

        {debouncedSearchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearSearch}
            className="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 h-8 px-2 py-1"
          >
            <X className="h-3.5 w-3.5 mr-1" />
            Clear Search
          </Button>
        )}
      </motion.div>

      {/* Product Grid */}
      {isLoading || (!isLoading && products.length === 0 && isFirstRender.current) ? (
        <ProductGridSkeleton />
      ) : products.length > 0 ? (
        <div
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4"
        >
          {products.map((product, index) => {
            // Use the product's ID as the key
            // Each product has a unique ID in the database
            const uniqueKey = `product-${product.id}`;

            return (
              <motion.div
                key={uniqueKey}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="group"
              >
                {product.business_slug ? (
                  <Link
                    href={`/${product.business_slug}/product/${product.slug || product.id}`}
                    className="block h-full"
                  >
                    <div className="h-full">
                      <ProductListItem product={product} isLink={false} />
                    </div>
                  </Link>
                ) : (
                  <div className="relative h-full">
                    <ProductListItem product={product} isLink={false} />
                    <div className="absolute bottom-0 left-0 right-0 bg-red-500/80 text-white text-xs py-1 px-2 text-center rounded-b-md">
                      Unable to link to business
                    </div>
                  </div>
                )}
              </motion.div>
            );
          })}
        </div>
      ) : (
        <motion.div
          className="text-center py-16 px-4 bg-transparent rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <div className="max-w-md mx-auto">
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-2">
              No Products Found
            </h3>
            <p className="text-neutral-500 dark:text-neutral-400 mb-4">
              We couldn&apos;t find any products
              {debouncedSearchQuery
                ? ` with "${debouncedSearchQuery}" in the name`
                : ""}
              {currentFilterBy !== "all" &&
                ` in the ${
                  currentFilterBy === "physical" ? "Physical Items" : "Services"
                } category`}
              . Try adjusting your search criteria or browse all products.
            </p>
            {debouncedSearchQuery && (
              <Button
                variant="outline"
                className="mt-2 border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10"
                onClick={handleClearSearch}
              >
                <X className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
}
