"use client";

import { useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";

interface AnimatedBackgroundProps {
  className?: string;
}

export default function AnimatedBackground({
  className = "",
}: AnimatedBackgroundProps) {
  const ref = useRef(null);
  const [isClient, setIsClient] = useState(false);

  // Only render on client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Fixed gradient positions for a more stable background
  const gradientPosition = {
    x: 50,
    y: 50,
  };

  // Update isMobile state only on client side
  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    if (typeof window !== "undefined") {
      setIsMobile(window.innerWidth < 768);

      // Add resize listener to update isMobile state
      const handleResize = () => {
        setIsMobile(window.innerWidth < 768);
      };

      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, [isClient]);

  return (
    <div ref={ref} className={`absolute inset-0 overflow-hidden ${className}`}>
      {/* Simple gradient background - optimized blur for mobile */}
      {isClient ? (
        <motion.div
          className="absolute inset-0 opacity-40 dark:opacity-30"
          style={{
            background: `radial-gradient(circle at ${gradientPosition.x}% ${gradientPosition.y}%,
              var(--brand-gold) 0%,
              rgba(var(--brand-gold-rgb), 0.3) 25%,
              rgba(var(--brand-gold-rgb), 0.1) 50%,
              rgba(0, 0, 255, 0.1) 75%,
              rgba(0, 0, 255, 0.05) 100%)`,
            filter: isMobile ? "blur(60px)" : "blur(80px)",
            transformOrigin: "center",
          }}
          animate={{ opacity: 0.4 }}
          transition={{ duration: 2 }}
        />
      ) : (
        // Server-side placeholder with consistent styling
        <div
          className="absolute inset-0 opacity-0"
          style={{
            transformOrigin: "center",
          }}
        />
      )}

      {/* Digital noise texture */}
      <div className="absolute inset-0 bg-[url('/noise.svg')] opacity-5 dark:opacity-10 mix-blend-overlay pointer-events-none" />

      {/* Grid pattern - futuristic grid */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-5 dark:opacity-15 pointer-events-none" />

      {/* Enhanced seamless gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-[var(--brand-gold)]/5 dark:from-neutral-900/5 dark:to-[var(--brand-gold)]/10" />

      {/* Bottom fade-out gradient for seamless transition */}
      <div className="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-t from-background to-transparent" />
    </div>
  );
}
