import { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { Loader2 } from "lucide-react";
import ModernPrivacyPolicyClient from "./ModernPrivacyPolicyClient";

export async function generateMetadata(): Promise<Metadata> {
  const title = "Privacy Policy";
  const description =
    "Learn about <PERSON><PERSON><PERSON>'s privacy policy, how we collect, use, and protect your data, and your rights regarding your personal information.";
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/privacy`;
  const ogImage = `${siteUrl}/opengraph-image.png`; // Default OG image

  return {
    title,
    description,
    alternates: {
      canonical: pageUrl,
    },
    openGraph: {
      title,
      description,
      url: pageUrl,
      siteName: "Dukancard",
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "Dukancard Privacy Policy",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [ogImage],
    },
  };
}

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense
        fallback={
          <div className="min-h-screen flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-[var(--brand-gold)]" />
          </div>
        }
      >
        <ModernPrivacyPolicyClient />
      </Suspense>
    </div>
  );
}
