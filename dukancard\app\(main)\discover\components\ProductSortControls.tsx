"use client";

import { SortAsc, Check } from "lucide-react";
import { motion } from "framer-motion";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ProductSortOption } from "../context/types";
import { useDiscoverContext } from "../context/DiscoverContext";

export default function ProductSortControls() {
  const { productSortBy, handleProductSortChange, isSearching } = useDiscoverContext();

  // Helper function to get human-readable sort name
  const getSortName = (sort: ProductSortOption): string => {
    const sortNames: Record<string, string> = {
      "newest": "Newest First",
      "price_low": "Price (Low to High)",
      "price_high": "Price (High to Low)",
      "name_asc": "Name (A-Z)",
      "name_desc": "Name (Z-A)",
    };
    return sortNames[sort] || "Sort by";
  };

  return (
    <motion.div
      className="flex justify-end"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="inline-flex items-center bg-white dark:bg-neutral-900 px-3 py-2 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800">
        <SortAsc className="h-4 w-4 mr-2 text-[var(--brand-gold)]" />
        <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mr-2">Sort by:</span>
        <Select
          value={productSortBy}
          onValueChange={(value) => handleProductSortChange(value as ProductSortOption)}
          disabled={isSearching}
        >
          <SelectTrigger
            className="min-w-[180px] border-0 bg-transparent p-0 h-auto shadow-none focus:ring-0 focus:ring-offset-0"
          >
            <SelectValue placeholder="Sort by">
              {getSortName(productSortBy)}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5">Date</SelectLabel>
              <SelectItem value="newest" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${productSortBy === 'newest' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Newest First
              </SelectItem>
            </SelectGroup>
            <SelectGroup>
              <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">Price</SelectLabel>
              <SelectItem value="price_low" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${productSortBy === 'price_low' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Price (Low to High)
              </SelectItem>
              <SelectItem value="price_high" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${productSortBy === 'price_high' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Price (High to Low)
              </SelectItem>
            </SelectGroup>
            <SelectGroup>
              <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">Name</SelectLabel>
              <SelectItem value="name_asc" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${productSortBy === 'name_asc' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Name (A-Z)
              </SelectItem>
              <SelectItem value="name_desc" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${productSortBy === 'name_desc' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Name (Z-A)
              </SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </motion.div>
  );
}
