"use client";

import { motion } from "framer-motion";
import BusinessCardPreview from "@/app/(dashboard)/dashboard/business/card/components/BusinessCardPreview";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

interface CardShowcaseProps {
  cardData: BusinessCardData;
  isDemo?: boolean;
}

export default function CardShowcase({
  cardData,
  isDemo = false,
}: CardShowcaseProps) {
  return (
    <motion.div
      className="relative w-full max-w-4xl mx-auto"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      {/* Card container - no borders or edges */}
      <div className="flex justify-center items-center w-full">
        <BusinessCardPreview
          data={cardData}
          userPlan="basic"
          isAuthenticated={false}
          totalLikes={50251}
          totalSubscriptions={32053}
          averageRating={4.8}
          isDemo={isDemo}
        />
      </div>
    </motion.div>
  );
}
