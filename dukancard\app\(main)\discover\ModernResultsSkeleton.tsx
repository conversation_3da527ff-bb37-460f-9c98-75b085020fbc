"use client";

import { Skeleton } from "@/components/ui/skeleton";
import AnimatedBusinessGridSkeleton from "./components/AnimatedBusinessGridSkeleton";

export default function ModernResultsSkeleton() {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      {/* Improved Search Section Skeleton */}
      <div className="w-full py-6 mt-6">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="flex flex-col md:flex-row gap-3 items-start">
              <Skeleton className="h-12 w-full md:w-[140px]" />
              <div className="flex-1 w-full flex flex-col md:flex-row gap-3">
                <Skeleton className="h-12 flex-1" />
                <Skeleton className="h-12 w-full md:w-[200px]" />
                <Skeleton className="h-12 w-full md:w-[120px]" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Carousel Skeleton */}
      <div className="mb-6 container mx-auto px-4">
        <div className="flex items-center justify-between mb-2">
          <Skeleton className="h-5 w-36" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="flex gap-2 overflow-hidden">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-20 w-20 flex-shrink-0 rounded-xl" />
          ))}
        </div>
      </div>

      {/* View Toggle Skeleton */}
      <div className="flex justify-center mb-6">
        <Skeleton className="h-10 w-64 rounded-xl" />
      </div>

      {/* Filter Bar Skeleton */}
      <div className="container mx-auto px-4 mb-4">
        <Skeleton className="h-12 w-full rounded-lg" />
      </div>

      {/* Results Grid Skeleton */}
      <div className="container mx-auto px-4">
        <AnimatedBusinessGridSkeleton />
      </div>
    </div>
  );
}
