import { updateSession } from "@/utils/supabase/middleware";
import { createClient } from "@/utils/supabase/server";
import { NextRequest, NextResponse } from "next/server";

jest.mock("@/utils/supabase/server");

const createMockRequest = (
  pathname: string,
  searchParams: Record<string, string> = {},
  cookies: Record<string, string> = {}
): NextRequest => {
  const url = new URL(`http://localhost${pathname}`);
  Object.entries(searchParams).forEach(([key, value]) => {
    url.searchParams.set(key, value);
  });

  // Create a mock request with proper cookie handling
  const mockCookies = new Map();
  Object.entries(cookies).forEach(([name, value]) => {
    mockCookies.set(name, { name, value });
  });

  // Create mock headers with getSetCookie method
  const mockHeaders = new Headers();
  mockHeaders.getSetCookie = jest.fn(() => []);

  // Add clone method to URL
  const urlWithClone = {
    ...url,
    clone: jest.fn(() => new URL(url.toString())),
  };

  const request = {
    url: url.toString(),
    nextUrl: urlWithClone,
    method: 'GET',
    headers: mockHeaders,
    cookies: {
      get: jest.fn((name: string) => mockCookies.get(name)),
      has: jest.fn((name: string) => mockCookies.has(name)),
      set: jest.fn((name: string, value: string) => mockCookies.set(name, { name, value })),
      delete: jest.fn((name: string) => mockCookies.delete(name)),
      clear: jest.fn(() => mockCookies.clear()),
      getAll: jest.fn(() => Array.from(mockCookies.values())),
      toString: jest.fn(() => ''),
    },
    geo: {},
    ip: '127.0.0.1',
  } as unknown as NextRequest;

  return request;
};

describe("Next.js Middleware Tests", () => {
  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabase = {
      auth: {
        getUser: jest.fn(),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
      }),
    };
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  it("should correctly construct a NextResponse redirect object", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null,
    });
    const request = createMockRequest("/dashboard");
    const response = await updateSession(request);

    expect(response).toBeInstanceOf(NextResponse);
    expect(response.status).toBe(307);
    const location = response.headers.get("location");
    expect(location).toBe("http://localhost/login?next=%2Fdashboard");
  });

  it("should pass the request through NextResponse.next() when no redirect is needed", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null,
    });
    const request = createMockRequest("/login");
    const response = await updateSession(request);

    expect(response).toBeInstanceOf(NextResponse);
    // Check for a property that indicates it's a "next" response, not a redirect
    expect(response.headers.get("location")).toBeNull();
  });

  it("should handle URL search parameters correctly when redirecting", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null,
    });
    const request = createMockRequest("/dashboard", { foo: "bar" });
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    const location = new URL(response.headers.get("location")!);
    expect(location.pathname).toBe("/login");
    expect(location.searchParams.get("next")).toBe("/dashboard");
    // The original search params should not be carried over to the login URL
    expect(location.searchParams.has("foo")).toBe(false);
  });

  it("should redirect to /choose-role when an authenticated user has no profile", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "test-user" } },
      error: null,
    });
    // Both profile checks return null
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    });

    const request = createMockRequest("/some-protected-route");
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("location")).toBe(
      "http://localhost/choose-role"
    );
  });

  it("should not redirect if the user is already on the choose-role page", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "test-user" } },
      error: null,
    });
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    });

    const request = createMockRequest("/choose-role");
    const response = await updateSession(request);

    expect(response.headers.get("location")).toBeNull();
  });

  it("should redirect an existing customer away from the choose-role page", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "test-user" } },
      error: null,
    });
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === "customer_profiles") {
        return {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          maybeSingle: jest
            .fn()
            .mockResolvedValue({ data: { id: "test-user" }, error: null }),
        };
      }
      return {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
      };
    });

    const request = createMockRequest("/choose-role");
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("location")).toBe(
      "http://localhost/dashboard/customer"
    );
  });

  it("should redirect an existing business user away from the choose-role page", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "test-user" } },
      error: null,
    });
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === "business_profiles") {
        return {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          maybeSingle: jest.fn().mockResolvedValue({
            data: { id: "test-user", business_slug: "my-biz" },
            error: null,
          }),
        };
      }
      return {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
      };
    });

    const request = createMockRequest("/choose-role");
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("location")).toBe(
      "http://localhost/dashboard/business"
    );
  });
});