/**
 * Test utilities for dukancard Next.js application
 * Provides common testing helpers, mocks, and data factories
 */

import { AuthError } from '@supabase/supabase-js';

// Test data factories
export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  phone: '+911234567890',
  created_at: '2024-01-01T00:00:00.000Z',
  updated_at: '2024-01-01T00:00:00.000Z',
  email_confirmed_at: '2024-01-01T00:00:00.000Z',
  phone_confirmed_at: '2024-01-01T00:00:00.000Z',
  last_sign_in_at: '2024-01-01T00:00:00.000Z',
  app_metadata: {},
  user_metadata: {},
  aud: 'authenticated',
  role: 'authenticated',
  ...overrides,
});

export const createMockSession = (overrides = {}) => ({
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  expires_in: 3600,
  expires_at: Date.now() / 1000 + 3600,
  token_type: 'bearer',
  user: createMockUser(),
  ...overrides,
});

export const createMockAuthError = (code: string, message?: string): AuthError => ({
  name: 'AuthError',
  message: message || `Mock auth error: ${code}`,
  code,
  status: 400,
  __isAuthError: true,
} as unknown as AuthError);

// Common test data
export const validTestEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const invalidTestEmails = [
  'invalid-email',
  '@domain.com',
  'test@',
  '<EMAIL>',
  '',
];

export const validTestMobileNumbers = [
  '9876543210',
  '8765432109',
  '7654321098',
];

export const invalidTestMobileNumbers = [
  '123456789', // 9 digits
  '12345678901', // 11 digits
  'abcdefghij', // non-numeric
  '0123456789', // starts with 0
  '',
];

export const validTestOTPs = [
  '123456',
  '000000',
  '999999',
];

export const invalidTestOTPs = [
  '12345', // 5 digits
  '1234567', // 7 digits
  'abcdef', // non-numeric
  '12 456', // with space
  '',
];

// Mock implementations for Supabase
export const createMockSupabaseClient = () => ({
  auth: {
    signInWithOtp: jest.fn(),
    verifyOtp: jest.fn(),
    signInWithPassword: jest.fn(),
    signOut: jest.fn(),
    getUser: jest.fn(),
    getSession: jest.fn(),
    onAuthStateChange: jest.fn(() => ({
      data: { subscription: { unsubscribe: jest.fn() } }
    })),
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    maybeSingle: jest.fn(),
    single: jest.fn(),
  })),
});

// Helper functions for testing
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const mockConsoleError = () => {
  const originalError = console.error;
  const mockError = jest.fn();
  console.error = mockError;
  
  return {
    mockError,
    restore: () => {
      console.error = originalError;
    },
  };
};

export const mockConsoleWarn = () => {
  const originalWarn = console.warn;
  const mockWarn = jest.fn();
  console.warn = mockWarn;
  
  return {
    mockWarn,
    restore: () => {
      console.warn = originalWarn;
    },
  };
};

// Form testing helpers
export const fillFormField = async (container: HTMLElement, fieldName: string, value: string) => {
  const field = container.querySelector(`[name="${fieldName}"]`) as HTMLInputElement;
  if (!field) {
    throw new Error(`Field with name "${fieldName}" not found`);
  }
  
  field.value = value;
  field.dispatchEvent(new Event('input', { bubbles: true }));
  field.dispatchEvent(new Event('change', { bubbles: true }));
};

export const submitForm = async (container: HTMLElement, formSelector = 'form') => {
  const form = container.querySelector(formSelector) as HTMLFormElement;
  if (!form) {
    throw new Error(`Form with selector "${formSelector}" not found`);
  }
  
  form.dispatchEvent(new Event('submit', { bubbles: true }));
};

// Assertion helpers
export const expectToastMessage = (mockToast: { success: jest.Mock; error: jest.Mock }, type: 'success' | 'error', message?: string) => {
  expect(mockToast[type]).toHaveBeenCalled();
  if (message) {
    expect(mockToast[type]).toHaveBeenCalledWith(expect.stringContaining(message));
  }
};

export const expectFormValidationError = (container: HTMLElement, expectedError: string) => {
  const errorElement = container.querySelector('[role="alert"]');
  expect(errorElement).toBeInTheDocument();
  expect(errorElement).toHaveTextContent(expectedError);
};

// Test environment setup
export const setupTestEnvironment = () => {
  // Reset all mocks before each test
  jest.clearAllMocks();
  
  // Mock window.location
  Object.defineProperty(window, 'location', {
    writable: true,
    value: {
      href: 'http://localhost:3000',
      origin: 'http://localhost:3000',
      pathname: '/login',
    search: '',
    hash: '',
    assign: jest.fn(),
    replace: jest.fn(),
    reload: jest.fn(),
    }
  });
  
  // Mock localStorage
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });
  
  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
  });
  
  return {
    localStorage: localStorageMock,
    sessionStorage: sessionStorageMock,
  };
};

// Network request mocking helpers
export const mockSuccessfulOTPSend = (mockSupabase: { auth: { signInWithOtp: jest.Mock } }) => {
  mockSupabase.auth.signInWithOtp.mockResolvedValue({
    data: {},
    error: null,
  });
};

export const mockFailedOTPSend = (mockSupabase: { auth: { signInWithOtp: jest.Mock } }, error: AuthError) => {
  mockSupabase.auth.signInWithOtp.mockResolvedValue({
    data: null,
    error,
  });
};

export const mockSuccessfulOTPVerify = (mockSupabase: { auth: { verifyOtp: jest.Mock } }, user = createMockUser()) => {
  mockSupabase.auth.verifyOtp.mockResolvedValue({
    data: {
      user,
      session: createMockSession({ user }),
    },
    error: null,
  });
};

export const mockFailedOTPVerify = (mockSupabase: { auth: { verifyOtp: jest.Mock } }, error: AuthError) => {
  mockSupabase.auth.verifyOtp.mockResolvedValue({
    data: null,
    error,
  });
};

export const mockSuccessfulPasswordLogin = (mockSupabase: { auth: { signInWithPassword: jest.Mock } }, user = createMockUser()) => {
  mockSupabase.auth.signInWithPassword.mockResolvedValue({
    data: {
      user,
      session: createMockSession({ user }),
    },
    error: null,
  });
};

export const mockFailedPasswordLogin = (mockSupabase: { auth: { signInWithPassword: jest.Mock } }, error: AuthError) => {
  mockSupabase.auth.signInWithPassword.mockResolvedValue({
    data: null,
    error,
  });
};
