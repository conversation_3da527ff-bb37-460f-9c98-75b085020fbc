"use client";

import { useState, useRef, useTransition } from "react";
import { User, Save, Loader2, MapPin } from "lucide-react";
import { ProfileForm, type ProfileFormRef } from "../ProfileForm";
import AddressForm, { type AddressFormRef } from "./AddressForm";
import AvatarUpload from "./AvatarUpload";
import ProfileRequirementDialog from "./ProfileRequirementDialog";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { updateCustomerProfileAndAddress, type UnifiedProfileFormState } from "../actions";

interface ProfilePageClientProps {
  initialName: string | null;
  initialAvatarUrl?: string | null;
  initialAddressData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  } | null;
  hasCompleteAddress?: boolean;
}

export default function ProfilePageClient({
  initialName,
  initialAvatarUrl,
  initialAddressData,
  hasCompleteAddress = false,
}: ProfilePageClientProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(initialAvatarUrl || undefined);
  const profileFormRef = useRef<ProfileFormRef>(null);
  const addressFormRef = useRef<AddressFormRef>(null);
  const [isPending, startTransition] = useTransition();
  const [formState, setFormState] = useState<UnifiedProfileFormState>({
    message: null,
    errors: {},
    success: false
  });

  const handleUnifiedSubmit = async () => {
    // Get data from both forms
    const profileData = profileFormRef.current?.getFormData();
    const addressData = addressFormRef.current?.getFormData();

    // Validate profile form (required)
    const isProfileValid = profileFormRef.current?.validateForm() ?? false;
    if (!isProfileValid) {
      toast.error('Please check your profile information. Name is required.');
      return;
    }

    // Validate address form (optional)
    const isAddressValid = addressFormRef.current?.validateForm() ?? true; // Address is optional

    // Double-check if name is provided (required) - this should now work correctly
    if (!profileData?.name?.trim()) {
      toast.error('Name is required');
      return;
    }

    // If address data is partially filled, validate that required fields are present
    const hasAnyAddressField = addressData && (
      addressData.pincode || addressData.city || addressData.state || addressData.locality
    );

    if (hasAnyAddressField && !isAddressValid) {
      toast.error('Please complete all required address fields or leave them empty');
      return;
    }

    // Create FormData for submission
    const formData = new FormData();
    formData.append('name', profileData.name);

    // Add address data if provided
    if (addressData) {
      formData.append('address', addressData.address || '');
      formData.append('pincode', addressData.pincode || '');
      formData.append('city', addressData.city || '');
      formData.append('state', addressData.state || '');
      formData.append('locality', addressData.locality || '');
    }

    // Submit the unified form
    startTransition(async () => {
      try {
        const initialState: UnifiedProfileFormState = {
          message: null,
          errors: {},
          success: false
        };

        const result = await updateCustomerProfileAndAddress(initialState, formData);
        setFormState(result);

        if (result.success) {
          toast.success(result.message || 'Profile updated successfully!');
        } else {
          toast.error(result.message || 'Failed to update profile');
        }
      } catch (error) {
        console.error('Error submitting unified form:', error);
        const errorState: UnifiedProfileFormState = {
          message: 'An unexpected error occurred. Please try again.',
          success: false,
          errors: {}
        };
        setFormState(errorState);
        toast.error('An unexpected error occurred. Please try again.');
      }
    });
  };

  return (
    <>
      {/* Profile Requirement Dialog */}
      <ProfileRequirementDialog
        hasCompleteAddress={hasCompleteAddress}
      />

      <div className="space-y-8">
        {/* Header Section */}
        <div className="flex items-center gap-4">
          <div className="p-3 rounded-xl bg-muted hidden sm:block">
            <User className="w-6 h-6 text-foreground" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">
              Profile Information
            </h1>
            <p className="text-muted-foreground mt-1">
              Update your personal details and address
            </p>
          </div>
        </div>

        {/* Content Section */}
        <div className="space-y-8">
          {/* Avatar Upload Section */}
          <div className="flex justify-center">
            <AvatarUpload
              initialAvatarUrl={avatarUrl}
              userName={initialName}
              onUpdateAvatar={(url) => setAvatarUrl(url)}
            />
          </div>

          {/* Profile Information Section */}
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-muted">
                <User className="w-5 h-5 text-foreground" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-foreground">
                  Personal Information
                </h2>
                <p className="text-sm text-muted-foreground">
                  Update your name and personal details
                </p>
              </div>
            </div>

            <ProfileForm
              ref={profileFormRef}
              initialName={initialName}
              hideSubmitButton={true}
            />
          </div>

          {/* Address Information Section */}
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-muted">
                <MapPin className="w-5 h-5 text-foreground" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-foreground">
                  Address Information
                </h2>
                <p className="text-sm text-muted-foreground">
                  Update your address details (optional)
                </p>
              </div>
            </div>

            <AddressForm
              ref={addressFormRef}
              initialData={initialAddressData || undefined}
              hideSubmitButton={true}
            />
          </div>

          {/* Unified Submit Button */}
          <div className="flex justify-center pt-6 pb-8 border-t">
            <Button
              onClick={handleUnifiedSubmit}
              disabled={isPending}
              size="lg"
              className="bg-primary hover:bg-primary/90 text-primary-foreground px-8"
            >
              {isPending ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Saving Changes...
                </>
              ) : (
                <>
                  <Save className="w-5 h-5 mr-2" />
                  Save Profile
                </>
              )}
            </Button>
          </div>

          {/* Error Display */}
          {formState.message && !formState.success && (
            <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-sm text-destructive">{formState.message}</p>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
