import { apiCall, api, handleApiError, safeApiCall } from '@/src/utils/apiClient';
import { supabase } from '@/lib/supabase';
import { BACKEND_CONFIG } from '@/src/config/publicKeys';

// Mock dependencies
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
    },
  },
}));

global.fetch = jest.fn();

describe('apiClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: { access_token: 'test-token' } },
      error: null,
    });
  });

  describe('apiCall', () => {
    it('should make a successful GET request', async () => {
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: { message: 'Success' } }),
      });

      const response = await api.get('/test');
      expect(response.data).toEqual({ message: 'Success' });
      expect(fetch).toHaveBeenCalledWith(
        `${BACKEND_CONFIG.baseUrl}${BACKEND_CONFIG.apiPrefix}/test`,
        expect.any(Object)
      );
    });

    it('should handle authentication errors', async () => {
      (supabase.auth.getSession as jest.Mock).mockResolvedValue({
        data: { session: null },
        error: { message: 'Auth error' },
      });

      const response = await api.get('/test');
      expect(response.success).toBe(false);
      expect(response.error).toBe('Authentication required. Please log in again.');
    });

    it('should handle network errors', async () => {
        (fetch as jest.Mock).mockRejectedValue(new Error('Network failed'));
  
        const response = await api.get('/test');
        expect(response.success).toBe(false);
        expect(response.error).toContain('Network error');
      });
  
      it('should handle timeouts', async () => {
        (fetch as jest.Mock).mockRejectedValue({ name: 'AbortError' });
  
        const response = await api.get('/test', { timeout: 1 });
        expect(response.success).toBe(false);
        expect(response.error).toBe('Request timeout. Please try again.');
      });
  });

  describe('handleApiError', () => {
    it('should throw an error for unsuccessful responses', () => {
      expect(() => handleApiError({ success: false, error: 'Test error' })).toThrow('Test error');
    });

    it('should return data for successful responses', () => {
      const data = { id: 1 };
      expect(handleApiError({ success: true, data })).toBe(data);
    });
  });

  describe('safeApiCall', () => {
    it('should return data on success', async () => {
        (fetch as jest.Mock).mockResolvedValue({
            ok: true,
            json: () => Promise.resolve({ success: true, data: { message: 'Success' } }),
          });
      const data = await safeApiCall('/test');
      expect(data).toEqual({ message: 'Success' });
    });

    it('should throw on failure', async () => {
        (fetch as jest.Mock).mockResolvedValue({
            ok: false,
            status: 500,
            statusText: 'Server Error',
            json: () => Promise.resolve({ success: false, error: 'Server error' }),
          });
      await expect(safeApiCall('/test')).rejects.toThrow('Server error');
    });
  });
});