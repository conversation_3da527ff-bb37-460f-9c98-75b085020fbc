import { NextRequest, NextResponse } from "next/server";
import { updateWebhook, UpdateWebhookParams } from "@/lib/razorpay/services/webhook";

/**
 * API route to update a Razorpay webhook
 *
 * This endpoint updates an existing webhook for a Razorpay account.
 *
 * Request body:
 * {
 *   "account_id": "acc_H3kYHQ635sBwXG",
 *   "webhook_id": "HK890egfiItP3H",
 *   "url": "https://www.linkedin.com",
 *   "events": [
 *     "refund.created"
 *   ]
 * }
 *
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "id": "HK890egfiItP3H",
 *     "created_at": **********,
 *     "updated_at": **********,
 *     "service": "beta-api-test",
 *     "owner_id": "H3kYHQ635sBwXG",
 *     "owner_type": "merchant",
 *     "context": [],
 *     "disabled_at": 0,
 *     "url": "https://www.linkedin.com",
 *     "alert_email": "<EMAIL>",
 *     "secret_exists": true,
 *     "entity": "webhook",
 *     "active": true,
 *     "events": [
 *       "refund.created"
 *     ]
 *   }
 * }
 */
export async function PATCH(req: NextRequest) {
  try {
    // Get the request body
    const body = await req.json();

    // Extract parameters
    const {
      account_id,
      webhook_id,
      url,
      events
    } = body;

    // Validate required parameters
    if (!account_id) {
      return NextResponse.json(
        { success: false, message: "Missing account_id parameter" },
        { status: 400 }
      );
    }

    if (!webhook_id) {
      return NextResponse.json(
        { success: false, message: "Missing webhook_id parameter" },
        { status: 400 }
      );
    }

    if (!url) {
      return NextResponse.json(
        { success: false, message: "Missing url parameter" },
        { status: 400 }
      );
    }

    if (!events || !Array.isArray(events) || events.length === 0) {
      return NextResponse.json(
        { success: false, message: "Missing or invalid events parameter" },
        { status: 400 }
      );
    }

    // Create update parameters
    const updateParams: UpdateWebhookParams = {
      url,
      events
    };

    // Update the webhook
    const result = await updateWebhook(account_id, webhook_id, updateParams);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return success response
    return NextResponse.json(
      { success: true, data: result.data },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK_UPDATE] Error updating webhook:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Error updating webhook",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
