"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Mail, Phone, MapPin, MessageSquare, HelpCircle, Send } from "lucide-react";

export default function ContactAnimatedBackground() {
  const [isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    setIsClient(true);
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Generate random positions for the floating elements
  const generateElements = () => {
    const elements = [];
    const icons = [Mail, Phone, MapPin, MessageSquare, HelpCircle, Send];
    
    for (let i = 0; i < 12; i++) {
      const IconComponent = icons[i % icons.length];
      
      elements.push({
        id: i,
        icon: <IconComponent />,
        posX: Math.random() * 100,
        posY: Math.random() * 100,
        size: Math.random() * 10 + 15,
        opacity: Math.random() * 0.07 + 0.03,
        duration: Math.random() * 20 + 15,
        delay: Math.random() * 5,
        moveX: (Math.random() - 0.5) * 30,
        moveY: (Math.random() - 0.5) * 30,
        rotate: Math.random() * 360,
        rotateDirection: (Math.random() - 0.5) * 360,
      });
    }
    
    return elements;
  };

  // Generate elements only on client-side
  const elements = isClient ? generateElements() : [];

  return (
    <div className="absolute inset-0 w-full h-full overflow-hidden pointer-events-none">
      {/* Background gradient blobs */}
      <motion.div
        className="absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold)]/5 blur-3xl dark:bg-[var(--brand-gold)]/10"
        initial={{ opacity: 0.5 }}
        animate={{ opacity: 0.7 }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      />

      <motion.div
        className="absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10"
        initial={{ opacity: 0.5 }}
        animate={{ opacity: 0.7 }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1
        }}
      />

      <motion.div
        className="absolute bottom-0 left-1/3 w-[400px] h-[400px] rounded-full bg-purple-500/5 blur-3xl dark:bg-purple-500/10"
        initial={{ opacity: 0.5 }}
        animate={{ opacity: 0.7 }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 2
        }}
      />

      {/* Floating icons */}
      {isClient && elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute text-[var(--brand-gold)]"
          style={{
            left: `${element.posX}%`,
            top: `${element.posY}%`,
            opacity: element.opacity,
            fontSize: `${isMobile ? element.size * 0.7 : element.size}px`,
          }}
          initial={{ 
            rotate: element.rotate,
            x: 0,
            y: 0,
          }}
          animate={{ 
            rotate: element.rotate + element.rotateDirection,
            x: element.moveX,
            y: element.moveY,
          }}
          transition={{
            rotate: {
              duration: element.duration,
              repeat: Infinity,
              ease: "linear",
            },
            x: {
              duration: element.duration / 2,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
              delay: element.delay,
            },
            y: {
              duration: element.duration / 3,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
              delay: element.delay + 2,
            },
          }}
        >
          {element.icon}
        </motion.div>
      ))}

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-repeat opacity-[0.015] dark:opacity-[0.03]" />
    </div>
  );
}
