import React from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { CreditCard, ChevronDown, Check, CheckCircle2 } from "lucide-react";
import { onboardingPlans } from "@/lib/PricingPlans";
import { StepComponentProps } from "../../types/onboarding";

export function PlanSelectionStep({ 
  form, 
  isSubmitting, 
  existingData, 
  selectedPlan, 
  setSelectedPlan, 
  showPlans, 
  setShowPlans 
}: StepComponentProps) {
  return (
    <>
      <FormField
        control={form.control}
        name="planId"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <CreditCard className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Select Plan
            </FormLabel>
            <FormControl>
              <div>
                <button
                  type="button"
                  onClick={() => {
                    if (!isSubmitting && !existingData?.hasExistingSubscription && setShowPlans) {
                      setShowPlans(!showPlans);
                    }
                  }}
                  className={`flex items-center justify-between w-full border rounded-lg px-4 py-3 text-left h-12 transition-all ${
                    existingData?.hasExistingSubscription
                      ? "bg-muted/50 border-border text-foreground dark:bg-neutral-800/30 dark:border-neutral-700 cursor-not-allowed opacity-75"
                      : "cursor-pointer bg-background border-border hover:border-primary hover:bg-background/80 dark:bg-neutral-800/50 dark:border-neutral-700 dark:hover:border-[var(--brand-gold)] dark:hover:bg-neutral-800/80 text-foreground dark:text-white"
                  } ${isSubmitting ? "opacity-50" : ""}`}
                  disabled={isSubmitting || existingData?.hasExistingSubscription}
                >
                  <span className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5 text-muted-foreground dark:text-neutral-400" />
                    {selectedPlan ? selectedPlan.name : "Select a plan"}
                    {existingData?.hasExistingSubscription && (
                      <span className="text-xs text-muted-foreground dark:text-neutral-400">(Current Plan)</span>
                    )}
                  </span>
                  {!existingData?.hasExistingSubscription && (
                    <ChevronDown className={`w-5 h-5 text-muted-foreground dark:text-neutral-400 transition-transform ${showPlans ? "rotate-180" : ""}`} />
                  )}
                </button>
                <input type="hidden" {...field} value={selectedPlan?.id || ""} />
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {showPlans && !existingData?.hasExistingSubscription && (
        <div className="bg-popover border border-border dark:bg-neutral-900 dark:border-[var(--brand-gold)]/30 rounded-lg shadow-lg overflow-hidden">
          {onboardingPlans.map((plan, index, arr) => {
            const isDisabled = !plan.available;
            return (
              <React.Fragment key={plan.id}>
                <div
                  onClick={() => {
                    if (isDisabled || !setSelectedPlan || !setShowPlans) return;
                    setSelectedPlan(plan);
                    form.setValue("planId", plan.id, { shouldValidate: true });
                    setShowPlans(false);
                  }}
                  aria-disabled={isDisabled}
                  tabIndex={isDisabled ? -1 : 0}
                  className={`p-4 transition-colors flex flex-col gap-1
                    ${isDisabled
                      ? "opacity-60 cursor-not-allowed bg-muted dark:bg-neutral-800"
                      : "cursor-pointer hover:bg-muted dark:hover:bg-neutral-800"}
                    ${plan.recommended ? "border-l-4 border-primary dark:border-[var(--brand-gold)]" : ""}
                    ${selectedPlan?.id === plan.id ? "bg-muted dark:bg-neutral-800" : ""}
                  `}
                  title={isDisabled ? "This plan is coming soon and cannot be selected." : ""}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="font-medium text-popover-foreground dark:text-white">
                        {plan.name}
                        {isDisabled && (
                          <span className="ml-2 text-xs text-muted-foreground dark:text-neutral-400 font-semibold">(Coming Soon)</span>
                        )}
                      </h3>
                      <p className="text-sm text-muted-foreground dark:text-neutral-400">{plan.price}</p>
                    </div>
                    {selectedPlan?.id === plan.id && !isDisabled && (
                      <div className="w-6 h-6 rounded-full bg-primary dark:bg-[var(--brand-gold)] flex items-center justify-center">
                        <Check className="w-4 h-4 text-white dark:text-black" />
                      </div>
                    )}
                    {plan.recommended && (
                      <span className="text-xs bg-primary/10 text-primary dark:bg-[var(--brand-gold)]/20 dark:text-[var(--brand-gold)] px-3 py-1 rounded-full font-medium">
                        Recommended
                      </span>
                    )}
                  </div>
                </div>
                {index < arr.length - 1 && <Separator className="bg-border dark:bg-[var(--brand-gold)]/20" />}
              </React.Fragment>
            );
          })}
        </div>
      )}

      {/* Show notification if user already has a subscription */}
      {existingData?.hasExistingSubscription && selectedPlan && (
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center">
              <CheckCircle2 className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                You&apos;re already subscribed to {selectedPlan.name}
              </h4>
              <p className="text-xs text-blue-700 dark:text-blue-300">
                Your current plan will continue as usual. You can manage your subscription from the dashboard.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="p-5 bg-muted/50 border border-border dark:bg-neutral-900/50 dark:border-[var(--brand-gold)]/20 rounded-lg">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-full bg-primary/10 dark:bg-[var(--brand-gold)]/10 flex items-center justify-center">
            <Check className="w-6 h-6 text-primary dark:text-[var(--brand-gold)]" />
          </div>
          <div>
            <h3 className="font-medium text-foreground dark:text-white text-lg">
              {existingData?.hasExistingSubscription ? (
                "Continue with Current Plan"
              ) : selectedPlan?.id === "free" ? (
                "Free Forever Plan"
              ) : (
                "1 Month Free Trial"
              )}
            </h3>
            <p className="text-sm text-muted-foreground dark:text-neutral-400">
              {existingData?.hasExistingSubscription ? (
                "Complete your profile setup to access all features of your current plan."
              ) : selectedPlan?.id === "free" ? (
                "Get your business online instantly with our free plan. Upgrade anytime."
              ) : (
                "Start your 30-day free trial of premium features by completing the setup."
              )}
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
