"use client";

import { motion, AnimatePresence } from "framer-motion";

import { ProductsProvider } from "./context/ProductsContext";
import { containerVariants } from "./types";
import { ProductWithVariantInfo } from "@/types/products";
import {
  ProductHeader,
  ProductFilters,
  ProductStats,
  ProductViewToggle,
  ProductTable,
  ProductGrid,
  ProductDeleteDialog,
  ProductLoadingState,
  ProductPagination
} from "./components/product-ui";

interface ProductsClientProps {
  initialData: ProductWithVariantInfo[];
  initialCount: number;
  planLimit: number;
  error?: string;
}

export default function ProductsClient({
  initialData,
  initialCount,
  planLimit,
  error,
}: ProductsClientProps) {
  return (
    <ProductsProvider
      initialData={initialData}
      initialCount={initialCount}
      planLimit={planLimit}
      initialError={error}
    >
      <ProductsClientContent />
    </ProductsProvider>
  );
}

function ProductsClientContent() {
  const {
    viewType,
    isLoading,
    isInitialLoading,
    currentPage,
    totalPages,
    totalCount,
    itemsPerPage,
    goToPage,
    setItemsPerPage,
    products
  } = useProducts();



  return (
    <motion.div
      className="space-y-8"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Product Header - Full Width */}
      <motion.div variants={containerVariants}>
        <ProductHeader />
      </motion.div>

      {/* Stats Summary - Full Width */}
      <motion.div variants={containerVariants}>
        <ProductStats />
      </motion.div>

      {/* Action Bar - Full Width */}
      <motion.div variants={containerVariants}>
        <ProductFilters />
      </motion.div>

      {/* Count and View Toggle - Full Width */}
      <motion.div variants={containerVariants}>
        <ProductViewToggle />
      </motion.div>

      {/* Products List - Full Width */}
      <motion.div variants={containerVariants}>
        {isInitialLoading || isLoading ? (
          <ProductLoadingState view={viewType} count={itemsPerPage} />
        ) : (
          <AnimatePresence mode="wait">
            {viewType === "table" ? (
              <ProductTable key="table" />
            ) : (
              <ProductGrid key="grid" />
            )}
          </AnimatePresence>
        )}

        {/* Pagination */}
        {products.length > 0 && (
          <ProductPagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalCount}
            itemsPerPage={itemsPerPage}
            onPageChange={goToPage}
            onItemsPerPageChange={setItemsPerPage}
            isLoading={isLoading}
            className="mt-8"
          />
        )}
      </motion.div>

      {/* Delete Confirmation Dialog */}
      <ProductDeleteDialog />
    </motion.div>
  );
}

// Import at the end to avoid circular dependencies
import { useProducts } from "./context/ProductsContext";
