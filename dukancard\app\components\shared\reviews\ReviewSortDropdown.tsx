"use client";

import React from "react";
import { motion } from "framer-motion";
import { SortAsc } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export type ReviewSortOption =
  | "newest"
  | "oldest"
  | "highest_rating"
  | "lowest_rating";

interface ReviewSortDropdownProps {
  sortBy: ReviewSortOption;
  onSortChange: (_sortBy: ReviewSortOption) => void;
  className?: string;
}

const sortOptions = [
  { value: "newest" as const, label: "Newest First" },
  { value: "oldest" as const, label: "Oldest First" },
  { value: "highest_rating" as const, label: "High to Low Ratings" },
  { value: "lowest_rating" as const, label: "Low to High Ratings" },
];

export default function ReviewSortDropdown({
  sortBy,
  onSortChange,
  className = "",
}: ReviewSortDropdownProps) {
  const currentSortLabel = sortOptions.find(option => option.value === sortBy)?.label || "Newest First";

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.2 }}
      className={className}
    >
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="gap-1.5 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200"
          >
            <SortAsc className="h-4 w-4" />
            <span>Sort: </span>
            <span className="font-medium text-[var(--brand-gold)]">
              {currentSortLabel}
            </span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48 p-1">
          {sortOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() => onSortChange(option.value)}
              className={`cursor-pointer transition-colors duration-200 ${
                sortBy === option.value
                  ? "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] font-medium"
                  : "hover:bg-neutral-100 dark:hover:bg-neutral-800"
              }`}
            >
              {option.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </motion.div>
  );
}
