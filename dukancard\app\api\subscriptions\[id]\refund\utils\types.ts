export interface PaymentSubscription {
  id: string;
  business_profile_id: string;
  razorpay_subscription_id: string | null;
  subscription_status: string;
  plan_id: string;
  plan_cycle: string;
  last_payment_id?: string | null;
  cancellation_requested_at?: string | null;
}

export interface BusinessProfile {
  id: string;
  has_active_subscription: boolean;
}

export interface RefundData {
  id: string;
  entity: string;
  amount: number;
  currency: string;
  payment_id: string;
  notes: Record<string, unknown>;
  receipt: string | null;
  acquirer_data: {
    arn: string | null;
  };
  created_at: number;
  batch_id: string | null;
  status: string;
  speed_processed: string;
  speed_requested: string;
}

export interface RequestDetails {
  url: string;
  body: Record<string, unknown>;
}
