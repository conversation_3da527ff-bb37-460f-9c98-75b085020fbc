import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Input, InputProps } from '@/src/components/ui/Input';
import { Text, View } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';

// Mock the useTheme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    isDark: false,
    colors: {
      textPrimary: '#000000',
      textSecondary: '#666666',
      error: '#ff0000',
      border: '#cccccc',
      background: '#ffffff',
    },
    spacing: {
      xs: 4,
      sm: 8,
    },
    typography: {
      fontSize: {
        sm: 12,
        base: 14,
      },
    },
    brandColors: {
      gold: '#C29D5B',
    },
  }),
}));

const mockOnChangeText = jest.fn();

const defaultProps: InputProps = {
  label: 'Test Label',
  placeholder: 'Test Placeholder',
  onChangeText: mockOnChangeText,
};

const renderComponent = (props: Partial<InputProps> = {}) => {
  return render(<Input {...defaultProps} {...props} />);
};

describe('<Input />', () => {
  beforeEach(() => {
    mockOnChangeText.mockClear();
  });

  it('renders correctly with default props', () => {
    const { getByText, getByPlaceholderText, toJSON } = renderComponent();
    expect(getByText('Test Label')).toBeTruthy();
    expect(getByPlaceholderText('Test Placeholder')).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it('calls onChangeText when text is entered', () => {
    const { getByPlaceholderText } = renderComponent();
    fireEvent.changeText(getByPlaceholderText('Test Placeholder'), 'hello');
    expect(mockOnChangeText).toHaveBeenCalledWith('hello');
  });

  it('displays an error message', () => {
    const { getByText, toJSON } = renderComponent({ error: 'Test Error' });
    expect(getByText('Test Error')).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it('displays helper text when no error is present', () => {
    const { getByText, queryByText } = renderComponent({ helperText: 'Helper text' });
    expect(getByText('Helper text')).toBeTruthy();
    expect(queryByText('Test Error')).toBeNull();
  });

  it('does not display helper text when an error is present', () => {
    const { queryByText } = renderComponent({
      helperText: 'Helper text',
      error: 'Test Error',
    });
    expect(queryByText('Helper text')).toBeNull();
  });

  it('renders with left and right icons', () => {
    const LeftIcon = () => <View testID="left-icon" />;
    const RightIcon = () => <View testID="right-icon" />;
    const { getByTestId } = renderComponent({
      leftIcon: <LeftIcon />,
      rightIcon: <RightIcon />,
    });
    expect(getByTestId('left-icon')).toBeTruthy();
    expect(getByTestId('right-icon')).toBeTruthy();
  });

  it('displays an asterisk when isRequired is true', () => {
    const { getByText } = renderComponent({ isRequired: true });
    expect(getByText('*')).toBeTruthy();
  });

  describe('password input', () => {
    it('toggles password visibility', () => {
      const { getByTestId, getByPlaceholderText } = renderComponent({ type: 'password' });
      const input = getByPlaceholderText('Test Placeholder');
      const toggle = getByTestId('password-visibility-toggle');

      // Initially secure
      expect(input.props.secureTextEntry).toBe(true);

      // Press to reveal
      fireEvent.press(toggle);
      expect(input.props.secureTextEntry).toBe(false);

      // Press to hide again
      fireEvent.press(toggle);
      expect(input.props.secureTextEntry).toBe(true);
    });
  });

  describe('keyboard types', () => {
    it('sets email keyboard type', () => {
      const { getByPlaceholderText } = renderComponent({ type: 'email' });
      expect(getByPlaceholderText('Test Placeholder').props.keyboardType).toBe('email-address');
    });

    it('sets phone keyboard type', () => {
      const { getByPlaceholderText } = renderComponent({ type: 'phone' });
      expect(getByPlaceholderText('Test Placeholder').props.keyboardType).toBe('phone-pad');
    });

    it('sets numeric keyboard type', () => {
      const { getByPlaceholderText } = renderComponent({ type: 'numeric' });
      expect(getByPlaceholderText('Test Placeholder').props.keyboardType).toBe('numeric');
    });
  });

  it('handles focus and blur events', () => {
    const { getByPlaceholderText, toJSON } = renderComponent();
    const input = getByPlaceholderText('Test Placeholder');

    fireEvent(input, 'focus');
    // Snapshot after focus
    expect(toJSON()).toMatchSnapshot('focused');

    fireEvent(input, 'blur');
    // Snapshot after blur
    expect(toJSON()).toMatchSnapshot('blurred');
  });
});