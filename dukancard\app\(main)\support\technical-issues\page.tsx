import TechnicalIssuesClient from "./TechnicalIssuesClient";
import { Metadata } from "next";
import { siteConfig } from "@/lib/site-config";

export async function generateMetadata(): Promise<Metadata> {
  const title = `Technical Issues`;
  const description =
    "Get help with technical problems and troubleshooting for your Dukancard digital business card. Find solutions to common issues with login, card display, image uploads, and more.";
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || siteConfig.url;
  const pageUrl = `${siteUrl}/support/technical-issues`;
  const ogImage = `${siteUrl}/opengraph-image.png`;

  return {
    title,
    description,
    keywords: [
      "Dukancard troubleshooting",
      "technical support",
      "login issues",
      "image upload problems",
      "card not displaying",
      "error messages",
    ],
    alternates: {
      canonical: "/support/technical-issues",
    },
    openGraph: {
      title,
      description,
      url: pageUrl,
      siteName: siteConfig.name,
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: `${siteConfig.name} Technical Support`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [ogImage],
    },
    // Add WebPage Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: siteConfig.name,
          url: siteUrl,
        },
      }),
    },
  };
}

export default function TechnicalIssuesPage() {
  return <TechnicalIssuesClient />;
}
