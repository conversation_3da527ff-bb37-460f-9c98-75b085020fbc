"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card"; // Keep Card import for structure
import {
  Check,
  Clock,
  CreditCard,
  Store,
  Users,
  ShieldCheck,
  AlertCircle,
  Loader2,
  ChevronDown,
  ChevronUp,
  Package,
  Zap,
  Sparkles,
  Gift,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { PricingPlan } from "@/lib/PricingPlans";
import { cn } from "@/lib/utils";
import { usePricingCard } from "./PricingCardContext";

interface PricingCardProps {
  plan: PricingPlan;
  onButtonClick?: (_plan: PricingPlan) => void; // Prefixed 'plan' parameter in type signature
  index: number; // Keep index for animation delay if needed
  isLoading?: boolean; // Keep for button state
  isCurrentPlan?: boolean; // Keep for button state/text
  buttonTextOverride?: string; // Keep for flexibility
}

// Animation variant matching LandingPageClient
const itemFadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { duration: 0.5, delay: i * 0.1, ease: "easeOut" },
  }),
};

// Helper function to get the correct icon based on plan name
const getPlanIcon = (planName: string, featured: boolean) => {
  const isEnterprise = planName === "Enterprise Plan";
  const iconProps = {
    className: cn(
      "w-5 h-5",
      featured
        ? "text-[var(--brand-gold)]"
        : isEnterprise
        ? "text-purple-600"
        : "text-muted-foreground"
    ),
  };
  switch (planName) {
    case "Free Plan":
      return <Gift {...iconProps} />;
    case "Basic Plan":
      return <CreditCard {...iconProps} />;
    case "Growth Plan":
      return <Store {...iconProps} />;
    case "Pro Plan":
      return <ShieldCheck {...iconProps} />;
    case "Enterprise Plan":
      return <Users {...iconProps} />;
    default:
      return null;
  }
};

// Helper function to get previous plan name
const getPreviousPlanName = (planName: string): string | null => {
  switch (planName) {
    case "Growth Plan":
      return "Basic";
    case "Pro Plan":
      return "Growth";
    case "Enterprise Plan":
      return "Pro";
    default:
      return null;
  }
};

// Helper function to filter features to only show available ones
const getAvailableFeatures = (features: string[]): string[] => {
  return features.filter(feature => !feature.includes("❌"));
};

// Helper function to get feature icon
const getFeatureIcon = (feature: string, featured: boolean) => {
  const iconProps = {
    className: cn(
      "w-4 h-4 mr-2 flex-shrink-0",
      featured ? "text-[var(--brand-gold)]" : "text-green-500"
    ),
  };

  if (feature.includes("Product")) return <Package {...iconProps} />;
  if (feature.includes("Analytics")) return <Zap {...iconProps} />;
  return <Check {...iconProps} />;
};

export default function PricingCard({
  plan,
  onButtonClick,
  index,
  isLoading = false,
  isCurrentPlan = false,
  buttonTextOverride,
}: PricingCardProps) {
  // Use global context for features expansion
  const { featuresExpanded, toggleFeatures } = usePricingCard();

  // Logic for button state
  const isEnterprise = plan.id === "enterprise";
  const isDisabled = (!plan.available && !isEnterprise) || isCurrentPlan || isLoading;
  let buttonText = plan.button; // Default from plan data

  if (buttonTextOverride) {
    buttonText = buttonTextOverride;
  } else if (isCurrentPlan) {
    buttonText = "Current Plan";
  }

  // Get previous plan name (for "Everything in X" feature)
  const previousPlanName = getPreviousPlanName(plan.name);

  // Get only available features
  const availableFeatures = getAvailableFeatures(plan.features);

  return (
    // Structure and classes with standardized width and centering
    <motion.div
      key={plan.id} // Use plan.id for key
      custom={index} // Use index prop for stagger delay
      variants={itemFadeIn}
      className="w-full mx-auto" // Full width with centering
    >
      <Card
        className={cn(
          "bg-card border flex flex-col h-full w-full relative transition-all duration-300 rounded-xl p-3 md:p-4", // Full width, minimal padding
          plan.featured
            ? "border-[var(--brand-gold)] shadow-lg dark:shadow-[var(--brand-gold)]/15 pt-6 md:pt-8" // Add extra top padding if featured for badge space
            : isEnterprise
            ? "border-purple-500 shadow-lg dark:shadow-purple-500/15 pt-3 md:pt-4" // Enterprise styling without extra padding
            : "border-border pt-3 md:pt-4", // Keep original padding if not featured
          !plan.available && "opacity-70" // Keep disabled visual cue
        )}
      >
        {plan.featured && plan.mostPopular && (
          // Adjusted positioning slightly, ensure z-index if needed
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] px-4 py-1 rounded-full text-xs font-semibold shadow-md z-10">
            Most Popular
          </div>
        )}

        {/* Header section matching LandingPageClient */}
        {/* Removed CardHeader component, applying padding directly to Card */}
        <div className="flex items-center mb-3">
          <div
            className={cn(
              "p-1.5 rounded-full mr-2",
              plan.featured
                ? "bg-[var(--brand-gold)]/15"
                : isEnterprise
                ? "bg-purple-500/15"
                : "bg-muted"
            )}
          >
            {getPlanIcon(plan.name, plan.featured || isEnterprise)}
          </div>
          <h3 className="text-lg md:text-xl font-semibold text-foreground">{plan.name}</h3>
        </div>

        {/* Price section matching LandingPageClient */}
        <div className="mb-4">
          <div className="flex items-baseline gap-1">
            <span className="text-2xl md:text-3xl font-bold text-foreground">
              {plan.price}
            </span>
            <span className="text-muted-foreground text-sm">{plan.period}</span>
          </div>
          {plan.savings && (
            <span className="text-green-600 text-sm block mt-1 font-medium">
              <Sparkles className="inline-block w-3 h-3 mr-1" />
              {plan.savings}
            </span>
          )}
          <p className="text-muted-foreground mt-2 text-sm">
            {plan.description}
          </p>
        </div>

        {/* Button moved above features section */}
        <Button
          onClick={() => !isDisabled && onButtonClick?.(plan)} // Use optional chaining for onButtonClick
          size="lg"
          className={cn(
            "w-full mb-4 rounded-lg font-semibold flex items-center justify-center gap-2 transition-colors duration-200",
            isDisabled
              ? "bg-muted text-muted-foreground cursor-not-allowed" // Disabled style from LandingPageClient
              : isEnterprise
              ? "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white" // Enterprise style
              : plan.featured
              ? "bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]" // Featured available
              : "bg-primary hover:bg-primary/90 text-primary-foreground" // Non-featured available
          )}
          disabled={isDisabled}
        >
          {isLoading ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <>
              {/* Icons for button state */}
              {!plan.available && !isCurrentPlan && (
                <Clock className="w-4 h-4" />
              )}
              {isCurrentPlan && <ShieldCheck className="w-4 h-4" />}
              {buttonText}
            </>
          )}
        </Button>

        {/* Features section with expand/collapse functionality */}
        <div className="flex-1">
          {/* Features toggle button */}
          <button
            onClick={toggleFeatures}
            className={cn(
              "flex items-center justify-center w-full mb-4 py-1 px-2 rounded-md text-sm font-medium transition-all",
              "border border-transparent hover:border-border",
              featuresExpanded
                ? "text-foreground bg-muted/50"
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            <span className="mr-1">
              {featuresExpanded ? "Hide" : "Show"} features
            </span>
            {featuresExpanded ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </button>

          {/* Preview of key features when collapsed */}
          {!featuresExpanded && (
            <ul className="space-y-3">
              {/* If this is not the Basic plan, show "Everything in [Previous Plan]" */}
              {previousPlanName && (
                <li className="flex items-start">
                  <div className="flex items-center text-sm">
                    <div className={cn(
                      "p-1 rounded-full mr-2 flex-shrink-0",
                      plan.featured ? "bg-[var(--brand-gold)]/15" : "bg-muted"
                    )}>
                      <Package className={cn(
                        "w-3 h-3",
                        plan.featured ? "text-[var(--brand-gold)]" : "text-foreground"
                      )} />
                    </div>
                    <span className="font-medium">Everything in {previousPlanName}</span>
                  </div>
                </li>
              )}

              {/* Show 2-3 key features specific to this plan */}
              {availableFeatures.slice(0, previousPlanName ? 2 : 3).map((feature, i) => (
                <li key={i} className="flex items-center text-sm">
                  {getFeatureIcon(feature, plan.featured)}
                  <span>{feature}</span>
                </li>
              ))}

              {availableFeatures.length > (previousPlanName ? 2 : 3) && (
                <li className="text-sm text-muted-foreground text-center italic">
                  {availableFeatures.length - (previousPlanName ? 2 : 3)} more features...
                </li>
              )}
            </ul>
          )}

          {/* Expanded features list with animation */}
          <AnimatePresence>
            {featuresExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <ul className="space-y-3">
                  {/* If this is not the Basic plan, show "Everything in [Previous Plan]" */}
                  {previousPlanName && (
                    <li className="flex items-start bg-muted/30 p-2 rounded-md">
                      <div className="flex items-center text-sm font-medium">
                        <div className={cn(
                          "p-1 rounded-full mr-2 flex-shrink-0",
                          plan.featured ? "bg-[var(--brand-gold)]/15" : "bg-muted"
                        )}>
                          <Package className={cn(
                            "w-3 h-3",
                            plan.featured ? "text-[var(--brand-gold)]" : "text-foreground"
                          )} />
                        </div>
                        <span>Everything in {previousPlanName}</span>
                      </div>
                    </li>
                  )}

                  {/* Show all available features specific to this plan */}
                  {availableFeatures.map((feature, i) => (
                    <li key={i} className="flex items-center text-sm">
                      {getFeatureIcon(feature, plan.featured)}
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Tooltip matching LandingPageClient */}
        {!plan.available && !isCurrentPlan && !isEnterprise && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                {/* Adjusted tooltip trigger position */}
                <div className="absolute top-3 right-3 cursor-help">
                  <AlertCircle className="w-5 h-5 text-muted-foreground hover:text-foreground" />
                </div>
              </TooltipTrigger>
              <TooltipContent className="bg-popover border-border text-popover-foreground">
                <p>{plan.id === "free" ? "Free plan is automatically managed by the system." : "This plan is coming soon."}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </Card>
    </motion.div>
  );
}
