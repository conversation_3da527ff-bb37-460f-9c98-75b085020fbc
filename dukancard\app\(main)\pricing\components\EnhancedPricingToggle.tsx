"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";

type AnimationVariant = {
  hidden: { opacity: number; y: number };
  visible: {
    opacity: number;
    y: number;
    transition: { duration: number; ease: string };
  } | ((_i: number) => {
    opacity: number;
    y: number;
    transition: { duration: number; delay: number; ease: string };
  });
};

interface EnhancedPricingToggleProps {
  billingCycle: "monthly" | "yearly";
  setBillingCycle: (_cycle: "monthly" | "yearly") => void;
  itemFadeIn: AnimationVariant;
}

export default function EnhancedPricingToggle({
  billingCycle,
  setBillingCycle,
  itemFadeIn,
}: EnhancedPricingToggleProps) {
  const toggleRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(toggleRef, { once: true, amount: 0.8 });

  return (
    <div ref={toggleRef} className="relative">
      <motion.div
        variants={itemFadeIn}
        custom={1}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="flex flex-col items-center justify-center mb-16"
      >
        {/* Toggle container */}
        <div className="relative">
          {/* Background glow effect */}
          <motion.div
            className="absolute inset-0 bg-[var(--brand-gold)]/10 blur-md rounded-full -z-10"
            animate={{ 
              scale: [1, 1.05, 1],
              opacity: [0.5, 0.7, 0.5]
            }}
            transition={{ 
              duration: 3, 
              repeat: Infinity, 
              repeatType: "reverse" 
            }}
          />

          {/* Toggle buttons */}
          <div className="relative z-10 space-x-2 bg-muted p-1.5 rounded-full border border-[var(--brand-gold)]/30 inline-flex shadow-sm">
            <Button
              onClick={() => setBillingCycle("monthly")}
              variant={billingCycle === "monthly" ? "default" : "ghost"}
              size="sm"
              className={`relative overflow-hidden cursor-pointer px-5 py-1.5 rounded-full text-sm font-medium transition-all duration-300 ${
                billingCycle === "monthly"
                  ? "bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900 shadow" // Active state
                  : "text-muted-foreground hover:text-foreground" // Inactive state
              }`}
            >
              Monthly
              
              {/* Animated sparkle effect for active state */}
              {billingCycle === "monthly" && (
                <motion.span
                  className="absolute inset-0 pointer-events-none"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.span
                    className="absolute top-1 right-2 text-black/40 dark:text-white/40"
                    animate={{ 
                      rotate: [0, 20, 0, -20, 0],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{ 
                      duration: 2, 
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  >
                    <Sparkles size={10} />
                  </motion.span>
                </motion.span>
              )}
            </Button>
            
            <Button
              onClick={() => setBillingCycle("yearly")}
              variant={billingCycle === "yearly" ? "default" : "ghost"}
              size="sm"
              className={`relative overflow-hidden cursor-pointer px-5 py-1.5 rounded-full text-sm font-medium transition-all duration-300 flex items-center gap-2 ${
                billingCycle === "yearly"
                  ? "bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900 shadow" // Active state
                  : "text-muted-foreground hover:text-foreground" // Inactive state
              }`}
            >
              Yearly
              
              {/* Savings badge with enhanced animation */}
              <motion.span 
                className="bg-green-600 text-white text-xs px-2 py-0.5 rounded-full flex items-center gap-1 whitespace-nowrap"
                animate={billingCycle === "yearly" ? {
                  scale: [1, 1.05, 1],
                  y: [0, -2, 0]
                } : {}}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                <Sparkles size={10} className="inline" />
                Save 20%
              </motion.span>
              
              {/* Animated sparkle effect for active state */}
              {billingCycle === "yearly" && (
                <motion.span
                  className="absolute inset-0 pointer-events-none"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.span
                    className="absolute top-1 left-2 text-black/40 dark:text-white/40"
                    animate={{ 
                      rotate: [0, 20, 0, -20, 0],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{ 
                      duration: 2, 
                      repeat: Infinity,
                      repeatType: "reverse",
                      delay: 0.5
                    }}
                  >
                    <Sparkles size={10} />
                  </motion.span>
                </motion.span>
              )}
            </Button>
          </div>
        </div>
        
        {/* Yearly savings callout - only visible when monthly is selected */}
        {billingCycle === "monthly" && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3 }}
            className="mt-4 text-sm text-muted-foreground"
          >
            Switch to yearly billing and save 20%
          </motion.div>
        )}
      </motion.div>
    </div>
  );
}
