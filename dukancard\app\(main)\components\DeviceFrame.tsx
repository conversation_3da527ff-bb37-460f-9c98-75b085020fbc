"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Smartphone, Tablet } from "lucide-react";

interface DeviceFrameProps {
  children: React.ReactNode;
}

export default function DeviceFrame({ children }: DeviceFrameProps) {
  // Using state to track the current device view
  const [, setDevice] = useState<"phone" | "tablet">("phone");

  return (
    <div className="relative">
      <Tabs
        defaultValue="phone"
        className="w-full mb-4"
        onValueChange={(value) => setDevice(value as "phone" | "tablet")}
      >
        <TabsList className="grid w-48 grid-cols-2 mx-auto">
          <TabsTrigger value="phone" className="flex items-center gap-2">
            <Smartphone className="w-4 h-4" />
            <span>Phone</span>
          </TabsTrigger>
          <TabsTrigger value="tablet" className="flex items-center gap-2">
            <Tablet className="w-4 h-4" />
            <span>Tablet</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="phone" className="mt-4">
          <motion.div
            className="relative mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            style={{ maxWidth: "320px" }}
          >
            {/* Phone frame */}
            <div className="relative rounded-[40px] border-8 border-neutral-800 dark:border-neutral-700 shadow-xl overflow-hidden bg-neutral-800 dark:bg-neutral-700 pt-8 pb-10 px-2">
              {/* Notch */}
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1/3 h-6 bg-neutral-800 dark:bg-neutral-700 rounded-b-xl flex justify-center items-end pb-1">
                <div className="w-16 h-2 rounded-full bg-neutral-700 dark:bg-neutral-600"></div>
              </div>

              {/* Home indicator */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-1/3 h-1 bg-neutral-700 dark:bg-neutral-600 rounded-full"></div>

              {/* Content */}
              <div className="rounded-lg overflow-hidden bg-background">
                {children}
              </div>
            </div>
          </motion.div>
        </TabsContent>

        <TabsContent value="tablet" className="mt-4">
          <motion.div
            className="relative mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            style={{ maxWidth: "500px" }}
          >
            {/* Tablet frame */}
            <div className="relative rounded-[24px] border-8 border-neutral-800 dark:border-neutral-700 shadow-xl overflow-hidden bg-neutral-800 dark:bg-neutral-700 pt-4 pb-4 px-2">
              {/* Camera */}
              <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-2 h-2 rounded-full bg-neutral-700 dark:bg-neutral-600"></div>

              {/* Content */}
              <div className="rounded-lg overflow-hidden bg-background">
                {children}
              </div>
            </div>
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
