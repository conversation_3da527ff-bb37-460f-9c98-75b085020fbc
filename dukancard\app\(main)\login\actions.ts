"use server";

import { createClient } from "@/utils/supabase/server";
import { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from "@/lib/schemas/authSchemas";

// Send OTP to email
export async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {
  const validatedFields = EmailOTPSchema.safeParse(values);

  if (!validatedFields.success) {
    return {
      success: false,
      error: "Invalid email address",
    };
  }

  const { email } = validatedFields.data;

  try {
    const supabase = await createClient();
    const { error } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        // Set shouldCreateUser to true to allow new user registration
        shouldCreateUser: true,
        data: {
          auth_type: "email",
        },
      },
    });

    if (error) {
      // Check if this is an email rate limit error - indicates configuration issue
      if (isEmailRateLimitError(error)) {
        return {
          success: false,
          error: "Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.",
          isConfigurationError: true,
        };
      }

      return {
        success: false,
        error: handleSupabaseAuthError(error),
      };
    }

    return {
      success: true,
      message: "OTP sent to your email address. Please check your inbox.",
    };
  } catch (error) {
    // Check if this is an email rate limit error in the catch block too
    if (isEmailRateLimitError(error as Error)) {
      return {
        success: false,
        error: "Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.",
        isConfigurationError: true,
      };
    }

    return {
      success: false,
      error: handleSupabaseAuthError(error as Error),
    };
  }
}

// Verify OTP and sign in
export async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {
  const validatedFields = VerifyOTPSchema.safeParse(values);

  if (!validatedFields.success) {
    return {
      success: false,
      error: "Invalid OTP format",
    };
  }

  const { email, token } = validatedFields.data;
  const supabase = await createClient();

  try {
    const { data, error } = await supabase.auth.verifyOtp({
      email: email,
      token: token,
      type: 'email',
    });

    if (error) {
      return {
        success: false,
        error: handleSupabaseAuthError(error),
      };
    }

    return {
      success: true,
      data: data,
      message: "Successfully signed in!",
    };
  } catch (error) {
    return {
      success: false,
      error: handleSupabaseAuthError(error as Error),
    };
  }
}

// Mobile + password login
export async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {
  const validatedFields = MobilePasswordLoginSchema.safeParse(values);

  if (!validatedFields.success) {
    return {
      success: false,
      error: "Invalid mobile number or password format",
    };
  }

  const { mobile, password } = validatedFields.data;
  const supabase = await createClient();

  try {
    // Format mobile number with +91 prefix
    const phoneNumber = `+91${mobile}`;

    const { data, error } = await supabase.auth.signInWithPassword({
      phone: phoneNumber,
      password: password,
    });

    if (error) {
      return {
        success: false,
        error: handleSupabaseAuthError(error),
      };
    }

    return {
      success: true,
      data: data,
      message: "Successfully signed in!",
    };
  } catch (error) {
    return {
      success: false,
      error: handleSupabaseAuthError(error as Error),
    };
  }
}
