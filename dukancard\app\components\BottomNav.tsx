"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { useEffect, useState } from "react";
import { Home, Search, User, Store, Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface BottomNavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  isTablet?: boolean;
  badge?: string;
  disabled?: boolean;
}

const BottomNavItem = ({
  href,
  icon,
  label,
  isActive,
  isTablet = false,
  badge,
  disabled = false
}: BottomNavItemProps) => {
  const content = (
    <>
      <div className="relative mb-1">
        {icon}
        {badge && (
          <Badge
            variant="outline"
            className="absolute -top-2 -right-3 px-1 py-0 text-[7px] bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)]"
          >
            {badge}
          </Badge>
        )}
      </div>
      <span className={cn(
        "transition-all",
        isTablet ? "text-[9px]" : "text-[10px]"
      )}>{label}</span>
    </>
  );

  const itemClassName = cn(
    "flex flex-col items-center justify-center flex-1 py-2 text-xs transition-colors",
    isActive
      ? "text-[var(--brand-gold)]"
      : "text-muted-foreground hover:text-[var(--brand-gold)]",
    disabled && "opacity-70 pointer-events-none"
  );

  if (disabled) {
    return (
      <div className={itemClassName}>
        {content}
      </div>
    );
  }

  return (
    <Link href={href} className={itemClassName}>
      {content}
    </Link>
  );
};

export default function BottomNav() {
  const pathname = usePathname();
  const isMobile = useIsMobile();
  const [isTablet, setIsTablet] = useState(false);

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Check if device is a tablet (between 768px and 1024px)
    const checkTablet = () => {
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    // Initial check
    checkTablet();

    // Add event listener for resize
    window.addEventListener('resize', checkTablet);

    // Cleanup
    return () => window.removeEventListener('resize', checkTablet);
  }, []);

  // Don't render on desktop
  if (!isMobile && !isTablet) {
    return null;
  }

  // Unified navigation items for all layouts
  // Determine the appropriate account link based on the current path
  let accountLink = "/login";
  let accountIsActive = false;

  // If user is in business dashboard
  if (pathname.startsWith("/dashboard/business")) {
    accountLink = "/dashboard/business/settings";
    accountIsActive = pathname.includes("/dashboard/business/settings");
  }
  // If user is in customer dashboard
  else if (pathname.startsWith("/dashboard/customer")) {
    accountLink = "/dashboard/customer/settings";
    accountIsActive = pathname.includes("/dashboard/customer/settings");
  }
  // If user is in auth or onboarding flow
  else if (pathname.startsWith("/login") ||
           pathname.startsWith("/choose-role") || pathname.startsWith("/onboarding")) {
    accountLink = pathname; // Keep current page
    accountIsActive = true;
  }
  // For public pages
  else {
    accountLink = "/login";
    accountIsActive = pathname === "/login";
  }

  // Determine feed link based on current location and authentication status
  let feedLink = "/login"; // Default to login for non-authenticated users
  let feedIsActive = false;

  // If user is in business dashboard, redirect to business dashboard (main feed page)
  if (pathname.startsWith("/dashboard/business")) {
    feedLink = "/dashboard/business";
    feedIsActive = pathname === "/dashboard/business";
  }
  // If user is in customer dashboard, redirect to customer dashboard (main feed page)
  else if (pathname.startsWith("/dashboard/customer")) {
    feedLink = "/dashboard/customer";
    feedIsActive = pathname === "/dashboard/customer";
  }
  // For all other pages (public pages), redirect to login
  else {
    feedLink = "/login";
    feedIsActive = false;
  }

  // Unified navigation items
  const navItems = [
    {
      key: "home",
      href: pathname.startsWith("/dashboard/business") ? "/?view=home" :
            pathname.startsWith("/dashboard/customer") ? "/?view=home" : "/",
      icon: <Home size={20} />,
      label: "Home",
      isActive: pathname === "/" ||
                pathname === "/dashboard/business/overview" ||
                pathname === "/dashboard/customer/overview"
    },
    {
      key: "discover",
      href: "/discover",
      icon: <Search size={20} />,
      label: "Discover",
      isActive: pathname === "/discover"
    },
    {
      key: "feed",
      href: feedLink,
      icon: <Users size={20} />,
      label: "Feed", // Always show "Feed" for consistency
      isActive: feedIsActive
    },
    {
      key: "dukan-ai",
      href: "#",
      icon: <Store size={20} />,
      label: "Dukan AI",
      isActive: false,
      badge: "Soon",
      disabled: true
    },
    {
      key: "account",
      href: accountLink,
      icon: <User size={20} />,
      label: "Account",
      isActive: accountIsActive
    }
  ];

  return (
    <motion.div
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "fixed bottom-0 left-0 right-0 z-50 flex items-center justify-around bg-background/95 backdrop-blur-lg border-t border-border/80 px-2",
        isTablet ? "h-14" : "h-16"
      )}
    >
      {navItems.map((item) => (
        <BottomNavItem
          key={item.key}
          href={item.href}
          icon={item.icon}
          label={item.label}
          isActive={item.isActive}
          isTablet={isTablet}
          badge={item.badge}
          disabled={item.disabled}
        />
      ))}
    </motion.div>
  );
}
