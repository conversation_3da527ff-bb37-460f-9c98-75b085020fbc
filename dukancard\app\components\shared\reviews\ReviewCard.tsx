"use client";

import { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Edit, Trash2, Loader2, ExternalLink, Check, X, User, Building2 } from "lucide-react";
import { deleteReview } from "@/lib/actions/interactions";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";
import { Textarea } from "@/components/ui/textarea";

interface BusinessProfileDataForReview {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: BusinessProfileDataForReview | null;
  reviewer_type?: 'business' | 'customer';
  reviewer_slug?: string | null;
}

interface ReviewCardProps {
  review: ReviewData;
  onDeleteSuccess: ((_reviewId: string) => void) | null;
  isReviewsReceivedTab?: boolean;
}

export default function ReviewCard({
  review,
  onDeleteSuccess,
  isReviewsReceivedTab = false,
}: ReviewCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editedRating, setEditedRating] = useState(review.rating);
  const [editedReviewText, setEditedReviewText] = useState(review.review_text || "");

  // Access business_profiles directly from the review object
  const business = review.business_profiles;

  // Handle potential issues with updated_at
  let timeAgo;
  try {
    timeAgo = formatDistanceToNow(new Date(review.updated_at || review.created_at), { addSuffix: true });
  } catch (_error) {
    timeAgo = 'recently';
  }

  // Handle delete
  const handleDelete = async () => {
    if (!review.business_profile_id) {
      toast.error('Cannot delete review: Missing business information');
      return;
    }

    const confirmation = confirm("Are you sure you want to delete this review?");
    if (!confirmation) return;

    try {
      setIsDeleting(true);
      const result = await deleteReview(review.business_profile_id);

      if (result.success) {
        toast.success(`Review for ${business?.business_name || 'business'} deleted.`);
        if (onDeleteSuccess) {
          onDeleteSuccess(review.id);
        }
      } else {
        toast.error(`Failed to delete review: ${result.error || 'Unknown error'}`);
      }
    } catch (_error) {
      toast.error('An error occurred while deleting the review');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle edit mode toggle
  const handleEdit = () => {
    setIsEditing(true);
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedRating(review.rating);
    setEditedReviewText(review.review_text || "");
  };

  // Handle save edit
  const handleSaveEdit = async () => {
    setIsUpdating(true);
    try {
      const response = await fetch("/api/customer/reviews/update", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reviewId: review.id,
          rating: editedRating,
          reviewText: editedReviewText,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Review updated successfully");
        setIsEditing(false);
        // Update the local review data
        review.rating = editedRating;
        review.review_text = editedReviewText;
      } else {
        toast.error(data.error || "Failed to update review");
      }
    } catch (error) {
      console.error("Error updating review:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      whileHover={!isEditing ? { y: -5, transition: { duration: 0.2 } } : {}}
      className={cn(
        "rounded-xl border border-neutral-200 dark:border-neutral-800",
        "bg-white dark:bg-black", // Changed to black in dark mode
        "shadow-sm p-4 transition-all duration-300 hover:shadow-md",
        "relative overflow-hidden group",
        isEditing && "ring-2 ring-amber-400 dark:ring-amber-600"
      )}
    >
      {/* Card background with subtle pattern */}
      <div
        className="absolute inset-0 pointer-events-none opacity-5 dark:opacity-10"
        style={{
          backgroundImage: `url("/decorative/card-texture.svg")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      ></div>

      <div className="relative z-10">
        {/* Business info and rating */}
        <div className="flex items-start mb-3">
          <div className="flex items-center gap-3">
            {/* Make avatar clickable if it's a business reviewer */}
            {review.reviewer_type === 'business' && review.reviewer_slug ? (
              <Link
                href={`/${review.reviewer_slug}`}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:opacity-80 transition-opacity"
              >
                <Avatar className="h-10 w-10 border border-neutral-200 dark:border-neutral-800">
                  {business?.logo_url ? (
                    <AvatarImage
                      src={business.logo_url}
                      alt={business?.business_name || "Business"}
                    />
                  ) : null}
                  <AvatarFallback className="bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300">
                    {business?.business_name?.[0]?.toUpperCase() || "B"}
                  </AvatarFallback>
                </Avatar>
              </Link>
            ) : (
              <Avatar className="h-10 w-10 border border-neutral-200 dark:border-neutral-800">
                {business?.logo_url ? (
                  <AvatarImage
                    src={business.logo_url}
                    alt={business?.business_name || "Business"}
                  />
                ) : null}
                <AvatarFallback className="bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300">
                  {business?.business_name?.[0]?.toUpperCase() || "B"}
                </AvatarFallback>
              </Avatar>
            )}

            <div className="flex-1">
              {/* Business name with redirect icon */}
              <div className="flex items-center gap-1">
                {review.reviewer_type === 'business' && review.reviewer_slug ? (
                  <Link
                    href={`/${review.reviewer_slug}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1"
                  >
                    <h3 className="font-medium text-neutral-800 dark:text-neutral-200">
                      {business?.business_name || "Unknown Business"}
                    </h3>
                    <ExternalLink className="h-3.5 w-3.5 opacity-70" />
                  </Link>
                ) : (
                  <h3 className="font-medium text-neutral-800 dark:text-neutral-200">
                    {business?.business_name || "Unknown User"}
                  </h3>
                )}
              </div>

              {/* Reviewer type badge - only show in Reviews Received tab */}
              {isReviewsReceivedTab && (
                <div className="flex items-center gap-1 mt-1">
                  {review.reviewer_type === 'business' ? (
                    <div className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium">
                      <Building2 className="h-3 w-3" />
                      Business
                    </div>
                  ) : (
                    <div className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium">
                      <User className="h-3 w-3" />
                      Customer
                    </div>
                  )}
                </div>
              )}

              {/* Rating stars - only show in non-editing mode */}
              {!isEditing && (
                <div className="flex items-center gap-1 mt-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        "h-4 w-4",
                        i < review.rating
                          ? "text-amber-500 fill-amber-500"
                          : "text-neutral-300 dark:text-neutral-700"
                      )}
                    />
                  ))}
                  <span className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                    {timeAgo}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Review content */}
        {isEditing ? (
          // Edit mode content
          <div className="space-y-3">
            {/* Star rating selector */}
            <div className="flex items-center gap-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <button
                  key={i}
                  type="button"
                  onClick={() => setEditedRating(i + 1)}
                  className="focus:outline-none"
                >
                  <Star
                    className={cn(
                      "h-6 w-6 transition-colors",
                      i < editedRating
                        ? "text-amber-500 fill-amber-500"
                        : "text-neutral-300 dark:text-neutral-700 hover:text-amber-400"
                    )}
                  />
                </button>
              ))}
            </div>

            {/* Review text input */}
            <Textarea
              value={editedReviewText}
              onChange={(e) => setEditedReviewText(e.target.value)}
              placeholder="What did you like or dislike? What stood out about your experience?"
              className="w-full bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 rounded-lg focus:ring-amber-400 focus:border-amber-400 transition-all duration-200 resize-none"
              maxLength={500}
              rows={4}
            />
            <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-1 text-right">
              {editedReviewText.length}/500
            </div>

            {/* Edit mode buttons */}
            <div className="flex items-center justify-end gap-2 mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50">
              <Button
                variant="outline"
                size="sm"
                className="text-xs h-8 gap-1"
                onClick={handleCancelEdit}
              >
                <X className="h-3.5 w-3.5" />
                Cancel
              </Button>

              <Button
                variant="default"
                size="sm"
                className="text-xs h-8 gap-1 bg-amber-500 hover:bg-amber-600 text-white"
                onClick={handleSaveEdit}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <Loader2 className="h-3.5 w-3.5 animate-spin" />
                ) : (
                  <Check className="h-3.5 w-3.5" />
                )}
                Save
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* Display mode content */}
            {review.review_text && (
              <div className="mb-4">
                <p className="text-neutral-700 dark:text-neutral-300 text-sm leading-relaxed">
                  {review.review_text}
                </p>
              </div>
            )}

            {/* Action buttons - only show in "My Reviews" tab */}
            {!isReviewsReceivedTab && (
              <div className="flex items-center justify-between mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50">
                {/* View Business button - only show for business reviews */}
                {review.reviewer_type === 'business' && business?.business_slug ? (
                  <Button
                    asChild
                    variant="outline"
                    size="sm"
                    className="text-xs h-8 gap-1"
                  >
                    <Link
                      href={`/${business.business_slug}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="h-3.5 w-3.5" />
                      View Business
                    </Link>
                  </Button>
                ) : (
                  <div></div> // Empty div to maintain layout when no View Business button
                )}

                {/* Only show edit/delete buttons if onDeleteSuccess is provided */}
                {onDeleteSuccess && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs h-8 gap-1 text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300 dark:text-blue-400 dark:border-blue-900 dark:hover:bg-blue-950/30"
                      onClick={handleEdit}
                    >
                      <Edit className="h-3.5 w-3.5" />
                      Edit
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs h-8 gap-1 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-900 dark:hover:bg-red-950/30"
                      onClick={handleDelete}
                      disabled={isDeleting}
                    >
                      {isDeleting ? (
                        <Loader2 className="h-3.5 w-3.5 animate-spin" />
                      ) : (
                        <Trash2 className="h-3.5 w-3.5" />
                      )}
                      Delete
                    </Button>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </motion.div>
  );
}
