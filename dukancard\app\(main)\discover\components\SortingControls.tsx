"use client";

import { SortAsc, Check } from "lucide-react";
import { motion } from "framer-motion";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { BusinessSortOption } from "../context/types";

interface SortingControlsProps {
  sortBy: BusinessSortOption;
  onSortChange: (_value: BusinessSortOption) => void;
  disabled?: boolean;
}

export default function SortingControls({ sortBy, onSortChange, disabled = false }: SortingControlsProps) {
  // Helper function to get human-readable sort name
  const getSortName = (sort: BusinessSortOption): string => {
    const sortNames: Record<BusinessSortOption, string> = {
      "newest": "Newest First",
      "name_asc": "Name (A-Z)",
      "name_desc": "Name (Z-A)",
      "most_liked": "Most Liked",
      "most_subscribed": "Most Subscribed",
      "highest_rated": "Highest Rated"
    };
    return sortNames[sort] || "Sort by";
  };

  return (
    <motion.div
      className="flex justify-end mb-6 px-4"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="inline-flex items-center bg-white dark:bg-neutral-900 px-3 py-2 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800">
        <SortAsc className="h-4 w-4 mr-2 text-[var(--brand-gold)]" />
        <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mr-2">Sort by:</span>
        <Select
          value={sortBy}
          onValueChange={(value) => onSortChange(value as BusinessSortOption)}
          disabled={disabled}
        >
          <SelectTrigger
            className="min-w-[180px] border-0 bg-transparent p-0 h-auto shadow-none focus:ring-0 focus:ring-offset-0"
          >
            <SelectValue placeholder="Sort by">
              <span className="font-medium text-sm text-neutral-900 dark:text-white">
                {getSortName(sortBy)}
              </span>
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="max-h-[300px] overflow-y-auto">
            <SelectGroup>
              <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5">Name</SelectLabel>
              <SelectItem value="name_asc" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${sortBy === 'name_asc' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Name (A-Z)
              </SelectItem>
              <SelectItem value="name_desc" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${sortBy === 'name_desc' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Name (Z-A)
              </SelectItem>
            </SelectGroup>

            <SelectGroup>
              <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">Date</SelectLabel>
              <SelectItem value="newest" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${sortBy === 'newest' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Newest First
              </SelectItem>
            </SelectGroup>

            <SelectGroup>
              <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">Popularity</SelectLabel>
              <SelectItem value="most_liked" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${sortBy === 'most_liked' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Most Liked
              </SelectItem>
              <SelectItem value="most_subscribed" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${sortBy === 'most_subscribed' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Most Subscribed
              </SelectItem>
              <SelectItem value="highest_rated" className="relative pl-8">
                <Check className={`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${sortBy === 'highest_rated' ? 'text-[var(--brand-gold)] opacity-100' : 'opacity-0'}`} />
                Highest Rated
              </SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </motion.div>
  );
}
