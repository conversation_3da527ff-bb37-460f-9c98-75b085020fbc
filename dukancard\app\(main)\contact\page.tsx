import { Suspense } from "react";
import ModernContactClient from "./ModernContactClient"; // Import the new client component
import { Metada<PERSON> } from "next";
import { siteConfig } from "@/lib/site-config";
import { Loader2 } from "lucide-react";

export async function generateMetadata(): Promise<Metadata> {
  const title = `Contact ${siteConfig.name}`; // More specific title
  const description =
    `Reach out to ${siteConfig.name} for support, inquiries, or feedback. Find our contact details, office address, and connect with us easily.`; // Refined description
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || siteConfig.url;
  const pageUrl = `${siteUrl}/contact`;
  const ogImage = `${siteUrl}/opengraph-image.png`; // Default OG image

  return {
    title, // Uses template: "Contact Dukancard - Dukancard"
    description,
    keywords: [ // Added keywords
      `contact ${siteConfig.name}`,
      `${siteConfig.name} support`,
      `${siteConfig.name} phone number`,
      `${siteConfig.name} email`,
      `${siteConfig.name} address`,
      "digital business card help",
    ],
    alternates: {
      canonical: "/contact", // Relative canonical path
    },
    openGraph: {
      title: title,
      description: description,
      url: pageUrl,
      siteName: siteConfig.name,
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: `Contact ${siteConfig.name}`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: title,
      description: description,
      images: [ogImage],
    },
    // Add ContactPage Schema with enhanced structured data
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "ContactPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: siteConfig.name,
          url: siteUrl,
        },
        // Add Organization details for better SEO
        mainEntity: {
          "@type": "Organization",
          name: siteConfig.name,
          url: siteUrl,
          logo: `${siteUrl}/logo.png`,
          contactPoint: {
            "@type": "ContactPoint",
            telephone: siteConfig.contact.phone,
            contactType: "Customer Service",
            email: siteConfig.contact.email
          },
          address: {
            "@type": "PostalAddress",
            "streetAddress": siteConfig.contact.address.street,
            "addressLocality": siteConfig.contact.address.city,
            "addressRegion": siteConfig.contact.address.state,
            "postalCode": siteConfig.contact.address.postalCode,
            "addressCountry": "IN"
          }
        }
      }),
    },
  };
}

// Server Component with Suspense for better loading experience
export default function ContactPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <Loader2 className="animate-spin" />
        </div>
      }
    >
      <ModernContactClient />
    </Suspense>
  );
}
