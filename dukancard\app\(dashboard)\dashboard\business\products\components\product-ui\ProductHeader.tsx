"use client";

import { motion } from "framer-motion";
import { PlusCircle, Package2 } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { itemVariants } from "../../types";
import { useProducts } from "../../context/ProductsContext";

export default function ProductHeader() {
  const {
    canAddMore,
    isPending,
    isLoading
  } = useProducts();

  return (
    <motion.div
      className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60"
      variants={itemVariants}
    >
      <div className="space-y-1">
        <div className="flex items-center gap-3 mb-2">
          <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
            <Package2 className="w-5 h-5 text-primary" />
          </div>
          <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
          <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
            Inventory Management
          </div>
        </div>
        <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
          Products & Services
        </h1>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed">
          Manage your complete product catalog with advanced filtering, bulk operations, and real-time inventory tracking.
        </p>
      </div>

      <div className="flex items-center gap-3">
        <Link href="/dashboard/business/products/add">
          <Button
            disabled={!canAddMore || isPending || isLoading}
            className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium"
            size="lg"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add New Product
          </Button>
        </Link>
      </div>
    </motion.div>
  );
}
