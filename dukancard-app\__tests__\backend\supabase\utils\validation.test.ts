import {
  validateEmail,
  validatePassword,
  validatePasswordConfirmation,
  validateIndianMobile,
  validateBusinessName,
  validateBusinessSlug,
  validatePincode,
  validateMemberName,
  validateTitle,
  validateBusinessCategory,
  validateAddressLine,
  validateCity,
  validateState,
  validateLocality,
  validatePlanSelection,
  validateRequired,
} from '@/backend/supabase/utils/validation';

describe('validation utilities', () => {
  describe('validateEmail', () => {
    it('should pass for valid emails', () => {
      expect(validateEmail('<EMAIL>').isValid).toBe(true);
    });
    it('should fail for invalid emails', () => {
      expect(validateEmail('test').isValid).toBe(false);
      expect(validateEmail('test@').isValid).toBe(false);
      expect(validateEmail('@test.com').isValid).toBe(false);
    });
    it('should fail for empty emails', () => {
      expect(validateEmail('').isValid).toBe(false);
    });
  });

  describe('validatePassword', () => {
    it('should pass for valid passwords', () => {
      expect(validatePassword('Password123!').isValid).toBe(true);
    });
    it('should fail for short passwords', () => {
      expect(validatePassword('Pass1!').isValid).toBe(false);
    });
    it('should fail without an uppercase letter', () => {
      expect(validatePassword('password123!').isValid).toBe(false);
    });
    it('should fail without a lowercase letter', () => {
      expect(validatePassword('PASSWORD123!').isValid).toBe(false);
    });
    it('should fail without a number', () => {
      expect(validatePassword('Password!').isValid).toBe(false);
    });
    it('should fail without a symbol', () => {
      expect(validatePassword('Password123').isValid).toBe(false);
    });
  });

  describe('validatePasswordConfirmation', () => {
    it('should pass for matching passwords', () => {
      expect(validatePasswordConfirmation('password', 'password').isValid).toBe(true);
    });
    it('should fail for non-matching passwords', () => {
      expect(validatePasswordConfirmation('password', 'different').isValid).toBe(false);
    });
    it('should fail for empty confirmation', () => {
      expect(validatePasswordConfirmation('password', '').isValid).toBe(false);
    });
  });

  describe('validateIndianMobile', () => {
    it('should pass for valid 10-digit numbers', () => {
      expect(validateIndianMobile('9876543210').isValid).toBe(true);
    });
    it('should fail for numbers with less than 10 digits', () => {
      expect(validateIndianMobile('987654321').isValid).toBe(false);
    });
    it('should fail for numbers with more than 10 digits', () => {
      expect(validateIndianMobile('98765432100').isValid).toBe(false);
    });
    it('should pass for numbers with spaces or symbols', () => {
      expect(validateIndianMobile('************').isValid).toBe(true);
    });
  });

  // Add tests for other validation functions as needed...
});