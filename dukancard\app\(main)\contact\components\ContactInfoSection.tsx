"use client";

import { useState } from "react";
import { motion, Variants } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  Copy,
  ExternalLink,
  Calendar,
  CheckCircle2,
} from "lucide-react";
import { SiteConfig } from "@/lib/site-config";

interface ContactInfoSectionProps {
  contactInfo: SiteConfig["contact"];
  sectionFadeIn: Variants;
  itemFadeIn: (_delay?: number) => Variants;
}

export default function ContactInfoSection({
  contactInfo,
  sectionFadeIn,
  itemFadeIn,
}: ContactInfoSectionProps) {
  const [copiedItem, setCopiedItem] = useState<string | null>(null);

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItem(type);
      toast.success(`${type} copied to clipboard!`);
      setTimeout(() => setCopiedItem(null), 2000);
    } catch (err) {
      toast.error("Failed to copy to clipboard");
      console.error("Failed to copy: ", err);
    }
  };

  // Handle direct contact actions
  const handleEmailClick = () => {
    window.location.href = `mailto:${contactInfo.email}`;
  };

  const handleCallClick = () => {
    window.location.href = `tel:${contactInfo.phone.replace(/\s+/g, '')}`;
  };

  const handleMapClick = () => {
    window.open("https://maps.app.goo.gl/m6FfJHyYLC53HZiR7", "_blank");
  };

  // Card hover animation
  const cardHoverAnimation = {
    rest: { scale: 1, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)" },
    hover: {
      scale: 1.02,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: { duration: 0.3, ease: "easeOut" }
    }
  };

  // Icon animation
  const iconAnimation = {
    rest: { scale: 1 },
    hover: {
      scale: 1.15,
      rotate: [0, 5, -5, 0],
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  return (
    <motion.section
      id="contact-info-section"
      variants={sectionFadeIn}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      className="py-16 px-4 container mx-auto max-w-7xl"
    >
      <motion.div variants={itemFadeIn(0)} className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Our <span className="text-[var(--brand-gold)]">Contact Details</span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Reach out to us through any of these channels. We&apos;re here to help you with any questions or concerns.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 items-stretch">
        {/* Email Card */}
        <motion.div
          variants={itemFadeIn(1)}
          whileHover="hover"
          initial="rest"
          animate="rest"
        >
          <motion.div variants={cardHoverAnimation}>
            <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]">
              <div className="flex items-start mb-4">
                <motion.div
                  variants={iconAnimation}
                  className="bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full"
                >
                  <Mail className="w-6 h-6 text-primary dark:text-[var(--brand-gold)]" />
                </motion.div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-card-foreground">
                    Email Us
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Our team usually responds within 24 hours
                  </p>
                </div>
              </div>

              <div className="mt-auto">
                <p className="text-primary dark:text-[var(--brand-gold)] font-medium mb-4">
                  {contactInfo.email}
                </p>

                <div className="flex gap-2">
                  <Button
                    onClick={handleEmailClick}
                    variant="outline"
                    size="sm"
                    className="flex-1 border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5"
                  >
                    <Mail className="mr-2 h-4 w-4" />
                    Send Email
                  </Button>

                  <Button
                    onClick={() => copyToClipboard(contactInfo.email, "Email")}
                    variant="outline"
                    size="icon"
                    className="border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5"
                  >
                    {copiedItem === "Email" ? (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        </motion.div>

        {/* Phone Card */}
        <motion.div
          variants={itemFadeIn(2)}
          whileHover="hover"
          initial="rest"
          animate="rest"
        >
          <motion.div variants={cardHoverAnimation}>
            <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]">
              <div className="flex items-start mb-4">
                <motion.div
                  variants={iconAnimation}
                  className="bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full"
                >
                  <Phone className="w-6 h-6 text-primary dark:text-[var(--brand-gold)]" />
                </motion.div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-card-foreground">
                    Call Us
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Available during business hours
                  </p>
                </div>
              </div>

              <div className="mt-auto">
                <p className="text-primary dark:text-[var(--brand-gold)] font-medium mb-4">
                  {contactInfo.phone}
                </p>

                <div className="flex gap-2">
                  <Button
                    onClick={handleCallClick}
                    variant="outline"
                    size="sm"
                    className="flex-1 border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5"
                  >
                    <Phone className="mr-2 h-4 w-4" />
                    Call Now
                  </Button>

                  <Button
                    onClick={() => copyToClipboard(contactInfo.phone.replace(/\s+/g, ''), "Phone")}
                    variant="outline"
                    size="icon"
                    className="border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5"
                  >
                    {copiedItem === "Phone" ? (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        </motion.div>

        {/* Address Card */}
        <motion.div
          variants={itemFadeIn(3)}
          whileHover="hover"
          initial="rest"
          animate="rest"
        >
          <motion.div variants={cardHoverAnimation}>
            <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]">
              <div className="flex items-start mb-4">
                <motion.div
                  variants={iconAnimation}
                  className="bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full"
                >
                  <MapPin className="w-6 h-6 text-primary dark:text-[var(--brand-gold)]" />
                </motion.div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-card-foreground">
                    Visit Us
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Our office location
                  </p>
                </div>
              </div>

              <div className="mt-auto">
                <address className="not-italic text-primary dark:text-[var(--brand-gold)] font-medium mb-4">
                  {contactInfo.address.street}<br />
                  {contactInfo.address.city}, {contactInfo.address.state} - {contactInfo.address.postalCode}<br />
                  {contactInfo.address.country}
                </address>

                <div className="flex gap-2">
                  <Button
                    onClick={handleMapClick}
                    variant="outline"
                    size="sm"
                    className="flex-1 border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    View on Map
                  </Button>

                  <Button
                    onClick={() => copyToClipboard(contactInfo.address.full, "Address")}
                    variant="outline"
                    size="icon"
                    className="border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5"
                  >
                    {copiedItem === "Address" ? (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        </motion.div>

        {/* Hours Card */}
        <motion.div
          variants={itemFadeIn(4)}
          whileHover="hover"
          initial="rest"
          animate="rest"
        >
          <motion.div variants={cardHoverAnimation}>
            <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]">
              <div className="flex items-start mb-4">
                <motion.div
                  variants={iconAnimation}
                  className="bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full"
                >
                  <Clock className="w-6 h-6 text-primary dark:text-[var(--brand-gold)]" />
                </motion.div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-card-foreground">
                    Business Hours
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    When we&apos;re available
                  </p>
                </div>
              </div>

              <div className="mt-auto">
                <ul className="space-y-2 text-primary dark:text-[var(--brand-gold)] font-medium mb-4">
                  <li className="flex justify-between">
                    <span>Monday - Friday:</span>
                    <span>{contactInfo.hours.split(': ')[1]}</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Saturday:</span>
                    <span>Closed</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Sunday:</span>
                    <span>Closed</span>
                  </li>
                </ul>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5"
                  onClick={() => {
                    // Open calendar with business hours
                    const startTime = "09:00";
                    const endTime = "18:00";
                    const eventTitle = "Meeting with Dukancard";
                    const url = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(eventTitle)}&dates=20240101T${startTime.replace(":", "")}/20240101T${endTime.replace(":", "")}`;
                    window.open(url, "_blank");
                  }}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule Meeting
                </Button>
              </div>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </motion.section>
  );
}
