import { renderHook, act } from '@testing-library/react-native';
import { useAuthErrorHandler } from '@/src/hooks/useAuthErrorHandler';
import { useNetworkStatus } from '@/src/utils/networkStatus';
import { useToast } from '@/src/components/ui/Toast';

// Mock dependencies
jest.mock('@/src/utils/networkStatus');
jest.mock('@/src/components/ui/Toast');

describe('useAuthErrorHandler', () => {
  const mockShow = jest.fn();

  beforeEach(() => {
    (useNetworkStatus as jest.Mock).mockReturnValue({ isConnected: true });
    (useToast as jest.Mock).mockReturnValue({ show: mockShow });
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  it('should execute a successful operation', async () => {
    const operation = jest.fn().mockResolvedValue('Success');
    const { result } = renderHook(() => useAuthErrorHandler());

    let finalResult;
    await act(async () => {
      finalResult = await result.current.executeWithErrorHandling({ operation });
    });

    expect(operation).toHaveBeenCalledTimes(1);
    expect(result.current.error).toBeNull();
    expect(finalResult).toBe('Success');
  });

  it('should handle a failed operation and allow retry', async () => {
    const error = new Error('Operation failed');
    const operation = jest.fn()
      .mockRejectedValueOnce(error)
      .mockResolvedValue('Success on retry');
    const onRetry = jest.fn();
    const { result } = renderHook(() => useAuthErrorHandler());

    await act(async () => {
      await result.current.executeWithErrorHandling({ operation });
    });

    expect(result.current.error).not.toBeNull();
    expect(result.current.canRetry).toBe(true);

    let retryResult;
    await act(async () => {
      retryResult = await result.current.retryOperation({ operation, onRetry });
      jest.runAllTimers();
    });

    expect(onRetry).toHaveBeenCalledWith(1);
    expect(result.current.error).toBeNull();
    expect(retryResult).toBe('Success on retry');
  });

  it('should not retry if max retries are reached', async () => {
    const operation = jest.fn().mockRejectedValue(new Error('fail'));
    const { result } = renderHook(() => useAuthErrorHandler({ maxRetries: 1 }));

    await act(async () => {
      await result.current.executeWithErrorHandling({ operation });
    });
    
    await act(async () => {
        await result.current.retryOperation({ operation });
        jest.runAllTimers();
      });

    expect(result.current.retryCount).toBe(1);
    expect(result.current.canRetry).toBe(false);
  });
});