import { <PERSON><PERSON> } from "@/src/components/ui/Button";
import { <PERSON><PERSON>card<PERSON>ogo } from "@/src/components/ui/DukancardLogo";
import { GoogleIcon } from "@/src/components/ui/GoogleIcon";
import { Input } from "@/src/components/ui/Input";
import { OTPInput } from "@/src/components/ui/OTPInput";
import { useToast } from "@/src/components/ui/Toast";
import { useAuth } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/hooks/useTheme";
import { useAuthErrorHandler } from "@/src/hooks/useAuthErrorHandler";
import { NetworkStatusBanner } from "@/src/components/ui/NetworkStatusBanner";
import { InlineErrorHandler } from "@/src/components/ui/InlineErrorHandler";
import { ErrorRecovery } from "@/src/components/ui/ErrorRecovery";
import {
  sendEmailOTP,
  validateEmail,
  validateOTP,
  verifyEmailOTP,
} from "@/backend/supabase/services/auth/emailOtpService";
import { signInWithGoogleNative } from "@/backend/supabase/services/auth/nativeGoogleAuth2025";
import { router } from "expo-router";
import { openBrowserAsync } from "expo-web-browser";
import { Mail, Lock, Eye, EyeOff } from "lucide-react-native";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Image,
  Keyboard,
  Pressable,
  ScrollView,
  Text,
  TextInput,
  TouchableWithoutFeedback,
  View,
  ImageBackground,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { createLoginStyles } from "@/styles/auth/login-styles";
import { supabase } from "@/lib/supabase";
import { MobileAuthService } from "@/backend/supabase/services/auth/mobileAuthService";

// Minor change to trigger bundler re-evaluation

type AuthStep = "email" | "otp" | "success";
type AuthMethod = "email-otp" | "mobile-password";

interface FormData {
  email: string;
  otp: string;
  mobile: string;
  password: string;
}

interface FormErrors {
  email?: string;
  otp?: string;
  mobile?: string;
  password?: string;
}

export default function LoginScreen() {
  const { checkUserRole } = useAuth();
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const toast = useToast();
  // Styles will be created after state initialization

  const [currentStep, setCurrentStep] = useState<AuthStep>("email");
  const [authMethod, setAuthMethod] = useState<AuthMethod>("email-otp");
  const [formData, setFormData] = useState<FormData>({
    email: "",
    otp: "",
    mobile: "",
    password: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isOtpLoading, setIsOtpLoading] = useState(false);
  const [isMobileLoading, setIsMobileLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [showPassword, setShowPassword] = useState(false);

  // Enhanced error handling
  const errorHandler = useAuthErrorHandler({
    maxRetries: 3,
    showToastOnError: true,
    showAlertOnError: false,
    context: "LoginScreen",
  });

  // Create styles without keyboard state dependency
  const styles = createLoginStyles(theme, insets);

  // Handle opening URLs in browser
  const handleOpenURL = async (url: string) => {
    try {
      await openBrowserAsync(url);
    } catch (error) {
      console.error("Failed to open URL:", error);
    }
  };

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(60);
    const interval = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleEmailSubmit = async () => {
    const emailValidation = validateEmail(formData.email);
    if (!emailValidation.isValid) {
      setErrors({ email: emailValidation.message });
      return;
    }

    setErrors({});
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsEmailLoading(true);
        const otpResult = await sendEmailOTP(formData.email);

        if (!otpResult.success) {
          throw new Error(otpResult.message || "Failed to send OTP");
        }

        return otpResult;
      },
      onSuccess: () => {
        setCurrentStep("otp");
        startResendTimer();
        toast.success(
          "Code Sent",
          "Please check your email for the verification code."
        );
        setIsEmailLoading(false);
      },
      onError: (error) => {
        setErrors({ email: error.message });
        setIsEmailLoading(false);
      },
      context: "EmailOTP",
    });
  };

  const handleOTPSubmit = async (otp: string) => {
    const otpValidation = validateOTP(otp);
    if (!otpValidation.isValid) {
      setErrors({ otp: otpValidation.message });
      return;
    }

    setErrors({});
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsOtpLoading(true);
        const verifyResult = await verifyEmailOTP(formData.email, otp);

        if (!verifyResult.success) {
          throw new Error(verifyResult.message || "OTP verification failed");
        }

        return verifyResult;
      },
      onSuccess: async () => {
        try {
          setCurrentStep("success");

          // Check user role and navigate accordingly
          const roleStatus = await checkUserRole();
          if (roleStatus) {
            if (roleStatus.needsRoleSelection) {
              router.replace("/(auth)/choose-role");
            } else if (roleStatus.needsOnboarding) {
              router.replace("/(onboarding)/business-details");
            } else if (roleStatus.role === "customer") {
              router.replace("/(dashboard)/customer");
            } else if (roleStatus.role === "business") {
              router.replace("/(dashboard)/business");
            }
          } else {
            // Fallback if roleStatus is null/undefined (shouldn't happen with proper checkUserRole)
            toast.error(
              "Login successful, but unable to determine role.",
              "Redirecting to home screen."
            );
            router.replace("/"); // Fallback to home
          }
        } catch (error) {
          console.error("Error checking user role after login:", error);
          toast.error(
            "Login successful, but an error occurred determining your role.",
            "Please try again or contact support. Redirecting to home screen."
          );
          router.replace("/"); // Fallback to home
        } finally {
          setIsOtpLoading(false);
        }
      },
      onError: (error) => {
        setErrors({ otp: error.message });
        setIsOtpLoading(false);
      },
      context: "OTPVerification",
    });
  };

  const handleGoogleLogin = async () => {
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsGoogleLoading(true);
        const googleResult = await signInWithGoogleNative();

        if (!googleResult.success) {
          // Don't throw error for cancelled sign-in (normal user action)
          if (googleResult.message === "cancelled") {
            setIsGoogleLoading(false);
            return null; // Return null for cancelled operations
          }
          throw new Error(googleResult.message || "Google sign-in failed");
        }

        return googleResult;
      },
      onSuccess: async (result) => {
        if (!result) {
          return; // Handle cancelled sign-in gracefully
        }

        try {
          // Check user role after successful Google login
          const roleStatus = await checkUserRole();
          if (roleStatus) {
            if (roleStatus.needsRoleSelection) {
              router.replace("/(auth)/choose-role");
            } else if (roleStatus.needsOnboarding) {
              router.replace("/(onboarding)/business-details");
            } else if (roleStatus.role === "customer") {
              router.replace("/(dashboard)/customer");
            } else if (roleStatus.role === "business") {
              router.replace("/(dashboard)/business");
            }
          } else {
            // Fallback if roleStatus is null/undefined (shouldn't happen with proper checkUserRole)
            toast.error(
              "Login successful, but unable to determine role.",
              "Redirecting to home screen."
            );
            router.replace("/"); // Fallback to home
          }
        } catch (error) {
          console.error("Error checking user role after login:", error);
          toast.error(
            "Login successful, but an error occurred determining your role.",
            "Please try again or contact support. Redirecting to home screen."
          );
          router.replace("/"); // Fallback to home
        } finally {
          setIsGoogleLoading(false);
        }
      },
      onError: (error) => {
        setIsGoogleLoading(false);
        toast.error(
          "Google Sign-In Error",
          error.message ||
            "Unable to complete Google sign-in. Please try again."
        );
      },
      context: "GoogleLogin",
    });
  };

  const handleResendOTP = async () => {
    if (resendTimer > 0) return;

    setIsOtpLoading(true);
    try {
      const result = await sendEmailOTP(formData.email);
      if (result.success) {
        startResendTimer();
        toast.success(
          "Code Sent",
          "A new verification code has been sent to your email."
        );
      } else {
        toast.error("Unable to Send Code", result.message);
      }
    } catch (error) {
      console.error("Resend OTP error:", error);
      toast.error(
        "Connection Error",
        "Unable to send verification code. Please check your connection and try again."
      );
    } finally {
      setIsOtpLoading(false);
    }
  };

  // Mobile password validation
  const validateMobile = (mobile: string) => {
    if (!mobile)
      return { isValid: false, message: "Mobile number is required" };
    if (!/^\d{10}$/.test(mobile))
      return {
        isValid: false,
        message: "Please enter a valid 10-digit mobile number",
      };
    return { isValid: true, message: "" };
  };

  const validatePassword = (password: string) => {
    if (!password) return { isValid: false, message: "Password is required" };
    return { isValid: true, message: "" };
  };

  // Handle mobile password login
  const handleMobilePasswordSubmit = async () => {
    const mobileValidation = validateMobile(formData.mobile);
    const passwordValidation = validatePassword(formData.password);

    if (!mobileValidation.isValid || !passwordValidation.isValid) {
      setErrors({
        mobile: mobileValidation.message,
        password: passwordValidation.message,
      });
      return;
    }

    setErrors({});
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsMobileLoading(true);
        const authResult = await MobileAuthService.signInWithMobilePassword(
          formData.mobile,
          formData.password
        );

        if (!authResult.data?.user) {
          throw new Error(authResult.error?.message || "Mobile login failed");
        }

        return authResult;
      },
      onSuccess: async () => {
        try {
          setCurrentStep("success");

          // Check user role and navigate accordingly
          const roleStatus = await checkUserRole();
          if (roleStatus) {
            if (roleStatus.needsRoleSelection) {
              router.replace("/(auth)/choose-role");
            } else if (roleStatus.needsOnboarding) {
              router.replace("/(onboarding)/business-details");
            } else if (roleStatus.role === "customer") {
              router.replace("/(dashboard)/customer");
            } else if (roleStatus.role === "business") {
              router.replace("/(dashboard)/business");
            }
          } else {
            // Fallback if roleStatus is null/undefined (shouldn't happen with proper checkUserRole)
            toast.error(
              "Login successful, but unable to determine role.",
              "Redirecting to home screen."
            );
            router.replace("/"); // Fallback to home
          }
        } catch (error) {
          console.error("Error checking user role after login:", error);
          toast.error(
            "Login successful, but an error occurred determining your role.",
            "Please try again or contact support. Redirecting to home screen."
          );
          router.replace("/"); // Fallback to home
        } finally {
          setIsMobileLoading(false);
        }
      },
      onError: (error) => {
        setErrors({ password: error.message });
        setIsMobileLoading(false);
      },
      context: "MobilePasswordLogin",
    });
  };

  // Handle auth method change
  const handleAuthMethodChange = (method: AuthMethod) => {
    if (method !== authMethod) {
      setAuthMethod(method);
      setCurrentStep("email");
      setErrors({});
    }
  };

  // Auth method toggle component
  const renderAuthMethodToggle = () => {
    // Only show toggle when on email step or mobile-password method
    if (
      !(authMethod === "email-otp" && currentStep === "email") &&
      authMethod !== "mobile-password"
    ) {
      return null;
    }

    return (
      <View style={styles.authToggleContainer}>
        <Pressable
          onPress={() => handleAuthMethodChange("email-otp")}
          style={[
            styles.authToggleButton,
            authMethod === "email-otp" && styles.authToggleButtonActive,
          ]}
        >
          <Text
            style={[
              styles.authToggleText,
              authMethod === "email-otp" && styles.authToggleTextActive,
            ]}
          >
            Email
          </Text>
        </Pressable>
        <Pressable
          onPress={() => handleAuthMethodChange("mobile-password")}
          style={[
            styles.authToggleButton,
            authMethod === "mobile-password" && styles.authToggleButtonActive,
          ]}
        >
          <Text
            style={[
              styles.authToggleText,
              authMethod === "mobile-password" && styles.authToggleTextActive,
            ]}
          >
            Mobile
          </Text>
        </Pressable>
      </View>
    );
  };

  const renderEmailStep = () => (
    <View style={styles.formContainer}>
      <Input
        type="email"
        value={formData.email}
        onChangeText={(email) => setFormData({ ...formData, email })}
        placeholder="Enter your email address"
        error={errors.email}
        autoCapitalize="none"
        keyboardType="email-address"
        autoComplete="email"
        leftIcon={<Mail size={20} color={theme.colors.textSecondary} />}
      />

      {/* Inline Error Handler for Email Step */}
      {errorHandler.hasError && currentStep === "email" && (
        <InlineErrorHandler
          error={errorHandler.error}
          onRetry={handleEmailSubmit}
          onDismiss={() => errorHandler.clearError()}
          isRetrying={errorHandler.isLoading}
          showRetry={errorHandler.canRetryOperation}
        />
      )}

      <Button
        title="Continue with Email"
        onPress={handleEmailSubmit}
        loading={isEmailLoading}
        disabled={isEmailLoading || !errorHandler.isOnline}
      />

      {/* Divider */}
      <View style={styles.dividerContainer}>
        <View style={styles.dividerLine} />
        <Text style={styles.dividerText}>or</Text>
        <View style={styles.dividerLine} />
      </View>

      <Button
        title="Continue with Google"
        onPress={handleGoogleLogin}
        variant="outline"
        loading={isGoogleLoading}
        disabled={isGoogleLoading}
        icon={<GoogleIcon size={20} />}
      />
    </View>
  );

  const renderMobilePasswordStep = () => (
    <View style={styles.formContainer}>
      <View style={styles.mobileInputContainer}>
        <Text style={styles.inputLabel}>Mobile Number</Text>
        <View style={styles.mobileInputWrapper}>
          <View style={styles.countryCodeContainer}>
            <Text style={styles.countryCode}>+91</Text>
          </View>
          <TextInput
            value={formData.mobile}
            onChangeText={(mobile) => {
              // Remove any +91 prefix if user enters it
              let value = mobile.replace(/^\+91/, "");
              // Only allow numeric input
              value = value.replace(/\D/g, "");
              // Limit to 10 digits
              if (value.length <= 10) {
                setFormData({ ...formData, mobile: value });
              }
            }}
            placeholder="9876543210"
            keyboardType="numeric"
            maxLength={10}
            style={styles.mobileInput}
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>
        {errors.mobile && (
          <Text style={[styles.errorText, { color: "#ef4444" }]}>
            {errors.mobile}
          </Text>
        )}
      </View>

      <Input
        label="Password"
        type={showPassword ? "text" : "password"}
        value={formData.password}
        onChangeText={(password) => setFormData({ ...formData, password })}
        placeholder="••••••••"
        error={errors.password}
        leftIcon={<Lock size={20} color={theme.colors.textSecondary} />}
        rightIcon={
          <Pressable onPress={() => setShowPassword(!showPassword)}>
            {showPassword ? (
              <Eye size={20} color={theme.colors.textSecondary} />
            ) : (
              <EyeOff size={20} color={theme.colors.textSecondary} />
            )}
          </Pressable>
        }
      />

      {/* Inline Error Handler for Mobile Password Step */}
      {errorHandler.hasError && authMethod === "mobile-password" && (
        <InlineErrorHandler
          error={errorHandler.error}
          onRetry={handleMobilePasswordSubmit}
          onDismiss={() => errorHandler.clearError()}
          isRetrying={errorHandler.isLoading}
          showRetry={errorHandler.canRetryOperation}
        />
      )}

      <Button
        title="Sign In"
        onPress={handleMobilePasswordSubmit}
        loading={isMobileLoading}
        disabled={isMobileLoading || !errorHandler.isOnline}
      />

      {/* Divider */}
      <View style={styles.dividerContainer}>
        <View style={styles.dividerLine} />
        <Text style={styles.dividerText}>or</Text>
        <View style={styles.dividerLine} />
      </View>

      <Button
        title="Continue with Google"
        onPress={handleGoogleLogin}
        variant="outline"
        loading={isGoogleLoading}
        disabled={isGoogleLoading}
        icon={<GoogleIcon size={20} />}
      />
    </View>
  );

  const renderOTPStep = () => (
    <View style={styles.otpFullScreenContainer}>
      <ScrollView
        contentContainerStyle={[
          styles.otpScrollContent,
          { justifyContent: "flex-start" },
        ]}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
      >
        {/* Header Section */}
        <View style={styles.otpHeaderSection}>
          <View style={styles.otpIconContainer}>
            <View style={styles.otpIcon}>
              <Mail size={24} color={theme.colors.primary} />
            </View>
          </View>

          <Text style={styles.otpMainTitle}>Verify Your Email</Text>

          <Text style={styles.otpSubtitle}>
            We&amp;apos;ve sent a 6-digit verification code to
          </Text>

          <Text style={styles.otpEmailDisplay}>{formData.email}</Text>
        </View>

        {/* OTP Input Section */}
        <View style={styles.otpInputSection}>
          <Text style={styles.otpInputLabel}>Enter verification code</Text>

          <View style={styles.otpInputWrapper}>
            <OTPInput
              length={6}
              onComplete={handleOTPSubmit}
              onChangeText={(otp) => setFormData({ ...formData, otp })}
              error={errors.otp}
              disabled={isOtpLoading}
              autoFocus={true}
            />
            {isOtpLoading && (
              <View style={styles.otpLoadingOverlay}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons Section - Moved outside ScrollView */}
      <View style={styles.otpActionsSection}>
        <Pressable
          onPress={handleResendOTP}
          disabled={resendTimer > 0 || isOtpLoading}
          style={[
            styles.otpResendButton,
            resendTimer > 0 && styles.otpButtonDisabled,
          ]}
        >
          <Text
            style={[
              styles.otpResendButtonText,
              resendTimer > 0 && styles.otpButtonTextDisabled,
            ]}
          >
            {resendTimer > 0 ? `Resend in ${resendTimer}s` : "Resend code"}
          </Text>
        </Pressable>

        <Pressable
          onPress={() => setCurrentStep("email")}
          style={styles.otpChangeEmailButton}
        >
          <Text style={styles.otpChangeEmailText}>← Change email</Text>
        </Pressable>
      </View>
    </View>
  );

  return (
    <View style={styles.safeArea}>
      <StatusBar
        style="light" // Always light since we have a background image
        backgroundColor="transparent"
        translucent={true}
      />

      {currentStep === "otp" ? (
        // OTP layout - simplified
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.otpMainContainer}>
            {/* Network Status Banner for OTP */}
            <NetworkStatusBanner
              onRetry={() => {
                // Retry the last failed operation based on current step
                if (authMethod === "email-otp") {
                  handleEmailSubmit();
                } else {
                  // For mobile-password method, we could retry login
                  // but for now, just do nothing as there's no specific retry action
                }
              }}
              showWhenOnline={true}
            />
            {renderOTPStep()}
          </View>
        </TouchableWithoutFeedback>
      ) : (
        // New two-row layout
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.twoRowContainer}>
            {/* Network Status Banner - Absolute positioned */}
            <View style={styles.networkBannerContainer}>
              <NetworkStatusBanner
                onRetry={() => {
                  // Retry the last failed operation based on auth method
                  if (authMethod === "email-otp") {
                    handleEmailSubmit();
                  } else {
                    // For mobile-password method, we could retry login
                    // but for now, just do nothing as there's no specific retry action
                  }
                }}
                showWhenOnline={true}
              />
            </View>
            {/* Upper Row - Image + Logo */}
            <View style={styles.upperRow}>
              <Image
                source={require("@/images/Dukancard-Login-Screen.jpeg")}
                style={styles.backgroundImage}
                resizeMode="cover"
              />
              <View style={styles.logoOverlay}>
                <DukancardLogo size="hero" showText={true} showTagline={true} />
              </View>
            </View>

            {/* Lower Row - Form */}
            <View style={styles.lowerRow}>
              {/* Loading Overlay for success state */}
              {(isEmailLoading ||
                isGoogleLoading ||
                isOtpLoading ||
                isMobileLoading) &&
              currentStep === "success" ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator
                    size="large"
                    color={theme.colors.primary}
                  />
                  <Text style={styles.loadingText}>
                    {isEmailLoading
                      ? "Sending OTP..."
                      : isOtpLoading
                      ? "Verifying OTP..."
                      : isGoogleLoading
                      ? "Signing in with Google..."
                      : isMobileLoading
                      ? "Logging in..."
                      : "Signing you in..."}
                  </Text>
                </View>
              ) : (
                <>
                  {/* Auth Method Toggle */}
                  {renderAuthMethodToggle()}

                  {/* Form Content */}
                  {authMethod === "email-otp"
                    ? renderEmailStep()
                    : renderMobilePasswordStep()}

                  {/* Footer */}
                  <View style={styles.footerContainer}>
                    <View style={styles.footerLinksContainer}>
                      <Text style={styles.footerText}>
                        By continuing, you agree to our{" "}
                      </Text>
                      <Pressable
                        onPress={() =>
                          handleOpenURL("https://dukancard.in/terms")
                        }
                      >
                        <Text style={styles.footerLinkText}>
                          Terms of Service
                        </Text>
                      </Pressable>
                      <Text style={styles.footerText}> and </Text>
                      <Pressable
                        onPress={() =>
                          handleOpenURL("https://dukancard.in/privacy")
                        }
                      >
                        <Text style={styles.footerLinkText}>
                          Privacy Policy
                        </Text>
                      </Pressable>
                    </View>
                  </View>
                </>
              )}
            </View>
          </View>
        </TouchableWithoutFeedback>
      )}
    </View>
  );
}
