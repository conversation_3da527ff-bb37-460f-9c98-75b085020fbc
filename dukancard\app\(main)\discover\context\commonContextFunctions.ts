"use client";

import { useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchDiscoverCombined } from "../actions/combinedActions";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { NearbyProduct } from "../actions/types";
import {
  DISCOVER_BUSINESSES_PER_PAGE,
  DISCOVER_PRODUCTS_PER_PAGE,
} from "../constants/paginationConstants";
import {
  VIEW_TYPE_PARAM,
  BUSINESS_NAME_PARAM,
  PRODUCT_NAME_PARAM,
  PINCODE_PARAM,
  CITY_PARAM,
  LOCALITY_PARAM,
  CATEGORY_PARAM,
} from "../constants/urlParamConstants";
import {
  CombinedSearchFormData,
  DiscoverSearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
} from "./types";

// Common context functions
export function useCommonContextFunctions(
  viewType: ViewType,
  setViewType: (_value: ViewType) => void,
  setIsSearching: (_value: boolean) => void,
  setSearchResult: (_value: DiscoverSearchResult | null) => void,
  setIsAuthenticated: (_value: boolean) => void,
  setBusinesses: (_value: BusinessCardData[]) => void,
  setProducts: (_value: NearbyProduct[]) => void,
  setHasMore: (_value: boolean) => void,
  setTotalCount: (_value: number) => void,
  setCurrentPage: (_value: number) => void,
  setSearchError: (_value: string | null) => void,
  businessSort: BusinessSortBy,
  productSort: ProductSortOption,
  productFilterBy: ProductFilterOption,
  selectedCategory: string | null
) {
  const searchParams = useSearchParams();
  const [isPending, startSearchTransition] = useTransition();

  // Handle view change
  const handleViewChange = (view: ViewType) => {
    if (view !== viewType) {
      // First set the view type and loading state
      setViewType(view);
      setIsSearching(true);

      // Clear existing data for the new view to ensure we show skeletons
      if (view === "products") {
        setProducts([]);
      } else {
        setBusinesses([]);
      }

      // Update URL to include the view type
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        url.searchParams.set(VIEW_TYPE_PARAM, view);
        window.history.replaceState({}, "", url.toString());
      }

      // Always perform a search when switching views to ensure data is loaded
      const businessName = searchParams.get(BUSINESS_NAME_PARAM) || null;
      const productName = searchParams.get(PRODUCT_NAME_PARAM) || null;
      const pincode = searchParams.get(PINCODE_PARAM) || null;
      const city = searchParams.get(CITY_PARAM) || null;
      let locality = searchParams.get(LOCALITY_PARAM) || null;

      // Handle "_any" locality value
      if (locality === "_any") {
        locality = "";
      }

      startSearchTransition(async () => {
        try {
          // Add a small delay to prevent race conditions
          await new Promise((resolve) => setTimeout(resolve, 100));

          // If switching to products view, clear businesses
          if (view === "products") {
            setBusinesses([]);
          }
          // If switching to businesses view, clear products
          else {
            setProducts([]);
          }

          const result = await searchDiscoverCombined({
            businessName: view === "cards" ? businessName : null,
            productName: view === "products" ? productName : null,
            pincode,
            city,
            locality,
            viewType: view,
            page: 1,
            limit:
              view === "products"
                ? DISCOVER_PRODUCTS_PER_PAGE
                : DISCOVER_BUSINESSES_PER_PAGE,
            businessSort,
            productSort,
            productType:
              view === "products" && productFilterBy !== "all"
                ? productFilterBy
                : null,
            category: selectedCategory,
          });

          if (result.data) {
            setSearchResult(result.data);
            setIsAuthenticated(result.data.isAuthenticated);

            if (view === "cards" && result.data.businesses) {
              setBusinesses(result.data.businesses);
            } else if (view === "products" && result.data.products) {
              setProducts(result.data.products);
            }

            setHasMore(result.data.hasMore);
            setTotalCount(result.data.totalCount);
            setCurrentPage(1);
          } else {
            setSearchError(result.error || "Failed to fetch results.");
          }
        } catch (error) {
          console.error("Error changing view:", error);
          setSearchError("An unexpected error occurred.");
        } finally {
          setIsSearching(false);
        }
      });
    }
  };

  // Perform search
  const performSearch = (data: CombinedSearchFormData) => {
    // First set loading state and clear errors
    setIsSearching(true);
    setSearchError(null);

    // Reset state before new search to ensure we show skeletons
    if (viewType === "cards") {
      setBusinesses([]);
    } else {
      setProducts([]);
    }

    // Reset pagination state
    setCurrentPage(1);
    setHasMore(false);
    setTotalCount(0);

    // Extract values from data
    const { businessName, pincode, city, locality, category } = data;

    // Update URL parameters based on search type
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href);

      // Clear existing search parameters
      url.searchParams.delete(BUSINESS_NAME_PARAM);
      url.searchParams.delete(PRODUCT_NAME_PARAM);
      url.searchParams.delete(PINCODE_PARAM);
      url.searchParams.delete(CITY_PARAM);
      url.searchParams.delete(LOCALITY_PARAM);
      url.searchParams.delete(CATEGORY_PARAM);

      // Set business name or product name based on view type
      if (businessName) {
        if (viewType === "cards") {
          url.searchParams.set(BUSINESS_NAME_PARAM, businessName);
        } else {
          url.searchParams.set(PRODUCT_NAME_PARAM, businessName);
        }
      }

      // Set location parameters based on what's provided
      if (pincode) {
        url.searchParams.set(PINCODE_PARAM, pincode);
        if (locality && locality !== "_any") {
          url.searchParams.set(LOCALITY_PARAM, locality);
        }
      } else if (city) {
        url.searchParams.set(CITY_PARAM, city);
      }

      // Set category parameter
      if (category) {
        url.searchParams.set(CATEGORY_PARAM, category);
      }

      // Update the URL without reloading the page
      window.history.replaceState({}, "", url.toString());
    }

    startSearchTransition(async () => {
      try {
        // Add a small delay to prevent race conditions
        await new Promise((resolve) => setTimeout(resolve, 100));

        const result = await searchDiscoverCombined({
          businessName: viewType === "cards" ? businessName : null,
          productName: viewType === "products" ? businessName : null, // Reuse businessName for productName for now
          pincode,
          city, // Now correctly passing the city parameter
          locality: locality === "_any" ? "" : locality,
          category: category !== undefined ? category : selectedCategory, // Use category from search data or current selected category
          viewType,
          page: 1,
          limit:
            viewType === "products"
              ? DISCOVER_PRODUCTS_PER_PAGE
              : DISCOVER_BUSINESSES_PER_PAGE,
          businessSort,
          productSort,
          productType:
            viewType === "products" && productFilterBy !== "all"
              ? productFilterBy
              : null,
        });

        if (result.data) {
          setSearchResult(result.data);
          setIsAuthenticated(result.data.isAuthenticated);

          if (viewType === "cards" && result.data.businesses) {
            setBusinesses(result.data.businesses);
          } else if (viewType === "products" && result.data.products) {
            setProducts(result.data.products);
          }

          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
        } else {
          setSearchError(result.error || "Failed to fetch results.");
        }
      } catch (error) {
        console.error("Unexpected error in performSearch:", error);
        setSearchError("An unexpected error occurred. Please try again.");
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Load more items
  const loadMore = async (
    loadMoreBusinesses: (
      _nextPage: number,
      _isLoadingMore: boolean,
      _setIsLoadingMore: (_value: boolean) => void
    ) => Promise<boolean | undefined>,
    loadMoreProducts: (
      _nextPage: number,
      _isLoadingMore: boolean,
      _setIsLoadingMore: (_value: boolean) => void
    ) => Promise<boolean | undefined>,
    currentPage: number,
    isLoadingMore: boolean,
    setIsLoadingMore: (_value: boolean) => void
  ) => {
    if (isLoadingMore || isPending) {
      return;
    }

    const nextPage = currentPage + 1;

    if (viewType === "cards") {
      await loadMoreBusinesses(nextPage, isLoadingMore, setIsLoadingMore);
    } else {
      await loadMoreProducts(nextPage, isLoadingMore, setIsLoadingMore);
    }
  };

  return {
    isPending,
    handleViewChange,
    performSearch,
    loadMore,
  };
}
