"use client";

import React from "react";
import { Star } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";

interface ReviewSignInPromptProps {
  pathname: string;
}

export default function ReviewSignInPrompt({ pathname }: ReviewSignInPromptProps) {
  return (
    <motion.div
      className="relative p-4 sm:p-6 md:p-8 border rounded-xl bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-800/90 dark:to-neutral-900/90 shadow-lg text-center border-neutral-200 dark:border-neutral-700/80 mt-6 sm:mt-10 overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Background decorative elements */}
      <div className="absolute -top-20 -right-20 w-60 h-60 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl pointer-events-none" />
      <div className="absolute -bottom-20 -left-20 w-60 h-60 bg-blue-500/5 dark:bg-blue-500/10 rounded-full blur-3xl pointer-events-none" />

      <div className="relative flex flex-col items-center justify-center gap-6 max-w-md mx-auto">
        {/* Animated icon */}
        <motion.div
          className="p-3 sm:p-4 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/20 to-amber-500/20 text-[var(--brand-gold)] shadow-md border border-[var(--brand-gold)]/10"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <Star className="w-6 h-6 sm:w-8 sm:h-8" />
          </motion.div>
        </motion.div>

        <div className="space-y-4">
          <motion.h3
            className="text-lg sm:text-xl font-bold text-neutral-800 dark:text-neutral-200"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Write a Review
          </motion.h3>

          <motion.div
            className="bg-blue-50 dark:bg-blue-900/20 p-3 sm:p-4 rounded-lg border border-blue-100 dark:border-blue-800/30 text-left"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <p className="text-sm text-blue-700 dark:text-blue-300 font-medium mb-1">
              Your opinion matters!
            </p>
            <p className="text-xs text-blue-600/80 dark:text-blue-400/80">
              Sign in to share your experience and help others discover great businesses. Your feedback makes a difference!
            </p>
          </motion.div>

          <motion.div
            className="flex flex-col sm:flex-row gap-3 pt-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <motion.div
              className="w-full"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                asChild
                className="w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] font-medium shadow-md hover:shadow-lg transition-all duration-300 py-3 rounded-lg text-base cursor-pointer h-12"
              >
                <a href={`/login?message=Please log in to leave a review&redirect=${encodeURIComponent(pathname.substring(1))}`}>
                  Sign In
                </a>
              </Button>
            </motion.div>

            <motion.div
              className="w-full"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                asChild
                variant="outline"
                className="w-full border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 font-medium transition-all duration-300 py-3 rounded-lg text-base cursor-pointer h-12"
              >
                <a href={`/login?message=Create an account to leave a review&redirect=${encodeURIComponent(pathname.substring(1))}`}>
                  Create Account
                </a>
              </Button>
            </motion.div>
          </motion.div>

          <motion.div
            className="pt-2 text-xs text-neutral-500 dark:text-neutral-400"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            No account required to view reviews
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}