"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import {
  Share2,
  QrCode,
  MapPin,
  ShoppingBag,
  MessageCircle,
  Phone,
  Star,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function FeatureElements() {
  // Client-side only rendering
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Define features with responsive positioning
  const features = [
    {
      id: 1,
      icon: Share2,
      label: "Easily Shareable",
      description: "Share your digital card via link, QR code, or social media",
      top: "10%",
      left: "-5%",
      delay: 0.1,
    },
    {
      id: 2,
      icon: QrCode,
      label: "QR Code",
      description: "Scan to instantly view the business card on any device",
      top: "25%",
      right: "-5%",
      delay: 0.2,
    },
    {
      id: 3,
      icon: MapPin,
      label: "Location",
      description: "Find the business location with integrated maps",
      bottom: "30%",
      left: "-5%",
      delay: 0.3,
    },
    {
      id: 4,
      icon: ShoppingBag,
      label: "Products & Services",
      description: "Browse available products and services with pricing",
      bottom: "15%",
      right: "-5%",
      delay: 0.4,
    },
    {
      id: 5,
      icon: MessageCircle,
      label: "Direct Contact",
      description: "Message the business directly through WhatsApp",
      bottom: "-5%",
      left: "30%",
      delay: 0.5,
    },
    {
      id: 6,
      icon: Phone,
      label: "One-Click Call",
      description: "Call the business with a single tap",
      top: "-5%",
      right: "30%",
      delay: 0.6,
    },
    {
      id: 7,
      icon: Star,
      label: "Reviews",
      description: "See ratings and reviews from other customers",
      top: "50%",
      right: "-5%",
      delay: 0.7,
    },
  ];

  return (
    <div className="relative w-full h-full">
      <TooltipProvider>
        {isClient
          ? features.map((feature) => {
              const rotationAngle = (feature.id * 51) % 360;

              return (
                <Tooltip key={feature.id}>
                  <TooltipTrigger asChild>
                    <motion.div
                      className="absolute z-10 hidden md:block hover:scale-110 transition-transform"
                      style={{
                        top: feature.top,
                        left: feature.left,
                        right: feature.right,
                        bottom: feature.bottom,
                      }}
                      initial={{ y: 0 }}
                      animate={{ y: -3 }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        repeatType: "reverse",
                        delay: feature.delay
                      }}
                    >
                      {/* Glow effect behind icon */}
                      <div className="absolute inset-0 rounded-full bg-[var(--brand-gold)]/20 blur-md" />

                      {/* Icon container with futuristic styling */}
                      <div className="relative w-12 h-12 rounded-full bg-white/80 dark:bg-neutral-800/90 shadow-lg flex items-center justify-center border border-[var(--brand-gold)]/30 backdrop-blur-sm">
                        {/* Icon with glow effect */}
                        <feature.icon className="w-6 h-6 text-[var(--brand-gold)] drop-shadow-[0_0_3px_rgba(var(--brand-gold-rgb),0.5)]" />
                      </div>

                      {/* Connection line to card */}
                      <div
                        className="absolute top-1/2 left-1/2 h-0.5 bg-gradient-to-r from-[var(--brand-gold)] via-[var(--brand-gold)]/50 to-transparent"
                        style={{
                          transformOrigin: "0 0",
                          transform: `rotate(${rotationAngle}deg)`,
                          boxShadow: "0 0 5px var(--brand-gold)",
                          width: "50px",
                        }}
                      />
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent
                    side="top"
                    className="max-w-[200px] z-50 bg-white/80 dark:bg-neutral-800/90 backdrop-blur-sm border border-[var(--brand-gold)]/30"
                  >
                    <div>
                      <p className="font-semibold text-[var(--brand-gold)]">
                        {feature.label}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {feature.description}
                      </p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              );
            })
          : null}
      </TooltipProvider>
    </div>
  );
}
