"use server";

// Import types
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { ProductServiceData } from "@/app/(dashboard)/dashboard/business/products/actions";
import { NearbyProduct, DiscoverSearchResult } from "./actions/types";

// Import and re-export async functions
import { searchDiscoverData } from "./actions/locationActions";
import { searchDiscoverCombined } from "./actions/combinedActions";
import { fetchMoreBusinessCardsCombined } from "./actions/businessActions";
import { fetchMoreProductsCombined } from "./actions/productActions";

// Re-export async functions
export {
  searchDiscoverData,
  searchDiscoverCombined,
  fetchMoreBusinessCardsCombined,
  fetchMoreProductsCombined,
};

// Export types for use in client components
export type {
  BusinessCardData,
  ProductServiceData,
  NearbyProduct,
  DiscoverSearchResult,
};
