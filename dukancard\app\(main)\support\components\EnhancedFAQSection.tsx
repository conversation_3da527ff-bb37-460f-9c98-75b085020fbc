"use client";

import { useRef, useState } from "react";
import { motion, AnimatePresence, useInView } from "framer-motion";
import { ChevronDown, HelpCircle } from "lucide-react";
import AnimatedTitle from "./AnimatedTitle";
import { cn } from "@/lib/utils";

interface FAQItem {
  id: number;
  question: string;
  answer: string;
}

interface EnhancedFAQSectionProps {
  faqs: FAQItem[];
  title?: string;
  subtitle?: string;
}

export default function EnhancedFAQSection({
  faqs,
  title = "Frequently Asked Questions",
  subtitle = "Find answers to common questions"
}: EnhancedFAQSectionProps) {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  // Animation variants
  const itemFadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, delay: i * 0.1, ease: "easeOut" },
    }),
  };

  return (
    <section
      ref={sectionRef}
      id="faqs"
      className="py-16 px-4 md:px-6 lg:px-8 relative overflow-hidden max-w-7xl mx-auto"
    >
      <div className="max-w-4xl mx-auto">
        {/* Section Title */}
        <div className="text-center mb-12">
          <AnimatedTitle
            title={title}
            highlightWords={["Questions"]}
            subtitle={subtitle}
            size="medium"
          />
        </div>

        {/* Decorative elements */}
        <motion.div
          className="absolute top-20 left-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5"
          initial={{ opacity: 0, scale: 0.5, rotate: -10 }}
          animate={isInView ? { opacity: 1, scale: 1, rotate: 0 } : {}}
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          <HelpCircle size={120} />
        </motion.div>

        <motion.div
          className="absolute top-1/3 right-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5"
          initial={{ opacity: 0, scale: 0.5, rotate: 10 }}
          animate={isInView ? { opacity: 1, scale: 1, rotate: 0 } : {}}
          transition={{ duration: 0.7, delay: 0.4 }}
        >
          <HelpCircle size={80} />
        </motion.div>

        {/* FAQ Accordion */}
        <div className="space-y-4 relative z-10">
          {faqs.map((item, index) => (
            <motion.div
              key={item.id}
              variants={itemFadeIn}
              custom={index}
              initial="hidden"
              animate={isInView ? "visible" : "hidden"}
              className="border border-border/50 rounded-2xl overflow-hidden bg-card/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {/* Question button */}
              <button
                onClick={() => toggleFAQ(index)}
                className={cn(
                  "flex items-center justify-between w-full p-6 text-left transition-all duration-300",
                  openIndex === index
                    ? "bg-gradient-to-r from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/5"
                    : "hover:bg-muted/30"
                )}
              >
                <h3 className="text-lg font-semibold text-foreground pr-4 leading-relaxed">
                  {item.question}
                </h3>
                <motion.div
                  animate={{ rotate: openIndex === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                  className={cn(
                    "flex-shrink-0 ml-2 p-2 rounded-full transition-all duration-300",
                    openIndex === index
                      ? "bg-[var(--brand-gold)]/20 text-[var(--brand-gold)] shadow-lg"
                      : "text-muted-foreground hover:bg-muted/50"
                  )}
                >
                  <ChevronDown size={20} />
                </motion.div>
              </button>

              {/* Answer with animation */}
              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-6 border-t border-border/30">
                      <motion.div
                        initial={{ y: 10, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                        className="pt-4"
                      >
                        <p className="text-muted-foreground leading-relaxed text-base">
                          {item.answer}
                        </p>
                      </motion.div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        {/* Additional help text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/5 rounded-2xl p-8 border border-[var(--brand-gold)]/20">
            <h3 className="text-xl font-semibold text-foreground mb-3">
              Still have questions?
            </h3>
            <p className="text-muted-foreground mb-4">
              Our support team is ready to help you with any questions or concerns.
            </p>
            <p className="text-muted-foreground">
              Contact us at{" "}
              <span className="text-[var(--brand-gold)] font-semibold">
                <EMAIL>
              </span>
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
