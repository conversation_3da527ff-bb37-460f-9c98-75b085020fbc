import { MetadataRoute } from "next";
// import { getSecureBusinessProfilesForSitemap } from "@/lib/actions/businessProfiles"; // Temporarily commented out

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

// Use ISR with a long revalidation period (24 hours = 86400 seconds)
export const revalidate = 86400;

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";

  // Always include the base domain URL to ensure the sitemap is never empty
  const baseEntries: MetadataRoute.Sitemap = [
    {
      url: siteUrl,
      lastModified: new Date(),
      changeFrequency: "daily",
      priority: 1.0,
    }
  ];

  // TODO: Uncomment the following code when server is upgraded and ready for crawler load
  // This will enable full business cards sitemap generation

  /*
  try {
    // Fetch active business cards using the secure method
    let businessCardEntries: MetadataRoute.Sitemap = [];

    try {
      // Fetch business profiles using the secure method
      const { data: profiles, error } = await getSecureBusinessProfilesForSitemap();

      if (error) {
        return baseEntries; // Return base entries if fetching fails
      }

      if (!profiles || profiles.length === 0) {
        return baseEntries;
      }

      // Define type for profile based on select query
      type ProfileSlug = {
        business_slug: string;
        updated_at: string | null;
      };

      // Filter and map to sitemap entries
      businessCardEntries = profiles
        .filter((profile: ProfileSlug) => {
          const isValid = profile.business_slug && profile.business_slug.trim() !== "";
          return isValid;
        })
        .map((profile: ProfileSlug) => ({
          url: `${siteUrl}/${profile.business_slug}`,
          lastModified: profile.updated_at ? new Date(profile.updated_at) : new Date(),
          changeFrequency: "weekly",
          priority: 0.7,
        }));

      const finalSitemap = [...baseEntries, ...businessCardEntries];
      return finalSitemap;

    } catch (_error) {
      return baseEntries; // Return base entries if any error occurs
    }
  } catch (_error) {
    return baseEntries; // Return base entries if client creation fails
  }
  */

  // Temporarily return only base entries to reduce server load
  return baseEntries;
}
