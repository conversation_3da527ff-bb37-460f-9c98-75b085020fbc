import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { OTPInput } from '@/src/components/ui/OTPInput';
import { useTheme } from '@/src/hooks/useTheme';
import { useScreenDimensions } from '@/src/hooks/use-mobile';

// Mock hooks
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      card: '#ffffff',
      textPrimary: '#000000',
      primary: '#C29D5B',
      border: '#cccccc',
      error: '#ff0000',
    },
  }),
}));

jest.mock('@/src/hooks/use-mobile', () => ({
  useScreenDimensions: () => ({
    width: 400,
    height: 800,
  }),
}));

const mockOnComplete = jest.fn();
const mockOnChangeText = jest.fn();

const renderComponent = (props: Partial<React.ComponentProps<typeof OTPInput>> = {}) => {
  return render(
    <OTPInput
      onComplete={mockOnComplete}
      onChangeText={mockOnChangeText}
      {...props}
    />
  );
};

describe('<OTPInput />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the correct number of inputs', () => {
    const { getAllByRole } = renderComponent({ length: 4 });
    expect(getAllByRole('text')).toHaveLength(4);
  });

  it('calls onComplete when all digits are entered', () => {
    const { getAllByRole } = renderComponent();
    const inputs = getAllByRole('text');
    fireEvent.changeText(inputs[0], '1');
    fireEvent.changeText(inputs[1], '2');
    fireEvent.changeText(inputs[2], '3');
    fireEvent.changeText(inputs[3], '4');
    fireEvent.changeText(inputs[4], '5');
    fireEvent.changeText(inputs[5], '6');
    expect(mockOnComplete).toHaveBeenCalledWith('123456');
  });

  it('calls onChangeText for every change', () => {
    const { getAllByRole } = renderComponent();
    const inputs = getAllByRole('text');
    fireEvent.changeText(inputs[0], '1');
    expect(mockOnChangeText).toHaveBeenCalledWith('1');
    fireEvent.changeText(inputs[1], '2');
    expect(mockOnChangeText).toHaveBeenCalledWith('12');
  });

  it('handles pasting an OTP', () => {
    const { getAllByRole } = renderComponent();
    const inputs = getAllByRole('text');
    fireEvent.changeText(inputs[0], '123456');
    expect(mockOnComplete).toHaveBeenCalledWith('123456');
    expect(mockOnChangeText).toHaveBeenCalledWith('123456');
  });

  it('handles backspace correctly', () => {
    const { getAllByRole } = renderComponent();
    const inputs = getAllByRole('text');
    fireEvent.changeText(inputs[0], '1');
    fireEvent.changeText(inputs[1], '2');
    
    // Press backspace on the second input
    fireEvent(inputs[1], 'keyPress', { nativeEvent: { key: 'Backspace' } });
    expect(mockOnChangeText).toHaveBeenCalledWith('1');

    // Press backspace on the first input (when it's empty)
    fireEvent(inputs[1], 'keyPress', { nativeEvent: { key: 'Backspace' } });
    fireEvent(inputs[0], 'keyPress', { nativeEvent: { key: 'Backspace' } });
    expect(mockOnChangeText).toHaveBeenCalledWith('');
  });

  it('displays an error message', () => {
    const { getByText, toJSON } = renderComponent({ error: 'Invalid OTP' });
    expect(getByText('Invalid OTP')).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it('disables inputs when disabled prop is true', () => {
    const { getAllByRole } = renderComponent({ disabled: true });
    const inputs = getAllByRole('text');
    inputs.forEach(input => {
      expect(input.props.editable).toBe(false);
    });
  });

  it('auto-focuses the first input by default', () => {
    const { getAllByRole, toJSON } = renderComponent();
    const inputs = getAllByRole('text');
    // This is hard to test directly in JSDOM, but we can check the ref assignment
    // and trust the implementation. A snapshot will also capture the initial state.
    expect(toJSON()).toMatchSnapshot();
  });
});