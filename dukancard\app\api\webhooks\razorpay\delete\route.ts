import { NextRequest, NextResponse } from "next/server";
import { deleteWebhook } from "@/lib/razorpay/services/webhook";

/**
 * API route to delete a Razorpay webhook
 *
 * This endpoint deletes an existing webhook for a Razorpay account.
 *
 * URL format:
 * /api/webhooks/razorpay/delete?account_id=acc_H3kYHQ635sBwXG&webhook_id=HK890egfiItP3H
 *
 * Query parameters:
 * - account_id: The Razorpay account ID (required)
 * - webhook_id: The webhook ID to delete (required)
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Webhook deleted successfully"
 * }
 */
export async function DELETE(req: NextRequest) {
  try {
    // Get the query parameters
    const url = new URL(req.url);
    const accountId = url.searchParams.get("account_id");
    const webhookId = url.searchParams.get("webhook_id");

    // Validate required parameters
    if (!accountId) {
      return NextResponse.json(
        { success: false, message: "Missing account_id parameter" },
        { status: 400 }
      );
    }

    if (!webhookId) {
      return NextResponse.json(
        { success: false, message: "Missing webhook_id parameter" },
        { status: 400 }
      );
    }

    // Delete the webhook
    const result = await deleteWebhook(accountId, webhookId);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return success response
    return NextResponse.json(
      { success: true, message: "Webhook deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK_DELETE] Error deleting webhook:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Error deleting webhook",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
