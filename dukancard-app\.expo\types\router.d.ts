/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/choose-role` | `/choose-role`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/complete-profile` | `/complete-profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business/analytics` | `/business/analytics`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business/customers` | `/business/customers`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business` | `/business`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business/products` | `/business/products`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business/profile` | `/business/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/favorites` | `/customer/favorites`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer` | `/customer`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/notifications` | `/customer/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile` | `/customer/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/components/CustomerMetricsOverview` | `/customer/components/CustomerMetricsOverview`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/AddressForm` | `/customer/profile/components/AddressForm`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/AvatarUpload` | `/customer/profile/components/AvatarUpload`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/ProfilePageClient` | `/customer/profile/components/ProfilePageClient`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/ProfileRequirementDialog` | `/customer/profile/components/ProfileRequirementDialog`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/LinkEmailSection` | `/customer/settings/components/LinkEmailSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/LinkPhoneSection` | `/customer/settings/components/LinkPhoneSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/PasswordUpdateSection` | `/customer/settings/components/PasswordUpdateSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/SettingsPageClient` | `/customer/settings/components/SettingsPageClient`; params?: Router.UnknownInputParams; } | { pathname: `${'/(onboarding)'}/address-information` | `/address-information`; params?: Router.UnknownInputParams; } | { pathname: `${'/(onboarding)'}/business-details` | `/business-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(onboarding)'}/card-information` | `/card-information`; params?: Router.UnknownInputParams; } | { pathname: `${'/(onboarding)'}/plan-selection` | `/plan-selection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/business/[businessSlug]`, params: Router.UnknownInputParams & { businessSlug: string | number; } } | { pathname: `/post/[postId]`, params: Router.UnknownInputParams & { postId: string | number; } } | { pathname: `/product/[productId]`, params: Router.UnknownInputParams & { productId: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/choose-role` | `/choose-role`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/complete-profile` | `/complete-profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/business/analytics` | `/business/analytics`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/business/customers` | `/business/customers`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/business` | `/business`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/business/products` | `/business/products`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/business/profile` | `/business/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/favorites` | `/customer/favorites`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer` | `/customer`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/notifications` | `/customer/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/profile` | `/customer/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/components/CustomerMetricsOverview` | `/customer/components/CustomerMetricsOverview`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/AddressForm` | `/customer/profile/components/AddressForm`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/AvatarUpload` | `/customer/profile/components/AvatarUpload`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/ProfilePageClient` | `/customer/profile/components/ProfilePageClient`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/ProfileRequirementDialog` | `/customer/profile/components/ProfileRequirementDialog`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/LinkEmailSection` | `/customer/settings/components/LinkEmailSection`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/LinkPhoneSection` | `/customer/settings/components/LinkPhoneSection`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/PasswordUpdateSection` | `/customer/settings/components/PasswordUpdateSection`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/SettingsPageClient` | `/customer/settings/components/SettingsPageClient`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(onboarding)'}/address-information` | `/address-information`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(onboarding)'}/business-details` | `/business-details`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(onboarding)'}/card-information` | `/card-information`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(onboarding)'}/plan-selection` | `/plan-selection`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/business/[businessSlug]`, params: Router.UnknownOutputParams & { businessSlug: string; } } | { pathname: `/post/[postId]`, params: Router.UnknownOutputParams & { postId: string; } } | { pathname: `/product/[productId]`, params: Router.UnknownOutputParams & { productId: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/choose-role${`?${string}` | `#${string}` | ''}` | `/choose-role${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/complete-profile${`?${string}` | `#${string}` | ''}` | `/complete-profile${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/login${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/business/analytics${`?${string}` | `#${string}` | ''}` | `/business/analytics${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/business/customers${`?${string}` | `#${string}` | ''}` | `/business/customers${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/business${`?${string}` | `#${string}` | ''}` | `/business${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/business/products${`?${string}` | `#${string}` | ''}` | `/business/products${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/business/profile${`?${string}` | `#${string}` | ''}` | `/business/profile${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/favorites${`?${string}` | `#${string}` | ''}` | `/customer/favorites${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer${`?${string}` | `#${string}` | ''}` | `/customer${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/notifications${`?${string}` | `#${string}` | ''}` | `/customer/notifications${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/profile${`?${string}` | `#${string}` | ''}` | `/customer/profile${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/components/CustomerMetricsOverview${`?${string}` | `#${string}` | ''}` | `/customer/components/CustomerMetricsOverview${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/profile/components/AddressForm${`?${string}` | `#${string}` | ''}` | `/customer/profile/components/AddressForm${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/profile/components/AvatarUpload${`?${string}` | `#${string}` | ''}` | `/customer/profile/components/AvatarUpload${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/profile/components/ProfilePageClient${`?${string}` | `#${string}` | ''}` | `/customer/profile/components/ProfilePageClient${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/profile/components/ProfileRequirementDialog${`?${string}` | `#${string}` | ''}` | `/customer/profile/components/ProfileRequirementDialog${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/settings/components/LinkEmailSection${`?${string}` | `#${string}` | ''}` | `/customer/settings/components/LinkEmailSection${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/settings/components/LinkPhoneSection${`?${string}` | `#${string}` | ''}` | `/customer/settings/components/LinkPhoneSection${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/settings/components/PasswordUpdateSection${`?${string}` | `#${string}` | ''}` | `/customer/settings/components/PasswordUpdateSection${`?${string}` | `#${string}` | ''}` | `${'/(dashboard)'}/customer/settings/components/SettingsPageClient${`?${string}` | `#${string}` | ''}` | `/customer/settings/components/SettingsPageClient${`?${string}` | `#${string}` | ''}` | `${'/(onboarding)'}/address-information${`?${string}` | `#${string}` | ''}` | `/address-information${`?${string}` | `#${string}` | ''}` | `${'/(onboarding)'}/business-details${`?${string}` | `#${string}` | ''}` | `/business-details${`?${string}` | `#${string}` | ''}` | `${'/(onboarding)'}/card-information${`?${string}` | `#${string}` | ''}` | `/card-information${`?${string}` | `#${string}` | ''}` | `${'/(onboarding)'}/plan-selection${`?${string}` | `#${string}` | ''}` | `/plan-selection${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/explore${`?${string}` | `#${string}` | ''}` | `/explore${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/choose-role` | `/choose-role`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/complete-profile` | `/complete-profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business/analytics` | `/business/analytics`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business/customers` | `/business/customers`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business` | `/business`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business/products` | `/business/products`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/business/profile` | `/business/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/favorites` | `/customer/favorites`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer` | `/customer`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/notifications` | `/customer/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile` | `/customer/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/components/CustomerMetricsOverview` | `/customer/components/CustomerMetricsOverview`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/AddressForm` | `/customer/profile/components/AddressForm`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/AvatarUpload` | `/customer/profile/components/AvatarUpload`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/ProfilePageClient` | `/customer/profile/components/ProfilePageClient`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/profile/components/ProfileRequirementDialog` | `/customer/profile/components/ProfileRequirementDialog`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/LinkEmailSection` | `/customer/settings/components/LinkEmailSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/LinkPhoneSection` | `/customer/settings/components/LinkPhoneSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/PasswordUpdateSection` | `/customer/settings/components/PasswordUpdateSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(dashboard)'}/customer/settings/components/SettingsPageClient` | `/customer/settings/components/SettingsPageClient`; params?: Router.UnknownInputParams; } | { pathname: `${'/(onboarding)'}/address-information` | `/address-information`; params?: Router.UnknownInputParams; } | { pathname: `${'/(onboarding)'}/business-details` | `/business-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(onboarding)'}/card-information` | `/card-information`; params?: Router.UnknownInputParams; } | { pathname: `${'/(onboarding)'}/plan-selection` | `/plan-selection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | `/+not-found${`?${string}` | `#${string}` | ''}` | `/business/${Router.SingleRoutePart<T>}${`?${string}` | `#${string}` | ''}` | `/post/${Router.SingleRoutePart<T>}${`?${string}` | `#${string}` | ''}` | `/product/${Router.SingleRoutePart<T>}${`?${string}` | `#${string}` | ''}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/business/[businessSlug]`, params: Router.UnknownInputParams & { businessSlug: string | number; } } | { pathname: `/post/[postId]`, params: Router.UnknownInputParams & { postId: string | number; } } | { pathname: `/product/[productId]`, params: Router.UnknownInputParams & { productId: string | number; } };
    }
  }
}
