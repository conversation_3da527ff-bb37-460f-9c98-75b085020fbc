"use client";

import React from "react";
import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

interface ProductPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (_page: number) => void;
  onItemsPerPageChange: (_itemsPerPage: number) => void;
  isLoading?: boolean;
  className?: string;
}

export default function ProductPagination({
  currentPage: _currentPage,
  totalPages,
  totalItems,
  itemsPerPage: _itemsPerPage,
  onPageChange,
  onItemsPerPageChange,
  isLoading = false,
  className,
}: ProductPaginationProps) {
  // Don't render if there are no items
  if (totalItems === 0) return null;

  // Calculate display range
  const startItem = (_currentPage - 1) * _itemsPerPage + 1;
  const endItem = Math.min(_currentPage * _itemsPerPage, totalItems);

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages: (number | "ellipsis")[] = [];
    const maxPagesToShow = 7;

    if (totalPages <= maxPagesToShow) {
      // Show all pages if total pages is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      // Calculate start and end of middle section
      let startPage = Math.max(2, _currentPage - 2);
      let endPage = Math.min(totalPages - 1, _currentPage + 2);

      // Adjust if we're near the beginning
      if (_currentPage <= 4) {
        endPage = Math.min(totalPages - 1, 5);
      }

      // Adjust if we're near the end
      if (_currentPage >= totalPages - 3) {
        startPage = Math.max(2, totalPages - 4);
      }

      // Add ellipsis if needed
      if (startPage > 2) {
        pages.push("ellipsis");
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis if needed
      if (endPage < totalPages - 1) {
        pages.push("ellipsis");
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "flex flex-col sm:flex-row items-center justify-between gap-4 py-6",
        className
      )}
    >
      {/* Items info and per-page selector */}
      <div className="flex flex-col sm:flex-row items-center gap-4 text-sm text-neutral-600 dark:text-neutral-400">
        <div className="flex items-center gap-2">
          <span>
            Showing {startItem} to {endItem} of {totalItems} products
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="whitespace-nowrap">Items per page:</span>
          <Select
            value={_itemsPerPage.toString()}
            onValueChange={(value) => onItemsPerPageChange(parseInt(value))}
            disabled={isLoading}
          >
            <SelectTrigger className="w-20 h-8 text-sm border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 focus:ring-2 focus:ring-primary/20 focus:border-primary">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg bg-white dark:bg-neutral-900">
              <SelectItem value="10" className="text-sm focus:bg-primary/10 focus:text-primary">10</SelectItem>
              <SelectItem value="25" className="text-sm focus:bg-primary/10 focus:text-primary">25</SelectItem>
              <SelectItem value="50" className="text-sm focus:bg-primary/10 focus:text-primary">50</SelectItem>
              <SelectItem value="100" className="text-sm focus:bg-primary/10 focus:text-primary">100</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Pagination controls */}
      {totalPages > 1 && (
        <div className="flex items-center gap-1">
          {/* Previous button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(_currentPage - 1)}
            disabled={_currentPage === 1 || isLoading}
            className="h-9 px-3 border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 disabled:opacity-50"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Previous
          </Button>

          {/* Page numbers */}
          <div className="flex items-center gap-1 mx-2">
            {pageNumbers.map((page, index) => {
              if (page === "ellipsis") {
                return (
                  <div
                    key={`ellipsis-${index}`}
                    className="flex items-center justify-center w-9 h-9 text-neutral-400 dark:text-neutral-500"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </div>
                );
              }

              const isActive = _currentPage === page;
              return (
                <Button
                  key={page}
                  variant={isActive ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(page as number)}
                  disabled={isLoading}
                  className={cn(
                    "h-9 w-9 p-0 text-sm",
                    isActive
                      ? "bg-primary hover:bg-primary/90 text-primary-foreground border-primary shadow-sm"
                      : "border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300"
                  )}
                >
                  {page}
                </Button>
              );
            })}
          </div>

          {/* Next button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(_currentPage + 1)}
            disabled={_currentPage === totalPages || isLoading}
            className="h-9 px-3 border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 disabled:opacity-50"
          >
            Next
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      )}
    </motion.div>
  );
}
