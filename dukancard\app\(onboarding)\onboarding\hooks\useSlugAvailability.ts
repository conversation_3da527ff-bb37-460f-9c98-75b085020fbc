"use client";

import { useCallback, useTransition, useEffect, useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import debounce from "lodash/debounce";
import { checkSlugAvailability } from "../actions";
import { OnboardingFormData } from "../types/onboarding";

interface UseSlugAvailabilityOptions {
  form: UseFormReturn<OnboardingFormData>;
  slugToCheck: string;
  setSlugAvailable: (_available: boolean | null) => void;
}

export function useSlugAvailability({
  form,
  slugToCheck,
  setSlugAvailable
}: UseSlugAvailabilityOptions) {
  const [isCheckingSlug, startSlugCheckTransition] = useTransition();

  // --- Slug Availability Check ---
  const performSlugCheck = useCallback(
    async (slug: string) => {
      console.log("Client: Performing slug check for:", slug);

      // Basic validation - if invalid, reset state and return
      if (!slug || slug.length < 3 || !/^[a-z0-9-]+$/.test(slug)) {
        console.log("Client: Slug failed basic validation:", slug);
        setSlugAvailable(null);
        return;
      }

      // Use transition to handle the async operation
      startSlugCheckTransition(async () => {
        try {
          console.log("Client: Calling checkSlugAvailability for:", slug);
          const { available } = await checkSlugAvailability(slug);
          console.log("Client: Slug availability result:", { slug, available });

          setSlugAvailable(available);

          // Handle form errors
          if (!available) {
            console.log("Client: Setting error for unavailable slug:", slug);
            form.setError("businessSlug", {
              type: "manual",
              message: "This URL slug is already taken.",
            });
          } else {
            console.log("Client: Clearing errors for available slug:", slug);
            form.clearErrors("businessSlug");
          }
        } catch (error) {
          console.error("Client: Error checking slug availability:", error);
          setSlugAvailable(false);
          form.setError("businessSlug", {
            type: "manual",
            message: "Error checking slug availability. Please try again.",
          });
        }
      });
    },
    [form, setSlugAvailable, startSlugCheckTransition]
  );

  // Create debounced function
  const debouncedSlugCheck = useMemo(
    () => debounce((slug: string) => performSlugCheck(slug), 500),
    [performSlugCheck]
  );

  // Effect to trigger slug check when slugToCheck changes
  useEffect(() => {
    if (slugToCheck) {
      console.log("useSlugAvailability: Triggering check for:", slugToCheck);
      debouncedSlugCheck(slugToCheck);
    }

    // Cleanup function
    return () => {
      debouncedSlugCheck.cancel();
    };
  }, [slugToCheck, debouncedSlugCheck]);

  return {
    isCheckingSlug,
  };
}
