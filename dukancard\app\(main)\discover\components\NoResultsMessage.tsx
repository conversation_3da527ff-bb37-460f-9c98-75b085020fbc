"use client";

import { motion } from "framer-motion";
import { useSearchParams } from "next/navigation";
import { BUSINESS_NAME_PARAM, PRODUCT_NAME_PARAM } from "../constants/urlParamConstants";

interface NoResultsMessageProps {
  viewType: "cards" | "products";
}

export default function NoResultsMessage({ viewType }: NoResultsMessageProps) {
  const searchParams = useSearchParams();
  const searchTerm = viewType === "cards" 
    ? searchParams.get(BUSINESS_NAME_PARAM) 
    : searchParams.get(PRODUCT_NAME_PARAM);

  return (
    <motion.div
      className="text-center py-16 px-4 bg-transparent rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <div className="max-w-md mx-auto">
        <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-2">
          {viewType === "cards" ? "No Businesses Found" : "No Products Found"}
        </h3>
        <p className="text-neutral-500 dark:text-neutral-400 mb-4">
          We couldn&apos;t find any {viewType === "cards" ? "businesses" : "products"}
          {searchTerm ? ` with "${searchTerm}" in the name` : ""}
          . Try adjusting your search criteria or browse all {viewType === "cards" ? "businesses" : "products"}.
        </p>
        {searchTerm && (
          <button
            className="inline-flex items-center px-4 py-2 bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-lg text-sm font-medium text-neutral-700 dark:text-neutral-300 transition-colors"
            onClick={() => {
              // This would typically clear the search, but we'll leave it as a placeholder
              // since we don't have direct access to the router in this component
              console.log("Clear search clicked");
            }}
          >
            Clear Search
          </button>
        )}
      </div>
    </motion.div>
  );
}
