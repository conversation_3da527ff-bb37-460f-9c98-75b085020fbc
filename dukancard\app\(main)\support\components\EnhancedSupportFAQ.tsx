"use client";

import { useRef, useState } from "react";
import { motion, AnimatePresence, useInView } from "framer-motion";
import { ChevronDown, HelpCircle } from "lucide-react";
import AnimatedTitle from "./AnimatedTitle";
import { cn } from "@/lib/utils";

interface FAQItem {
  id: number;
  question: string;
  answer: string;
  category: string;
}

interface FAQCategory {
  id: string;
  label: string;
}

interface EnhancedSupportFAQProps {
  faqItems: FAQItem[];
  faqCategories: FAQCategory[];
  searchQuery: string;
  activeCategory: string;
  onCategoryChange: (_category: string) => void;
}

export default function EnhancedSupportFAQ({
  faqItems,
  faqCategories,
  searchQuery,
  activeCategory,
  onCategoryChange,
}: EnhancedSupportFAQProps) {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  // Filter FAQs based on search query and active category
  const filteredFaqs = faqItems.filter((faq) => {
    const matchesSearch =
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === faq.category;
    return matchesSearch && matchesCategory;
  });

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  // Animation variants
  const itemFadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, delay: i * 0.1, ease: "easeOut" },
    }),
  };

  return (
    <section
      ref={sectionRef}
      className="py-16 px-4 md:px-6 lg:px-8 relative overflow-hidden"
    >
      <div className="max-w-4xl mx-auto">
        {/* Section Title */}
        <div className="text-center mb-12">
          <AnimatedTitle
            title="Frequently Asked Questions"
            highlightWords={["Questions"]}
            subtitle="Find quick answers to common questions about Dukancard"
            size="medium"
          />
        </div>

        {/* Decorative elements */}
        <motion.div
          className="absolute top-20 left-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5"
          initial={{ opacity: 0, scale: 0.5, rotate: -10 }}
          animate={isInView ? { opacity: 1, scale: 1, rotate: 0 } : {}}
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          <HelpCircle size={120} />
        </motion.div>

        <motion.div
          className="absolute top-1/3 right-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5"
          initial={{ opacity: 0, scale: 0.5, rotate: 10 }}
          animate={isInView ? { opacity: 1, scale: 1, rotate: 0 } : {}}
          transition={{ duration: 0.7, delay: 0.4 }}
        >
          <HelpCircle size={80} />
        </motion.div>

        {/* Enhanced Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="relative z-10 mb-8"
        >
          <div className="flex flex-wrap justify-center gap-2 p-2 bg-muted/30 rounded-2xl backdrop-blur-sm border border-border/50">
            {faqCategories.map((category, index) => (
              <motion.button
                key={category.id}
                onClick={() => onCategoryChange(category.id)}
                className={cn(
                  "px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 relative overflow-hidden",
                  activeCategory === category.id
                    ? "text-[var(--brand-gold-foreground)] shadow-lg"
                    : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, y: 10 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.3, delay: 0.4 + index * 0.05 }}
              >
                {activeCategory === category.id && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] rounded-xl"
                    layoutId="activeTab"
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
                <span className="relative z-10">{category.label}</span>
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* FAQ Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="relative z-10"
          >
            {filteredFaqs.length > 0 ? (
              <div className="space-y-4">
                {filteredFaqs.map((item, index) => (
                  <motion.div
                    key={item.id}
                    variants={itemFadeIn}
                    custom={index}
                    initial="hidden"
                    animate={isInView ? "visible" : "hidden"}
                    className="border border-border rounded-lg overflow-hidden bg-card/50 backdrop-blur-sm"
                  >
                    {/* Question button */}
                    <button
                      onClick={() => toggleFAQ(index)}
                      className={cn(
                        "flex items-center justify-between w-full p-5 text-left transition-colors",
                        openIndex === index
                          ? "bg-muted/50"
                          : "hover:bg-muted/30"
                      )}
                    >
                      <h3 className="text-lg font-medium text-foreground pr-4">{item.question}</h3>
                      <motion.div
                        animate={{ rotate: openIndex === index ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                        className={cn(
                          "flex-shrink-0 ml-2 p-1 rounded-full",
                          openIndex === index
                            ? "bg-[var(--brand-gold)]/20 text-[var(--brand-gold)]"
                            : "text-muted-foreground"
                        )}
                      >
                        <ChevronDown size={18} />
                      </motion.div>
                    </button>

                    {/* Answer with animation */}
                    <AnimatePresence>
                      {openIndex === index && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <div className="p-5 pt-0 border-t border-border">
                            <motion.p
                              initial={{ y: 10, opacity: 0 }}
                              animate={{ y: 0, opacity: 1 }}
                              transition={{ duration: 0.3, delay: 0.1 }}
                              className="text-muted-foreground leading-relaxed"
                            >
                              {item.answer}
                            </motion.p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="text-center py-12 bg-card/30 rounded-2xl border border-border/50"
              >
                <HelpCircle className="w-16 h-16 text-muted-foreground/50 mx-auto mb-4" />
                <p className="text-muted-foreground text-lg mb-4">
                  No FAQs found matching your search criteria.
                </p>
                <p className="text-sm text-muted-foreground/70 mb-6">
                  Try adjusting your search terms or browse other categories.
                </p>
              </motion.div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Additional help text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="text-center mt-12 text-muted-foreground"
        >
          <p>
            Still have questions? Contact our support team at{" "}
            <span className="text-[var(--brand-gold)] font-medium">
              <EMAIL>
            </span>
          </p>
        </motion.div>
      </div>
    </section>
  );
}
