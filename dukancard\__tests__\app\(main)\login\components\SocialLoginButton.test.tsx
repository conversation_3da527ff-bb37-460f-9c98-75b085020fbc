import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SocialLoginButton } from '@/app/(main)/login/components/SocialLoginButton';
import { createClient } from '@/utils/supabase/client';
import { toast } from 'sonner';

// Mock Supabase client
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(),
}));

// Mock sonner
jest.mock('sonner', () => ({
  toast: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

describe('SocialLoginButton', () => {
  let mockSupabase: { auth: { signInWithOAuth: jest.Mock } };

  beforeEach(() => {
    mockSupabase = {
      auth: {
        signInWithOAuth: jest.fn(),
      },
    };
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the google login button', () => {
    render(<SocialLoginButton />);
    expect(screen.getByRole('button', { name: /login with google/i })).toBeInTheDocument();
  });

  it('calls signInWithOAuth on button click', async () => {
    mockSupabase.auth.signInWithOAuth.mockResolvedValue({ data: { url: 'https://google.com' }, error: null });
    render(<SocialLoginButton />);
    fireEvent.click(screen.getByRole('button', { name: /login with google/i }));
    await waitFor(() => {
      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: expect.stringContaining('/auth/callback'),
          skipBrowserRedirect: true,
          queryParams: {
            access_type: 'offline',
            prompt: 'select_account',
          },
        },
      });
    });
  });

  it('shows info toast when social login is initiated', async () => {
    mockSupabase.auth.signInWithOAuth.mockResolvedValue({ data: { url: 'https://google.com' }, error: null });
    render(<SocialLoginButton />);
    fireEvent.click(screen.getByRole('button', { name: /login with google/i }));
    await waitFor(() => {
      expect(toast.info).toHaveBeenCalledWith('Google sign-in opened in a new tab', {
        description: 'Please complete the sign-in process in the new tab.',
        duration: 5000,
      });
    });
  });
});