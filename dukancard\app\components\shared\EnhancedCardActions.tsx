"use client";

import { useState, useRef, useEffect } from "react";
import { toast } from "sonner";
import { Download, FileDown, QrCode as QrCodeIcon, CreditCard } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import QRCode from "react-qr-code";
import { generateAndDownloadQRCode, downloadRawQRImage } from "@/lib/qrCodeGenerator";
import { downloadBusinessCard, findBusinessCardElement } from "@/lib/cardDownloader";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

interface EnhancedCardActionsProps {
  businessSlug: string;
  businessName: string;
  ownerName?: string;
  businessAddress?: string;
  themeColor?: string;
  className?: string;
}

export default function EnhancedCardActions({
  businessSlug,
  businessName,
  ownerName = "",
  businessAddress = "",
  themeColor = "#F59E0B",
  className,
}: EnhancedCardActionsProps) {
  const [qrCodeSvg, setQrCodeSvg] = useState<SVGSVGElement | null>(null);
  const qrCodeRef = useRef<HTMLDivElement>(null);
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true after component mounts
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Get QR code SVG element after component mounts
  useEffect(() => {
    if (qrCodeRef.current) {
      const svg = qrCodeRef.current.querySelector("svg");
      if (svg instanceof SVGSVGElement) {
        setQrCodeSvg(svg);
      }
    }
  }, []);

  // Download A4 formatted QR code
  const handleDownloadA4QR = async () => {
    if (!businessSlug) {
      toast.error("Business slug not available.");
      return;
    }

    if (!qrCodeSvg) {
      toast.error("QR code not available for download.");
      return;
    }

    try {
      // Format the address for display
      const formattedAddress =
        businessAddress.trim() || "Address not available";
      const formattedOwnerName = ownerName.trim() || "Owner";

      await generateAndDownloadQRCode(qrCodeSvg, {
        businessName,
        ownerName: formattedOwnerName,
        address: formattedAddress,
        slug: businessSlug,
        qrValue: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessSlug}`,
        themeColor,
      });

      toast.success("A4 QR code downloaded!");
    } catch (error) {
      console.error("Error generating QR code:", error);
      toast.error("Could not download QR code.");
    }
  };

  // Download raw QR image
  const handleDownloadRawQR = async () => {
    if (!businessSlug) {
      toast.error("Business slug not available.");
      return;
    }

    if (!qrCodeSvg) {
      toast.error("QR code not available for download.");
      return;
    }

    try {
      await downloadRawQRImage(qrCodeSvg, businessSlug);
      toast.success("High-quality QR image downloaded!");
    } catch (error) {
      console.error("Error downloading QR image:", error);
      toast.error("Could not download QR image.");
    }
  };

  // Download business card as PNG
  const handleDownloadCardPNG = async () => {
    if (!businessSlug) {
      toast.error("Business slug not available.");
      return;
    }

    try {
      // Find the business card element
      let cardElement = findBusinessCardElement();

      // Additional validation to ensure we have the right element
      if (cardElement) {
        const rect = cardElement.getBoundingClientRect();

        // If the element is too large, it might be a container, try to find the actual card
        if (rect.width > 500) {
          const actualCard = cardElement.querySelector('[data-card-element]') as HTMLElement;
          if (actualCard) {
            cardElement = actualCard;
          }
        }
      }

      if (!cardElement) {
        toast.error("Business card not found for download.");
        return;
      }

      await downloadBusinessCard(cardElement, {
        businessName,
        businessSlug,
      });

      toast.success("Digital card downloaded as PNG!");
    } catch (error) {
      console.error("Error downloading business card as PNG:", error);
      toast.error("Could not download business card.");
    }
  };



  // Hidden QR code for download
  const qrValue = businessSlug ? `https://dukancard.in/${businessSlug}` : "";

  return (
    <div className={cn("w-full max-w-sm mx-auto space-y-5 mt-6", className)}>
      {/* Hidden QR code for download */}
      <div className="hidden">
        <div id="public-card-qrcode" ref={qrCodeRef}>
          <QRCode
            value={qrValue}
            size={300}
            level="M"
            bgColor="#FFFFFF"
            fgColor="#000000"
          />
        </div>
      </div>

      {/* Enhanced Download Button with Glow Effect */}
      <div className="w-full relative group">
        {/* Button glow effect with properly rounded corners */}
        {isClient && (
          <motion.div
            className="absolute -inset-0.5 rounded-full blur-md"
            style={{
              background: "linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))"
            }}
            initial={{ opacity: 0.7 }}
            animate={{
              opacity: [0.7, 0.9, 0.7],
              boxShadow: [
                "0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)",
                "0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)",
                "0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)"
              ]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "loop",
              ease: "easeInOut"
            }}
          />
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              className={cn(
                "w-full py-6 relative overflow-hidden group",
                "bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90",
                "text-black dark:text-neutral-900 font-medium text-base",
                "border-none rounded-full shadow-lg hover:shadow-xl",
                "transition-all duration-300 ease-out"
              )}
            >
              {/* Shimmer effect */}
              <span className="absolute inset-0 w-full h-full overflow-hidden">
                <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none" />
              </span>

              <div className="flex items-center justify-center gap-3 relative z-10">
                <div className="bg-white/20 p-2 rounded-lg">
                  <Download className="h-5 w-5 text-black dark:text-neutral-900" />
                </div>
                <span className="text-black dark:text-neutral-900 font-semibold">Download Options</span>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center" className="w-56 bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800">
            <DropdownMenuItem onClick={handleDownloadCardPNG} className="cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800">
              <CreditCard className="mr-2 h-4 w-4" />
              <span>Download Digital Card (PNG)</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDownloadA4QR} className="cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800">
              <FileDown className="mr-2 h-4 w-4" />
              <span>Download A4 Size QR</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDownloadRawQR} className="cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800">
              <QrCodeIcon className="mr-2 h-4 w-4" />
              <span>Download High-Quality QR Image</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>


    </div>
  );
}
