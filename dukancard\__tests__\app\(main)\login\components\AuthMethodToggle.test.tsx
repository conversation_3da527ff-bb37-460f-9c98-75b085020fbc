import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { AuthMethodToggle } from '@/app/(main)/login/components/AuthMethodToggle';

describe('AuthMethodToggle', () => {
  it('renders correctly when visible', () => {
    const onMethodChange = jest.fn();
    render(
      <AuthMethodToggle
        authMethod="email-otp"
        step="email"
        onMethodChange={onMethodChange}
      />
    );
    expect(screen.getByRole('button', { name: /email otp/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /mobile \+ password/i })).toBeInTheDocument();
  });

  it('calls onMethodChange when a button is clicked', () => {
    const onMethodChange = jest.fn();
    render(
      <AuthMethodToggle
        authMethod="email-otp"
        step="email"
        onMethodChange={onMethodChange}
      />
    );
    fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));
    expect(onMethodChange).toHaveBeenCalledWith('mobile-password');
  });

  it('does not render when in OTP step', () => {
    const onMethodChange = jest.fn();
    const { container } = render(
      <AuthMethodToggle
        authMethod="email-otp"
        step="otp"
        onMethodChange={onMethodChange}
      />
    );
    expect(container).toBeEmptyDOMElement();
  });
});