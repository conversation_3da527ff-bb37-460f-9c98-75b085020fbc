"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2, Star } from "lucide-react";
import Link from "next/link";
import { ReviewWithUser } from "@/lib/actions/reviews";
import ReviewCard from "@/app/components/shared/reviews/ReviewCard";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface ReviewsListProps {
  reviews: ReviewWithUser[];
  totalReviews: number;
  totalPages: number;
  currentPage: number;
  isLoadingReviews: boolean;
  isAuthenticated: boolean;
  currentUserId?: string | null;
  onPageChange: (_page: number) => void;
  onDeleteReview: () => void;
}

export default function ReviewsList({
  reviews,
  totalReviews,
  totalPages,
  currentPage,
  isLoadingReviews,
  isAuthenticated,
  currentUserId,
  onPageChange,
  onDeleteReview,
}: ReviewsListProps) {
  // Generate pagination items
  const generatePaginationItems = () => {
    const items = [];

    // Always show first page
    items.push(
      <PaginationItem key="page-1">
        <PaginationLink
          href="#"
          size="icon"
          onClick={(e) => {
            e.preventDefault();
            onPageChange(1);
          }}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );

    // Show ellipsis if needed
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Show current page and surrounding pages
    for (
      let i = Math.max(2, currentPage - 1);
      i <= Math.min(totalPages - 1, currentPage + 1);
      i++
    ) {
      if (i === 1 || i === totalPages) continue; // Skip first and last page as they're always shown

      items.push(
        <PaginationItem key={`page-${i}`}>
          <PaginationLink
            href="#"
            size="icon"
            onClick={(e) => {
              e.preventDefault();
              onPageChange(i);
            }}
            isActive={currentPage === i}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    // Show ellipsis if needed
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Always show last page if there's more than one page
    if (totalPages > 1) {
      items.push(
        <PaginationItem key={`page-${totalPages}`}>
          <PaginationLink
            href="#"
            size="icon"
            onClick={(e) => {
              e.preventDefault();
              onPageChange(totalPages);
            }}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    <div className="space-y-6">
      <motion.h3
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-200 pl-1"
      >
        {isLoadingReviews ? (
          <span className="flex items-center gap-2">
            <Loader2 className="w-4 h-4 animate-spin text-[var(--brand-gold)]" />
            Loading reviews...
          </span>
        ) : reviews.length > 0 ? (
          <span>Customer Reviews ({totalReviews})</span>
        ) : (
          <span>Reviews</span>
        )}
      </motion.h3>

      {isLoadingReviews ? (
        // Enhanced loading skeleton with staggered animation
        <div className="space-y-6">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: i * 0.1 }}
              className="p-4 sm:p-6 border border-neutral-200 dark:border-neutral-700 rounded-xl bg-white dark:bg-neutral-900 animate-pulse shadow-sm overflow-hidden"
            >
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <div className="h-12 w-12 rounded-full bg-neutral-200 dark:bg-neutral-700 shadow-sm"></div>
                  <div className="space-y-2">
                    <div className="h-5 w-32 bg-neutral-200 dark:bg-neutral-700 rounded-md"></div>
                    <div className="h-3 w-24 bg-neutral-200 dark:bg-neutral-700 rounded-md"></div>
                  </div>
                </div>
                <div className="h-6 w-20 bg-neutral-200 dark:bg-neutral-700 rounded-md"></div>
              </div>
              <div className="flex mb-4 bg-neutral-100 dark:bg-neutral-800 p-2 rounded-lg">
                {[...Array(5)].map((_, j) => (
                  <div
                    key={j}
                    className="h-5 w-5 mx-0.5 bg-neutral-200 dark:bg-neutral-700 rounded-full"
                  ></div>
                ))}
              </div>
              <div className="space-y-2 pl-6">
                <div className="h-4 w-full bg-neutral-200 dark:bg-neutral-700 rounded-md"></div>
                <div className="h-4 w-full bg-neutral-200 dark:bg-neutral-700 rounded-md"></div>
                <div className="h-4 w-3/4 bg-neutral-200 dark:bg-neutral-700 rounded-md"></div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : reviews.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4 }}
          className="space-y-6"
        >
          {/* Reviews with staggered animation */}
          {reviews.map((review, index) => (
            <motion.div
              key={review.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
            >
              <ReviewCard
                review={{
                  id: review.id,
                  rating: review.rating,
                  review_text: review.review_text,
                  created_at: review.created_at,
                  updated_at: review.updated_at,
                  business_profile_id: '', // This will need to be provided from the business context
                  user_id: review.user_id,
                  business_profiles: {
                    id: review.user_id,
                    business_name: review.user_profile?.name || "Anonymous User",
                    business_slug: review.user_profile?.business_slug || null,
                    logo_url: review.user_profile?.avatar_url || null,
                  },
                  reviewer_type: review.user_profile?.is_business ? 'business' : 'customer',
                  reviewer_slug: review.user_profile?.business_slug || null,
                }}
                onDeleteSuccess={
                  currentUserId === review.user_id ? () => onDeleteReview() : null
                }
              />
            </motion.div>
          ))}

          {/* Enhanced Pagination */}
          {totalPages > 1 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
            >
              <Pagination className="mt-6 sm:mt-8 overflow-x-auto pb-2">
                <PaginationContent>
                  {currentPage > 1 && (
                    <PaginationItem>
                      <motion.div
                        whileHover={{ x: -2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <PaginationPrevious
                          href="#"
                          size="default"
                          onClick={(e) => {
                            e.preventDefault();
                            onPageChange(Math.max(1, currentPage - 1));
                          }}
                          className="border-neutral-200 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200 cursor-pointer"
                        />
                      </motion.div>
                    </PaginationItem>
                  )}

                  {generatePaginationItems().map((item, index) => (
                    <motion.div
                      key={`pagination-${index}`}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: 0.3 + index * 0.05 }}
                    >
                      {item}
                    </motion.div>
                  ))}

                  {currentPage < totalPages && (
                    <PaginationItem>
                      <motion.div
                        whileHover={{ x: 2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <PaginationNext
                          href="#"
                          size="default"
                          onClick={(e) => {
                            e.preventDefault();
                            onPageChange(Math.min(totalPages, currentPage + 1));
                          }}
                          className="border-neutral-200 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200 cursor-pointer"
                        />
                      </motion.div>
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </motion.div>
          )}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="p-6 sm:p-10 text-center border border-dashed border-neutral-200 dark:border-neutral-700 rounded-xl bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-900/50 dark:to-neutral-800/50 shadow-sm overflow-hidden"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-neutral-100 dark:bg-neutral-800 w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4"
          >
            <Star className="w-6 h-6 sm:w-8 sm:h-8 text-neutral-400 dark:text-neutral-500" />
          </motion.div>

          <motion.h4
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-base sm:text-lg font-semibold text-neutral-700 dark:text-neutral-300 mb-2"
          >
            No reviews yet
          </motion.h4>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="text-neutral-600 dark:text-neutral-400 max-w-md mx-auto"
          >
            {isAuthenticated ? (
              <span className="text-[var(--brand-gold)] font-medium">
                Be the first to share your experience!
              </span>
            ) : (
              <span>
                <Link
                  href={`/login?redirect=${encodeURIComponent(
                    window.location.pathname.substring(1)
                  )}`}
                  className="text-[var(--brand-gold)] hover:underline font-medium"
                >
                  Sign in
                </Link>{" "}
                to leave a review and help others discover this business.
              </span>
            )}
          </motion.p>
        </motion.div>
      )}
    </div>
  );
}
