/**
 * <PERSON><PERSON> (Mock Service Worker) handlers for mocking external API calls
 * Only mocks external services, not internal Supabase calls
 */

import { http, HttpResponse } from 'msw';

// Mock handlers for external services only
export const handlers = [
  // Mock external email service (if any)
  http.post('https://api.external-email-service.com/*', () => {
    return HttpResponse.json({ success: true });
  }),

  // Mock external SMS service (if any)
  http.post('https://api.external-sms-service.com/*', () => {
    return HttpResponse.json({ success: true });
  }),

  // Mock external analytics services
  http.post('https://api.analytics-service.com/*', () => {
    return HttpResponse.json({ success: true });
  }),

  // Mock external monitoring services
  http.post('https://api.monitoring-service.com/*', () => {
    return HttpResponse.json({ success: true });
  }),

  // Mock Google OAuth (if used for social login)
  http.get('https://accounts.google.com/oauth/*', () => {
    return HttpResponse.json({
      access_token: 'mock-google-token',
      token_type: 'Bearer',
      expires_in: 3600,
    });
  }),

  // Mock any external CDN or asset requests
  http.get('https://cdn.external-service.com/*', () => {
    return HttpResponse.text('mock-asset-content');
  }),

  // Catch-all for any other external requests that should not reach real services
  http.get('https://external-api.example.com/*', () => {
    return HttpResponse.json({ mocked: true });
  }),

  // Mock any webhook endpoints that might be called during testing
  http.post('https://webhook.external-service.com/*', () => {
    return HttpResponse.json({ received: true });
  }),
];

// Error handlers for testing error scenarios
export const errorHandlers = [
  // Network error simulation
  http.post('https://api.external-email-service.com/error', () => {
    return HttpResponse.error();
  }),

  // Timeout simulation
  http.post('https://api.external-email-service.com/timeout', async () => {
    await new Promise(resolve => setTimeout(resolve, 10000)); // Long delay
    return HttpResponse.json({ success: true });
  }),

  // Rate limit simulation
  http.post('https://api.external-email-service.com/rate-limit', () => {
    return HttpResponse.json(
      { error: 'Rate limit exceeded' },
      { status: 429 }
    );
  }),

  // Server error simulation
  http.post('https://api.external-email-service.com/server-error', () => {
    return HttpResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }),
];

// Handlers for specific test scenarios
export const scenarioHandlers = {
  // Successful external service responses
  success: [
    ...handlers,
  ],

  // External service failures
  failure: [
    ...errorHandlers,
  ],

  // Mixed scenarios
  mixed: [
    ...handlers,
    ...errorHandlers,
  ],
};

// Helper function to setup MSW for tests
export const setupMSW = (scenario: 'success' | 'failure' | 'mixed' = 'success') => {
  const { setupServer } = require('msw/node');
  
  const server = setupServer(...scenarioHandlers[scenario]);
  
  return {
    server,
    start: () => {
      server.listen({
        onUnhandledRequest: 'warn', // Warn about unhandled requests
      });
    },
    stop: () => {
      server.close();
    },
    reset: () => {
      server.resetHandlers();
    },
    use: (...newHandlers: Parameters<typeof server.use>) => {
      server.use(...newHandlers);
    },
  };
};

// Custom handlers for specific test cases
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const createCustomHandler = (url: string, response: any, status = 200) => {
  return http.post(url, () => {
    return HttpResponse.json(response, { status });
  });
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const createDelayedHandler = (url: string, response: any, delay = 1000) => {
  return http.post(url, async () => {
    await new Promise(resolve => setTimeout(resolve, delay));
    return HttpResponse.json(response);
  });
};

// Note: We intentionally do NOT mock Supabase endpoints here
// because we want to test our actual Supabase integration logic
// with mocked Supabase client methods instead of mocking HTTP calls
