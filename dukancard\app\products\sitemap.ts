import { MetadataRoute } from "next";
// import { getSecureProductsForSitemap } from "@/lib/actions/products/sitemapHelpers"; // Temporarily commented out

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

// Use ISR with a long revalidation period (24 hours = 86400 seconds)
export const revalidate = 86400;

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";

  // Always include the base domain URL to ensure the sitemap is never empty
  const baseEntries: MetadataRoute.Sitemap = [
    {
      url: siteUrl,
      lastModified: new Date(),
      changeFrequency: "daily",
      priority: 1.0,
    }
  ];

  // TODO: Uncomment the following code when server is upgraded and ready for crawler load
  // This will enable full products sitemap generation

  /*
  try {
    // Fetch active products using the secure method
    let productEntries: MetadataRoute.Sitemap = [];

    try {
      // Fetch products using the secure method
      const { data: products, error } = await getSecureProductsForSitemap();

      if (error) {
        return baseEntries; // Return base entries if fetching fails
      }

      if (!products || products.length === 0) {
        return baseEntries;
      }

      // Define type for product based on select query
      type ProductData = {
        product_slug: string;
        business_slug: string;
        updated_at: string | null;
      };

      // Filter and map to sitemap entries
      productEntries = products
        .filter((product: ProductData) => {
          const isValid =
            product.product_slug &&
            product.product_slug.trim() !== "" &&
            product.business_slug &&
            product.business_slug.trim() !== "";

          return isValid;
        })
        .map((product: ProductData) => ({
          url: `${siteUrl}/${product.business_slug}/product/${product.product_slug}`,
          lastModified: product.updated_at ? new Date(product.updated_at) : new Date(),
          changeFrequency: "weekly",
          priority: 0.6, // Slightly lower priority than business cards
        }));

      const finalSitemap = [...baseEntries, ...productEntries];
      return finalSitemap;

    } catch (_error) {
      return baseEntries; // Return base entries if any error occurs
    }
  } catch (_error) {
    return baseEntries; // Return base entries if client creation fails
  }
  */

  // Temporarily return only base entries to reduce server load
  return baseEntries;
}
