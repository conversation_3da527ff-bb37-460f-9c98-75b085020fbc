import { Metadata } from "next";
import { Suspense } from "react";
import { notFound } from "next/navigation";
import LocalityPageClient from "./LocalityPageClient";
import ModernResultsSkeleton from "@/app/(main)/discover/ModernResultsSkeleton";
import { getSecureBusinessProfilesByLocation } from "@/lib/actions/businessProfiles";
import { getLocationBySlug } from "@/lib/actions/location/locationBySlug";

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

interface LocalityPageProps {
  params: Promise<{
    localSlug: string;
  }>;
}

export async function generateMetadata({ params }: LocalityPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { localSlug } = resolvedParams;

  // Get location details from slug
  const { data: locationData, error } = await getLocationBySlug(localSlug);

  if (error || !locationData) {
    return {
      title: "Location Not Found",
      description: "The requested location could not be found."
    };
  }

  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/locality/${localSlug}`;

  // Format location for SEO title
  const locationTitle = `${locationData.OfficeName}, ${locationData.DivisionName}, ${locationData.District}, ${locationData.StateName} - ${locationData.Pincode}`;

  return {
    title: `Discover Businesses & Products in ${locationData.OfficeName}`,
    description: `Find local businesses, products, and services in ${locationTitle} with Dukancard. Search, compare, and connect with local merchants.`,
    alternates: {
      canonical: pageUrl,
    },
    openGraph: {
      title: `Discover Businesses & Products in ${locationData.OfficeName}`,
      description: `Find local businesses, products, and services in ${locationTitle} with Dukancard. Search, compare, and connect with local merchants.`,
      url: pageUrl,
      siteName: "Dukancard",
      type: "website",
      locale: "en_IN",
    },
    twitter: {
      card: "summary_large_image",
      title: `Discover Businesses & Products in ${locationData.OfficeName}`,
      description: `Find local businesses, products, and services in ${locationTitle} with Dukancard. Search, compare, and connect with local merchants.`,
    },
  };
}

// Server Component - Handles initial rendering
async function LocalityPageContent({ params }: LocalityPageProps) {
  const resolvedParams = await params;
  const { localSlug } = resolvedParams;

  // Get location details from slug
  const { data: locationData, error } = await getLocationBySlug(localSlug);

  if (error || !locationData) {
    notFound();
  }

  // Create a serializable version of the locality
  const serializableLocality = {
    name: locationData.OfficeName,
    pincode: locationData.Pincode,
    divisionName: locationData.DivisionName,
    district: locationData.District,
    stateName: locationData.StateName,
    slug: localSlug
  };

  // Fetch businesses in this locality
  const { data: businesses, count } = await getSecureBusinessProfilesByLocation(
    { pincode: locationData.Pincode },
    1,
    20
  );

  return (
    <LocalityPageClient
      locality={serializableLocality}
      initialBusinesses={businesses || []}
      totalCount={count || 0}
    />
  );
}

// Main Page Component using Suspense for streaming
export default async function LocalityPage({ params }: LocalityPageProps) {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense fallback={<ModernResultsSkeleton />}>
        <LocalityPageContent params={params} />
      </Suspense>
    </div>
  );
}
