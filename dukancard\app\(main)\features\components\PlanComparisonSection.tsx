"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import FeatureComparisonTable from "../../components/shared/FeatureComparisonTable";
import { pricingPlans } from "@/lib/PricingPlans";


export default function PlanComparisonSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });

  // Get all plans for comparison
  const plans = pricingPlans("monthly");

  return (
    <section
      ref={sectionRef}
      className="py-16 px-4 md:px-6 lg:px-8 relative overflow-hidden"
    >
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.7 }}
        >
          <FeatureComparisonTable
            plans={plans}
            title="Feature Comparison"
            subtitle="Compare features across plans to find the perfect fit for your business needs."
          />
        </motion.div>
      </div>
    </section>
  );
}
