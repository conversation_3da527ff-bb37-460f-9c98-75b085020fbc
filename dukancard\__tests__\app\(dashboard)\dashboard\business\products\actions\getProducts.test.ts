import { getProductServices } from '@/app/(dashboard)/dashboard/business/products/actions/getProducts';
import { createClient } from '@/utils/supabase/server';

// Mock the Supabase client
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('getProductServices', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return an error if the user is not authenticated', async () => {
    // Arrange
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
      },
    });

    // Act
    const result = await getProductServices();

    // Assert
    expect(result.error).toBe('User not authenticated.');
  });

  it('should fetch products for an authenticated user', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProducts = [{
      id: 'prod-1',
      name: 'Product 1',
      product_type: 'product',
      description: 'Test product',
      base_price: 100,
      discounted_price: 90,
      is_available: true,
      image_url: null,
      images: [],
      featured_image_index: 0,
      slug: 'product-1',
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      product_variants: []
    }];
    const mockSelect = jest.fn().mockReturnThis();
    const mockEq = jest.fn().mockReturnThis();
    const mockOrder = jest.fn().mockReturnThis();
    const mockRange = jest.fn().mockResolvedValue({ data: mockProducts, error: null, count: 1 });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => ({
        select: mockSelect,
        eq: mockEq,
        order: mockOrder,
        range: mockRange,
      })),
    });

    // Act
    const result = await getProductServices();

    // Assert
    expect(mockSupabase().from).toHaveBeenCalledWith('products_services');
    expect(mockEq).toHaveBeenCalledWith('business_id', mockUser.id);
    expect(result.data).toHaveLength(1);
    expect(result.count).toBe(1);
  });

  it('should apply search term filter', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockOr = jest.fn().mockReturnThis();
    const mockOrder = jest.fn().mockReturnThis();
    const mockRange = jest.fn().mockResolvedValue({ data: [], error: null, count: 0 });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        or: mockOr,
        order: mockOrder,
        range: mockRange,
      })),
    });

    // Act
    await getProductServices(1, 10, { searchTerm: 'test' });

    // Assert
    expect(mockOr).toHaveBeenCalledWith('name.ilike.%test%,description.ilike.%test%');
  });

  it('should apply product type filter', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockEq = jest.fn().mockReturnThis();
    const mockOrder = jest.fn().mockReturnThis();
    const mockRange = jest.fn().mockResolvedValue({ data: [], error: null, count: 0 });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: mockEq,
        order: mockOrder,
        range: mockRange,
      })),
    });

    // Act
    await getProductServices(1, 10, { productType: 'service' });

    // Assert
    expect(mockEq).toHaveBeenCalledWith('product_type', 'service');
  });

  it('should apply sorting for price and name', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockOrder = jest.fn().mockReturnThis();
    const mockRange = jest.fn().mockResolvedValue({ data: [], error: null, count: 0 });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: mockOrder,
        range: mockRange,
      })),
    });

    // Act
    await getProductServices(1, 10, {}, 'price_asc');
    await getProductServices(1, 10, {}, 'name_desc');

    // Assert
    expect(mockOrder).toHaveBeenCalledWith('discounted_price', { ascending: true, nullsFirst: false });
    expect(mockOrder).toHaveBeenCalledWith('base_price', { ascending: true, nullsFirst: false });
    expect(mockOrder).toHaveBeenCalledWith('name', { ascending: false });
  });
});