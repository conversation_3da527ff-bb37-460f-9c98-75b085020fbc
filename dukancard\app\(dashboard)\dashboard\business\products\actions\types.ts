// Export types used across multiple files
export interface ProductFilters {
  searchTerm?: string;
  hasVariants?: boolean;
  productType?: 'physical' | 'service';
  priceRange?: {
    min?: number;
    max?: number;
  };
}

export type ProductSortBy =
  | "created_desc"
  | "created_asc"
  | "name_asc"
  | "name_desc"
  | "price_asc"
  | "price_desc"
  | "available_first"
  | "unavailable_first"
  | "variant_count_asc"
  | "variant_count_desc";

// Re-export the ProductServiceData type from schemas.ts
export type { ProductServiceData } from './schemas';