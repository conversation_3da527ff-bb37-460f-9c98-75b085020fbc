"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { PricingPlan } from "@/lib/PricingPlans";
import PricingCard from "@/app/components/PricingCard"; // Using the shared PricingCard component
import { PricingCardProvider } from "@/app/components/PricingCardContext";
import { Sparkles } from "lucide-react";
import EnhancedPricingToggle from "./EnhancedPricingToggle";

type AnimationVariant = {
  hidden: { opacity: number; y: number };
  visible: {
    opacity: number;
    y: number;
    transition: { duration: number; ease: string };
  } | ((_i: number) => {
    opacity: number;
    y: number;
    transition: { duration: number; delay: number; ease: string };
  });
};

interface EnhancedPricingCardsProps {
  plans: PricingPlan[];
  onButtonClick: (_plan: PricingPlan) => void;
  itemFadeIn: AnimationVariant;
  billingCycle: "monthly" | "yearly";
  setBillingCycle: (_cycle: "monthly" | "yearly") => void;
}

export default function EnhancedPricingCards({
  plans,
  onButtonClick,
  itemFadeIn,
  billingCycle,
  setBillingCycle,
}: EnhancedPricingCardsProps) {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });

  return (
    <PricingCardProvider>
      <div ref={sectionRef} className="px-1 md:px-2 max-w-full mx-auto mb-24">
      {/* Free Plan Notice with enhanced animation */}
      <motion.div
        variants={itemFadeIn}
        custom={1.5}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="relative text-center mb-12"
      >
        <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] font-medium text-lg">
          <Sparkles className="w-5 h-5" />
          <span>Get started for free or enjoy first month free on paid plans!</span>
          <Sparkles className="w-5 h-5" />
        </div>

        {/* Animated glow effect */}
        <motion.div
          className="absolute inset-0 rounded-full bg-[var(--brand-gold)]/5 blur-xl -z-10"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.5, 0.8, 0.5]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
      </motion.div>

      {/* Billing Toggle */}
      <div className="mb-12">
        <EnhancedPricingToggle
          billingCycle={billingCycle}
          setBillingCycle={setBillingCycle}
          itemFadeIn={itemFadeIn}
        />
      </div>

      {/* Pricing Cards Grid with responsive layout */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-1 md:gap-2 items-stretch">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.id}
            custom={index + 2} // Stagger delay
            variants={itemFadeIn}
            className="w-full relative" // Added relative for positioning
          >
            {/* Highlight glow for popular plan - matching homepage implementation */}
            {plan.mostPopular && (
              <motion.div
                className="absolute -inset-2 bg-[var(--brand-gold)]/20 dark:bg-[var(--brand-gold)]/30 rounded-xl blur-lg -z-10"
                initial={{ opacity: 0.5, scale: 0.95 }}
                animate={{
                  opacity: [0.5, 0.8, 0.5],
                  scale: [0.95, 1, 0.95]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "mirror"
                }}
              />
            )}

            {/* Enhanced card without hover effect */}
            <div>
              <PricingCard
                plan={plan}
                index={index}
                onButtonClick={() => onButtonClick(plan)}
              />
            </div>

{/* Removed duplicate floating badge as it's already in the PricingCard component */}
          </motion.div>
        ))}
      </div>

      {/* Additional information note */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="text-center text-sm text-muted-foreground mt-8"
      >
        All plans can be cancelled at any time. Your subscription will remain active until the end of your billing cycle.
      </motion.div>
    </div>
    </PricingCardProvider>
  );
}
