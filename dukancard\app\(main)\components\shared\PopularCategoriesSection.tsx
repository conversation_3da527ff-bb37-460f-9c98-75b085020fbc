"use client";

import React from "react";
import { motion } from "framer-motion";
import { getPopularCategories } from "@/lib/config/categories";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface PopularCategoriesSectionProps {
  className?: string;
  maxCategories?: number;
}

export default function PopularCategoriesSection({
  className = "",
  maxCategories = 15,
}: PopularCategoriesSectionProps) {
  // Get popular categories from the shared config
  const displayCategories = getPopularCategories(maxCategories);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      className={`w-full mt-0 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="flex justify-center mb-4 md:mb-5"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative">
          <h2 className="text-xl md:text-2xl font-bold text-neutral-800 dark:text-neutral-100 text-center px-4">
            <span className="relative z-10 inline-block">
              Popular Categories
              <motion.span
                className="absolute -bottom-1 left-0 right-0 h-[6px] bg-[var(--brand-gold)]/20 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 0.8, delay: 0.3 }}
              />
            </span>
          </h2>
        </div>
      </motion.div>

      <motion.div
        className={`grid ${
          className || "grid-cols-3 md:grid-cols-5"
        } gap-4 md:gap-5 lg:gap-6`}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {displayCategories.map((category, index) => (
          <motion.div
            key={category.name}
            className="flex flex-col items-center"
            variants={itemVariants}
            transition={{ duration: 0.3, delay: index * 0.05 }}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <Link
              href={`/discover?category=${encodeURIComponent(category.name)}`}
              className="w-full flex flex-col items-center"
            >
              <div className="relative group cursor-pointer">
                {/* Hover glow effect */}
                <motion.div
                  className="absolute -inset-1 rounded-xl bg-[var(--brand-gold)]/10 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={{ scale: 0.9 }}
                  whileHover={{ scale: 1.1 }}
                />

                {/* Icon container */}
                <div className="relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 lg:w-20 lg:h-20 bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-xl group-hover:border-[var(--brand-gold)] transition-all duration-300 shadow-sm group-hover:shadow-md">
                  <motion.div
                    initial={{ scale: 1 }}
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    {React.createElement(category.icon, {
                      className:
                        "w-7 h-7 md:w-8 md:h-8 lg:w-9 lg:h-9 text-[var(--brand-gold)]",
                    })}
                  </motion.div>
                </div>
              </div>

              <span className="mt-3 text-sm md:text-sm lg:text-sm text-center font-medium text-neutral-700 dark:text-neutral-300 truncate max-w-full">
                {category.name}
              </span>
            </Link>
          </motion.div>
        ))}
      </motion.div>

      {/* View All Categories Button */}
      <motion.div
        className="flex justify-center mt-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Link href="/discover">
          <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.98 }}>
            <Button className="bg-white dark:bg-neutral-900 hover:bg-neutral-100 dark:hover:bg-neutral-800 text-neutral-800 dark:text-neutral-200 border border-neutral-200 dark:border-neutral-800 px-5 py-2 rounded-full font-medium text-sm relative overflow-hidden group shadow-sm hover:shadow-md transition-all duration-300">
              <span className="flex items-center">
                View All Categories
                <ArrowRight className="ml-2 w-4 h-4 text-[var(--brand-gold)]" />
              </span>
            </Button>
          </motion.div>
        </Link>
      </motion.div>
    </motion.div>
  );
}
