import { createClient } from '@/utils/supabase/server';
import { createAdminClient } from '@/utils/supabase/admin';
import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath } from 'next/cache';

export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Get user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { reviewId, rating, reviewText } = body;

    // Validate required parameters
    if (!reviewId) {
      return NextResponse.json(
        { error: 'Review ID is required' },
        { status: 400 }
      );
    }

    if (!rating || rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // First, check if the review belongs to the user
    const { data: reviewData, error: reviewError } = await supabase
      .from('ratings_reviews')
      .select('business_profile_id')
      .eq('id', reviewId)
      .eq('user_id', user.id)
      .single();

    if (reviewError || !reviewData) {
      return NextResponse.json(
        { error: 'Review not found or you do not have permission to update it' },
        { status: 403 }
      );
    }

    // Update the review using admin client to bypass RLS
    const supabaseAdmin = createAdminClient();
    const { error: updateError } = await supabaseAdmin
      .from('ratings_reviews')
      .update({
        rating,
        review_text: reviewText || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', reviewId)
      .eq('user_id', user.id);

    if (updateError) {
      return NextResponse.json(
        { error: `Failed to update review: ${updateError.message}` },
        { status: 500 }
      );
    }

    // Revalidate paths
    revalidatePath('/dashboard/customer/reviews');
    revalidatePath('/dashboard/customer');

    // Get business slug for revalidation
    const { data: businessData } = await supabase
      .from('business_profiles')
      .select('business_slug')
      .eq('id', reviewData.business_profile_id)
      .single();

    if (businessData?.business_slug) {
      revalidatePath(`/${businessData.business_slug}`);
    }

    return NextResponse.json({
      success: true,
      message: 'Review updated successfully'
    });

  } catch (_error) {
    console.error('Error updating review:', _error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
