import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { PlanId, PlanCycle, PaymentMethodType } from "@/lib/types/subscription";
import { switchActiveSubscriptionWithNewPayment } from "@/lib/actions/subscription/manage/switch";

/**
 * Switch from an active subscription to a new plan when payment method is UPI or emandate
 *
 * This endpoint allows users to switch from an active subscription to a new plan
 * when their payment method is UPI or emandate, which requires creating a new subscription
 * instead of updating the existing one.
 *
 * Request body:
 * {
 *   "planId": "basic" | "growth",
 *   "planCycle": "monthly" | "yearly",
 *   "paymentMethod": "upi" | "emandate" | "enach" | "card"
 * }
 *
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "id": "sub_123456789",
 *     "short_url": "https://rzp.io/i/abcdefg",
 *     "requires_authorization": true,
 *     "message": "New subscription created. Please complete the payment authorization."
 *   }
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    console.log(`[API_DEBUG] switch-with-new-payment called for subscription: ${subscriptionId}`);

    // Parse request body
    const body = await request.json();
    const { planId, planCycle, paymentMethod } = body;

    console.log(`[API_DEBUG] Request body:`, { planId, planCycle, paymentMethod });

    // Validate required parameters
    if (!planId || !planCycle || !paymentMethod) {
      return NextResponse.json(
        { success: false, error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Call the switchActiveSubscriptionWithNewPayment function
    const result = await switchActiveSubscriptionWithNewPayment(
      subscriptionId,
      planId as PlanId, // Type assertion to match PlanId
      planCycle as PlanCycle, // Type assertion to match PlanCycle
      paymentMethod as PaymentMethodType // Type assertion to match PaymentMethodType
    );

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return the result
    return NextResponse.json(
      { success: true, data: result.data },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error switching subscription with new payment:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
