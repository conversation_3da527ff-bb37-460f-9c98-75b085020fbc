import {
  configureGoogleAuth,
  signInWithGoogleNative,
  signOutFromGoogle,
  isGoogleSignedIn,
  getCurrentGoogleUser,
  checkGooglePlayServices,
} from '@/backend/supabase/services/auth/nativeGoogleAuth2025';
import { getGoogleConfig } from '@/lib/config/google';
import { supabase } from '@/lib/supabase';
import { Platform } from 'react-native';

// Mock dependencies
jest.mock('@/lib/config/google');
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      signInWithIdToken: jest.fn(),
    },
  },
}));

// Mock the native module
const mockGoogleSignin = {
  configure: jest.fn(),
  hasPlayServices: jest.fn(),
  signOut: jest.fn(),
  signIn: jest.fn(),
  getCurrentUser: jest.fn(),
  signInSilently: jest.fn(),
};
const mockStatusCodes = {
  SIGN_IN_CANCELLED: 'SIGN_IN_CANCELLED',
  IN_PROGRESS: 'IN_PROGRESS',
  PLAY_SERVICES_NOT_AVAILABLE: 'PLAY_SERVICES_NOT_AVAILABLE',
};

jest.mock('@react-native-google-signin/google-signin', () => ({
  GoogleSignin: mockGoogleSignin,
  statusCodes: mockStatusCodes,
}));

describe('nativeGoogleAuth2025', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (getGoogleConfig as jest.Mock).mockResolvedValue({
      webClientId: 'test-web-client-id',
      iosClientId: 'test-ios-client-id',
      offlineAccess: true,
      forceCodeForRefreshToken: true,
    });
  });

  describe('configureGoogleAuth', () => {
    it('should configure Google Sign-In with correct client IDs', async () => {
      await configureGoogleAuth();
      expect(mockGoogleSignin.configure).toHaveBeenCalledWith({
        webClientId: 'test-web-client-id',
        iosClientId: Platform.OS === 'ios' ? 'test-ios-client-id' : undefined,
        offlineAccess: true,
        forceCodeForRefreshToken: true,
      });
    });
  });

  describe('signInWithGoogleNative', () => {
    it('should successfully sign in and return user data', async () => {
      mockGoogleSignin.hasPlayServices.mockResolvedValue(true);
      mockGoogleSignin.signIn.mockResolvedValue({ data: { idToken: 'test-id-token' } });
      (supabase.auth.signInWithIdToken as jest.Mock).mockResolvedValue({
        data: { user: { id: '123' } },
        error: null,
      });

      const response = await signInWithGoogleNative();

      expect(response.success).toBe(true);
      expect(response.user).toEqual({ id: '123' });
      expect(supabase.auth.signInWithIdToken).toHaveBeenCalledWith({
        provider: 'google',
        token: 'test-id-token',
      });
    });

    it('should handle sign-in cancellation', async () => {
        mockGoogleSignin.hasPlayServices.mockResolvedValue(true);
        mockGoogleSignin.signIn.mockRejectedValue({ code: mockStatusCodes.SIGN_IN_CANCELLED });
  
        const response = await signInWithGoogleNative();
  
        expect(response.success).toBe(false);
        expect(response.message).toBe('cancelled');
      });
  
      it('should handle missing Play Services', async () => {
        Platform.OS = 'android';
        mockGoogleSignin.hasPlayServices.mockResolvedValue(false);
  
        const response = await signInWithGoogleNative();
  
        expect(response.success).toBe(false);
        expect(response.message).toBe('Google services are not available on this device. Please try email login.');
      });
  });

  describe('signOutFromGoogle', () => {
    it('should call GoogleSignin.signOut', async () => {
      await signOutFromGoogle();
      expect(mockGoogleSignin.signOut).toHaveBeenCalledTimes(1);
    });
  });

  describe('isGoogleSignedIn', () => {
    it('should return true if a user is signed in', async () => {
      mockGoogleSignin.getCurrentUser.mockReturnValue({});
      const result = await isGoogleSignedIn();
      expect(result).toBe(true);
    });

    it('should return false if no user is signed in', async () => {
      mockGoogleSignin.getCurrentUser.mockReturnValue(null);
      const result = await isGoogleSignedIn();
      expect(result).toBe(false);
    });
  });

  describe('getCurrentGoogleUser', () => {
    it('should return user info from signInSilently', async () => {
      const mockUser = { email: '<EMAIL>' };
      mockGoogleSignin.signInSilently.mockResolvedValue(mockUser);
      const result = await getCurrentGoogleUser();
      expect(result).toEqual(mockUser);
    });
  });

  describe('checkGooglePlayServices', () => {
    it('should return true if play services are available', async () => {
      mockGoogleSignin.hasPlayServices.mockResolvedValue(true);
      const result = await checkGooglePlayServices();
      expect(result).toBe(true);
    });

    it('should return false if play services are not available', async () => {
      mockGoogleSignin.hasPlayServices.mockRejectedValue(new Error('No play services'));
      const result = await checkGooglePlayServices();
      expect(result).toBe(false);
    });
  });
});