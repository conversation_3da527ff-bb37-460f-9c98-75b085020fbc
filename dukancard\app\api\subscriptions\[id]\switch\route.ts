import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { switchAuthenticatedSubscription } from "@/lib/actions/subscription";
import { PlanId, PlanCycle } from "@/lib/types/subscription";

/**
 * Switch from an authenticated subscription to a new plan
 *
 * This endpoint allows users to switch from an authenticated subscription to a new plan.
 * It cancels the current authenticated subscription and creates a new one.
 *
 * Request body:
 * {
 *   "planId": "basic" | "growth",
 *   "planCycle": "monthly" | "yearly"
 * }
 *
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "subscription_id": "sub_123456789",
 *     "short_url": "https://rzp.io/i/abcdefg",
 *     "requires_authorization": true,
 *     "message": "New subscription created. Please complete the payment authorization."
 *   }
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    // Parse request body
    const body = await request.json();
    const { planId, planCycle } = body;

    if (!planId || !planCycle) {
      return NextResponse.json(
        { success: false, error: "Missing required parameters: planId and planCycle" },
        { status: 400 }
      );
    }

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get admin client to bypass RLS
    const adminClient = createAdminClient();

    // Verify that the subscription belongs to the user
    const { data: subscription, error: subscriptionError } = await adminClient
      .from("payment_subscriptions")
      .select("razorpay_subscription_id, subscription_status, business_profile_id, plan_id, plan_cycle")
      .eq("razorpay_subscription_id", subscriptionId)
      .eq("business_profile_id", user.id)
      .maybeSingle();

    if (subscriptionError) {
      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription details" },
        { status: 500 }
      );
    }

    if (!subscription) {
      return NextResponse.json(
        { success: false, error: "Subscription not found or does not belong to user" },
        { status: 404 }
      );
    }

    // Verify that the subscription is in authenticated state
    if (subscription.subscription_status !== "authenticated") {
      return NextResponse.json(
        { success: false, error: "Subscription is not in authenticated state" },
        { status: 400 }
      );
    }

    // Check if the user is trying to subscribe to the same plan they already have
    if (subscription.plan_id === planId && subscription.plan_cycle === planCycle) {
      return NextResponse.json(
        { success: false, error: "You are already subscribed to this plan. Please choose a different plan or cycle." },
        { status: 400 }
      );
    }

    // Call the switchAuthenticatedSubscription function
    const result = await switchAuthenticatedSubscription(
      subscriptionId,
      planId as PlanId, // Type assertion to match PlanId
      planCycle as PlanCycle // Type assertion to match PlanCycle
    );

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return the result
    return NextResponse.json(
      { success: true, data: result.data },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error switching subscription:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
