import { motion } from "framer-motion";
import { Camera } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface GalleryHeaderProps {
  canAddMore: boolean;
  isUploading: boolean;
  onUploadClick: () => void;
}

export default function GalleryHeader({
  canAddMore,
  isUploading,
  onUploadClick,
}: GalleryHeaderProps) {
  return (
    <motion.div
      className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6"
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.1 }}
    >
      <div className="p-3 rounded-xl bg-muted hidden sm:block">
        <Camera className="w-6 h-6 text-foreground" />
      </div>
      <div className="flex-1">
        <h1 className="text-2xl font-bold text-foreground">
          Gallery Management
        </h1>
        <p className="text-muted-foreground mt-1">
          Showcase your business with beautiful photos
        </p>
      </div>
      <Button
        onClick={onUploadClick}
        disabled={!canAddMore || isUploading}
        variant="outline"
        size="sm"
        className="flex items-center gap-2"
      >
        <Camera className="h-4 w-4" />
        Add Photo
      </Button>
    </motion.div>
  );
}
