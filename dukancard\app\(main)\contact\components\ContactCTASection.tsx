"use client";

import { useEffect, useState } from "react";
import { motion, Variants } from "framer-motion";
import { ArrowRight, Phone } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { siteConfig } from "@/lib/site-config";

interface ContactCTASectionProps {
  sectionFadeIn: Variants;
  itemFadeIn: (_delay?: number) => Variants;
}

export default function ContactCTASection({
  sectionFadeIn,
  itemFadeIn,
}: ContactCTASectionProps) {
  const router = useRouter();
  const [_isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle navigation
  const handleGetStarted = () => {
    router.push("/login");
  };

  const handleViewPricing = () => {
    router.push("/pricing");
  };



  // Button animation
  const buttonAnimation = {
    rest: { scale: 1 },
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 }
    },
    tap: {
      scale: 0.98,
      transition: { duration: 0.1 }
    }
  };

  return (
    <motion.section
      id="contact-cta-section"
      variants={sectionFadeIn}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      className="py-16 md:py-24 px-4 relative overflow-hidden"
    >
      {/* Background gradient effects */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/2 left-1/4 -translate-y-1/2 w-1/2 h-1/2 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-1/3 h-1/3 bg-blue-500/10 dark:bg-blue-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto max-w-5xl">
        <div className="bg-white/80 dark:bg-black/50 backdrop-blur-sm border border-neutral-200 dark:border-neutral-800 rounded-2xl p-8 md:p-12">
          <motion.div variants={itemFadeIn(0)} className="text-center mb-8 md:mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Ready to <span className="text-[var(--brand-gold)]">Get Started</span>?
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Create your digital business card today and start connecting with customers in a whole new way.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
            {/* Primary CTA Button */}
            <motion.div
              variants={itemFadeIn(1)}
              className="md:col-span-1"
            >
              <motion.div
                variants={buttonAnimation}
                whileHover="hover"
                whileTap="tap"
              >
                <Button
                  onClick={handleGetStarted}
                  size="lg"
                  className="w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium py-2 h-auto shadow-md hover:shadow-lg transition-all duration-300"
                >
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </motion.div>
            </motion.div>

            {/* Secondary CTA Buttons */}
            <motion.div
              variants={itemFadeIn(2)}
              className="md:col-span-1"
            >
              <motion.div
                variants={buttonAnimation}
                whileHover="hover"
                whileTap="tap"
              >
                <Button
                  onClick={handleViewPricing}
                  variant="outline"
                  size="lg"
                  className="w-full border-neutral-300 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group py-2 h-auto"
                >
                  <ArrowRight className="mr-2 h-4 w-4 text-[var(--brand-gold)] group-hover:translate-x-1 transition-transform" />
                  View Pricing
                </Button>
              </motion.div>
            </motion.div>



            <motion.div
              variants={itemFadeIn(4)}
              className="md:col-span-1"
            >
              <motion.div
                variants={buttonAnimation}
                whileHover="hover"
                whileTap="tap"
              >
                <Button
                  onClick={() => window.location.href = `tel:${siteConfig.contact.phone.replace(/\s+/g, '')}`}
                  variant="outline"
                  size="lg"
                  className="w-full border-neutral-300 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group py-2 h-auto"
                >
                  <Phone className="mr-2 h-4 w-4 text-[var(--brand-gold)] group-hover:rotate-12 transition-transform" />
                  Call Us
                </Button>
              </motion.div>
            </motion.div>
          </div>

          {/* Additional contact info */}
          <motion.div
            variants={itemFadeIn(5)}
            className="mt-8 pt-6 border-t border-neutral-200 dark:border-neutral-800 text-center"
          >
            <p className="text-muted-foreground">
              Or reach out via email at{" "}
              <a
                href={`mailto:${siteConfig.contact.email}`}
                className="text-[var(--brand-gold)] hover:underline"
              >
                {siteConfig.contact.email}
              </a>
            </p>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
