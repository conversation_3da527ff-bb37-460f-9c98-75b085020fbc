"use client";

import { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  FileText,
  User,
  Key,
  FileCode,
  AlertTriangle,
  CreditCard,
  Scale,
  Globe,
  Trash2,
  RefreshCw,
  Zap,
  Mail
} from "lucide-react";
import { Card } from "@/components/ui/card";
import PolicyHeroSection from "../components/policy/PolicyHeroSection";
import PolicySection from "../components/policy/PolicySection";
import PolicyNavigation from "../components/policy/PolicyNavigation";
import PolicyCTASection from "../components/policy/PolicyCTASection";
import SectionDivider from "../components/landing/SectionDivider";
import { siteConfig } from "@/lib/site-config";

// Navigation items for the policy
const navItems = [
  { id: "introduction", title: "Introduction" },
  { id: "definitions", title: "Definitions" },
  { id: "account", title: "Account Registration" },
  { id: "content", title: "User Content" },
  { id: "prohibited", title: "Prohibited Activities" },
  { id: "payment", title: "Payment Terms" },
  { id: "intellectual", title: "Intellectual Property" },
  { id: "disclaimer", title: "Disclaimer of Warranties" },
  { id: "limitation", title: "Limitation of Liability" },
  { id: "termination", title: "Termination" },
  { id: "changes", title: "Changes to Terms" },
  { id: "contact", title: "Contact Us" },
];

// Related links
const relatedLinks = [
  { title: "Privacy Policy", href: "/privacy" },
  { title: "Cookie Policy", href: "/cookies" },
  { title: "Refund Policy", href: "/refund" },
];

export default function ModernTermsOfServiceClient() {
  const pageRef = useRef<HTMLDivElement>(null);

  // Removed scroll-based fade effect

  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div
      ref={pageRef}
      className="min-h-screen bg-white dark:bg-black"
    >
      <div className="relative">
        {/* Hero Section */}
        <PolicyHeroSection
          title="Terms of Service"
          lastUpdated="May 19, 2025"
          variant="blue"
        />

        <div className="container mx-auto px-4 max-w-4xl pb-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left column: Navigation (sticky on desktop) */}
            <div className="w-full lg:w-1/4 order-1">
              <div className="lg:sticky lg:top-24 self-start">
                <PolicyNavigation items={navItems} />
              </div>
            </div>

            {/* Right column: Content */}
            <div className="w-full lg:w-3/4 order-2">
              <Card className="p-6 md:p-8 border border-border shadow-sm mb-8">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="prose prose-neutral dark:prose-invert max-w-none"
                >
                  <p className="text-lg">
                    Welcome to Dukancard. These Terms of Service govern your access to and use of our website, mobile applications, and services. By using our services, you agree to be bound by these terms.
                  </p>
                </motion.div>
              </Card>

              {/* Introduction Section */}
              <PolicySection
                id="introduction"
                title="1. Introduction"
                icon={<FileText className="h-6 w-6" />}
                delay={0}
              >
                <p>
                  Welcome to Dukancard. These Terms of Service (&quot;Terms&quot;) govern your access to and use of the Dukancard website, mobile applications, and services (collectively, the &quot;Service&quot;). By accessing or using the Service, you agree to be bound by these Terms. If you do not agree to these Terms, you may not access or use the Service.
                </p>
              </PolicySection>

              {/* Definitions Section */}
              <PolicySection
                id="definitions"
                title="2. Definitions"
                icon={<FileCode className="h-6 w-6" />}
                delay={1}
              >
                <p>
                  <strong>&quot;Dukancard&quot;</strong> refers to the digital business card platform operated by Dukancard.
                </p>
                <p>
                  <strong>&quot;User&quot;</strong> refers to any individual or entity that accesses or uses the Service.
                </p>
                <p>
                  <strong>&quot;Content&quot;</strong> refers to any information, text, graphics, photos, or other materials uploaded, downloaded, or appearing on the Service.
                </p>
              </PolicySection>

              {/* Account Registration Section */}
              <PolicySection
                id="account"
                title="3. Account Registration"
                icon={<User className="h-6 w-6" />}
                delay={2}
              >
                <p>
                  To use certain features of the Service, you may be required to register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.
                </p>
                <p>
                  You are responsible for safeguarding your password and for all activities that occur under your account. You agree to notify Dukancard immediately of any unauthorized use of your account.
                </p>
              </PolicySection>

              {/* User Content Section */}
              <PolicySection
                id="content"
                title="4. User Content"
                icon={<Key className="h-6 w-6" />}
                delay={3}
              >
                <p>
                  Our Service allows you to post, link, store, share and otherwise make available certain information, text, graphics, videos, or other material. You are responsible for the Content that you post on or through the Service, including its legality, reliability, and appropriateness.
                </p>
                <p>
                  By posting Content on or through the Service, you represent and warrant that: (i) the Content is yours (you own it) and/or you have the right to use it and the right to grant us the rights and license as provided in these Terms, and (ii) that the posting of your Content on or through the Service does not violate the privacy rights, publicity rights, copyrights, contract rights or any other rights of any person or entity.
                </p>
              </PolicySection>

              {/* Section Divider */}
              <SectionDivider variant="blue" className="my-12" />

              {/* Prohibited Activities Section */}
              <PolicySection
                id="prohibited"
                title="5. Prohibited Activities"
                icon={<AlertTriangle className="h-6 w-6" />}
                delay={4}
              >
                <p>
                  You agree not to engage in any of the following prohibited activities:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>
                    Using the Service for any illegal purpose or in violation of any local, state, national, or international law.
                  </li>
                  <li>
                    Harassing, abusing, or harming another person, or engaging in any other conduct that restricts or inhibits anyone&apos;s use or enjoyment of the Service.
                  </li>
                  <li>
                    Impersonating any person or entity, or falsely stating or otherwise misrepresenting your affiliation with a person or entity.
                  </li>
                  <li>
                    Interfering with or disrupting the Service or servers or networks connected to the Service.
                  </li>
                  <li>
                    Attempting to gain unauthorized access to the Service, other accounts, computer systems, or networks connected to the Service.
                  </li>
                </ul>
              </PolicySection>

              {/* Payment Terms Section */}
              <PolicySection
                id="payment"
                title="6. Payment Terms"
                icon={<CreditCard className="h-6 w-6" />}
                delay={5}
              >
                <p>
                  Certain aspects of the Service may be provided for a fee. You will be required to select a payment plan and provide accurate information regarding your payment method. You agree to pay Dukancard the amount that is specified in the payment plan in accordance with the terms of such plan.
                </p>
                <p>
                  All payments are processed securely through Razorpay, our payment gateway partner. Payments will be charged on the day you sign up for a payment plan and will cover the use of that service for the period as indicated. Payment plans will automatically renew unless cancelled in accordance with these Terms.
                </p>
                <p>
                  By subscribing to our paid plans, you authorize Razorpay to charge your payment method for the subscription fees on a recurring basis until you cancel your subscription.
                </p>
                <p>
                  For more information about our refund policy, please see our{" "}
                  <Link href="/refund" className="text-blue-500 hover:underline">
                    Refund Policy
                  </Link>
                  .
                </p>
              </PolicySection>

              {/* Intellectual Property Section */}
              <PolicySection
                id="intellectual"
                title="7. Intellectual Property"
                icon={<Scale className="h-6 w-6" />}
                delay={6}
              >
                <p>
                  The Service and its original content (excluding Content provided by users), features, and functionality are and will remain the exclusive property of Dukancard and its licensors. The Service is protected by copyright, trademark, and other laws of both India and foreign countries.
                </p>
                <p>
                  Our trademarks and trade dress may not be used in connection with any product or service without the prior written consent of Dukancard.
                </p>
              </PolicySection>

              {/* Disclaimer of Warranties Section */}
              <PolicySection
                id="disclaimer"
                title="8. Disclaimer of Warranties"
                icon={<Globe className="h-6 w-6" />}
                delay={7}
              >
                <p>
                  Your use of the Service is at your sole risk. The Service is provided on an &quot;AS IS&quot; and &quot;AS AVAILABLE&quot; basis. The Service is provided without warranties of any kind, whether express or implied, including, but not limited to, implied warranties of merchantability, fitness for a particular purpose, non-infringement, or course of performance.
                </p>
              </PolicySection>

              {/* Limitation of Liability Section */}
              <PolicySection
                id="limitation"
                title="9. Limitation of Liability"
                icon={<Trash2 className="h-6 w-6" />}
                delay={8}
              >
                <p>
                  In no event shall Dukancard, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from (i) your access to or use of or inability to access or use the Service; (ii) any conduct or content of any third party on the Service; (iii) any content obtained from the Service; and (iv) unauthorized access, use, or alteration of your transmissions or content, whether based on warranty, contract, tort (including negligence), or any other legal theory, whether or not we have been informed of the possibility of such damage.
                </p>
              </PolicySection>

              {/* Termination Section */}
              <PolicySection
                id="termination"
                title="10. Termination"
                icon={<RefreshCw className="h-6 w-6" />}
                delay={9}
              >
                <p>
                  We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.
                </p>
                <p>
                  If you wish to terminate your account, you may simply discontinue using the Service or contact us to request account deletion.
                </p>
              </PolicySection>

              {/* Changes to Terms Section */}
              <PolicySection
                id="changes"
                title="11. Changes to Terms"
                icon={<Zap className="h-6 w-6" />}
                delay={10}
              >
                <p>
                  Dukancard reserves the right, at its sole discretion, to modify or replace these Terms at any time. If a revision is material, Dukancard will provide at least 30 days&apos; notice prior to any new terms taking effect. What constitutes a material change will be determined at Dukancard&apos;s sole discretion.
                </p>
                <p>
                  By continuing to access or use the Service after those revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, you must stop using the Service.
                </p>
              </PolicySection>

              {/* Contact Us Section */}
              <PolicySection
                id="contact"
                title="12. Contact Us"
                icon={<Mail className="h-6 w-6" />}
                delay={11}
              >
                <p>
                  If you have any questions about these Terms, please contact us:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>By email: {siteConfig.contact.email}</li>
                  <li>By phone: {siteConfig.contact.phone}</li>
                  <li>By mail: {siteConfig.contact.address.full}</li>
                </ul>
              </PolicySection>
            </div>
          </div>
        </div>

        {/* Section Divider */}
        <SectionDivider variant="gold" className="my-8" />

        {/* CTA Section */}
        <PolicyCTASection relatedLinks={relatedLinks} />
      </div>
    </div>
  );
}
