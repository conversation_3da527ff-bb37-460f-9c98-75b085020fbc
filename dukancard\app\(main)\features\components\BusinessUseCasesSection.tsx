"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Store, Users, Globe, Briefcase, Scissors, Coffee } from "lucide-react";
import { Card } from "@/components/ui/card";

interface UseCaseCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  index: number;
}

function UseCaseCard({ icon, title, description, index }: UseCaseCardProps) {
  const cardRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(cardRef, { once: true, amount: 0.2 });

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="h-full"
    >
      <Card className="h-full bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 overflow-hidden group">
        {/* Icon header */}
        <div className="p-6 border-b border-neutral-200 dark:border-neutral-800 flex items-center">
          <div className="p-3 bg-neutral-100 dark:bg-neutral-800 rounded-full mr-4">
            {icon}
          </div>
          <h3 className="text-xl font-semibold">{title}</h3>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            {description}
          </p>
        </div>
      </Card>
    </motion.div>
  );
}

export default function BusinessUseCasesSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });

  // Use cases data
  const useCases = [
    {
      icon: <Store className="w-5 h-5 text-[var(--brand-gold)]" />,
      title: "Retail Shops",
      description: "Display your products, operating hours, and location. Allow customers to browse before visiting your physical store.",
    },
    {
      icon: <Users className="w-5 h-5 text-blue-500" />,
      title: "Service Providers",
      description: "Showcase your services, pricing, and availability. Make it easy for clients to contact you and book appointments.",
    },
    {
      icon: <Globe className="w-5 h-5 text-purple-500" />,
      title: "Freelancers",
      description: "Create a professional portfolio that highlights your skills, experience, and previous work to attract new clients.",
    },
    {
      icon: <Briefcase className="w-5 h-5 text-green-500" />,
      title: "Consultants",
      description: "Share your expertise, testimonials, and case studies. Build credibility and generate leads for your consulting business.",
    },
    {
      icon: <Scissors className="w-5 h-5 text-pink-500" />,
      title: "Salons & Spas",
      description: "Showcase your services, team members, and before/after photos. Allow clients to see your work before booking.",
    },
    {
      icon: <Coffee className="w-5 h-5 text-amber-500" />,
      title: "Cafés & Restaurants",
      description: "Display your menu, location, and hours. Make it easy for customers to find you and see what you offer.",
    },
  ];

  return (
    <section
      ref={sectionRef}
      className="py-16 px-4 md:px-6 lg:px-8 relative overflow-hidden"
    >
      <div className="max-w-7xl mx-auto">
        {/* Section Title */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.7 }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            Perfect for{" "}
            <span className="text-[var(--brand-gold)] relative">
              Every Business
              <motion.div
                className="absolute -bottom-1 left-0 h-1 bg-[var(--brand-gold)]/30 rounded-full"
                initial={{ width: 0 }}
                animate={isInView ? { width: "100%" } : {}}
                transition={{ duration: 0.7, delay: 0.3 }}
              />
            </span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
            See how different businesses leverage Dukancard to enhance their
            digital presence.
          </p>
        </motion.div>

        {/* Use Cases Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {useCases.map((useCase, index) => (
            <UseCaseCard
              key={index}
              icon={useCase.icon}
              title={useCase.title}
              description={useCase.description}
              index={index}
            />
          ))}
        </div>


      </div>
    </section>
  );
}
