import EnhancedPricingPageClient from "./EnhancedPricingPageClient"; // Import the enhanced client component
import { Metadata } from "next";

export async function generateMetadata(): Promise<Metadata> {
  const title = "Dukancard Pricing Plans"; // More specific title
  const description =
    "Explore Dukancard's affordable pricing plans. Find the perfect digital business card solution for your needs, from free trials to enterprise options."; // Refined description
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/pricing`;
  const ogImage = `${siteUrl}/opengraph-image.png`; // Default OG image

  return {
    title, // Uses template: "Dukancard Pricing Plans - Dukancard"
    description,
    keywords: [ // Added keywords
      "Dukancard pricing",
      "digital business card cost",
      "affordable online presence",
      "Dukancard plans",
      "business card subscription India",
      "free digital business card",
    ],
    alternates: {
      canonical: "/pricing", // Relative canonical path
    },
    openGraph: {
      title: title,
      description: description,
      url: pageUrl,
      siteName: "Dukancard",
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "Dukancard Pricing Plans",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: title,
      description: description,
      images: [ogImage],
    },
    // Add WebPage Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: "Dukancard",
          url: siteUrl,
        },
      }),
    },
  };
}

// This is now a Server Component
export default function PricingPage() {
  // Render the enhanced client component which contains the interactive parts
  return <EnhancedPricingPageClient />;
}
