"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertCircle, Mail, Phone, MapPin, CheckCircle } from "lucide-react";
import { motion } from "framer-motion";

interface ProfileRequirementDialogProps {
  hasCompleteAddress?: boolean;
}

export default function ProfileRequirementDialog({
  hasCompleteAddress = false,
}: ProfileRequirementDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [missingFields, setMissingFields] = useState<string[]>([]);
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    // Check URL parameters for missing fields
    const missingParam = searchParams.get("missing");
    const messageParam = searchParams.get("message");

    if (missingParam || messageParam) {
      const fields = missingParam ? missingParam.split(",") : [];

      // Also check current state to determine what's actually missing
      const actuallyMissing: string[] = [];
      if (!hasCompleteAddress) actuallyMissing.push("address");

      // Use the more comprehensive list
      const finalMissing = actuallyMissing.length > 0 ? actuallyMissing : fields;

      setMissingFields(finalMissing);
      setIsOpen(true);

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      router.replace(newUrl, { scroll: false });
    }
  }, [searchParams, hasCompleteAddress, router]);

  const getFieldInfo = (field: string) => {
    switch (field) {
      case "email":
        return {
          icon: <Mail className="w-5 h-5" />,
          label: "Email Address",
          description: "Required for account notifications and password reset",
          color: "text-[#C29D5B]",
          bgColor: "bg-[#C29D5B]/10",
          borderColor: "border-[#C29D5B]/20",
        };
      case "phone":
        return {
          icon: <Phone className="w-5 h-5" />,
          label: "Mobile Number",
          description: "Required for account access and verification",
          color: "text-[#C29D5B]",
          bgColor: "bg-[#C29D5B]/10",
          borderColor: "border-[#C29D5B]/20",
        };
      case "address":
        return {
          icon: <MapPin className="w-5 h-5" />,
          label: "Address Information",
          description: "Required for location-based services",
          color: "text-[#C29D5B]",
          bgColor: "bg-[#C29D5B]/10",
          borderColor: "border-[#C29D5B]/20",
        };
      default:
        return {
          icon: <AlertCircle className="w-5 h-5" />,
          label: field,
          description: "Required information",
          color: "text-[#C29D5B]",
          bgColor: "bg-[#C29D5B]/10",
          borderColor: "border-[#C29D5B]/20",
        };
    }
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  if (missingFields.length === 0) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-lg max-w-[calc(100vw-2rem)] mx-auto p-0 gap-0 overflow-hidden border-0 shadow-2xl">
        {/* Header Section */}
        <div className="relative bg-gradient-to-br from-[#C29D5B] to-[#B08A4A] px-6 py-8 text-white">
          <div className="absolute inset-0 bg-black/5"></div>
          <div className="relative">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-xl bg-white/20 backdrop-blur-sm">
                  <AlertCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <DialogTitle className="text-xl font-bold text-white mb-1">
                    Complete Your Profile
                  </DialogTitle>
                  <DialogDescription className="text-white/80 text-sm">
                    Just a few more details to get started
                  </DialogDescription>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="px-6 py-6">
          <div className="mb-6">
            <p className="text-sm text-muted-foreground leading-relaxed">
              Please add the following required information to unlock all dashboard features and ensure the best experience.
            </p>
          </div>

          <div className="space-y-4 mb-8">
            {missingFields.map((field, index) => {
              const fieldInfo = getFieldInfo(field);
              return (
                <motion.div
                  key={field}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className={`group relative flex items-start gap-4 p-4 rounded-xl border-2 ${fieldInfo.borderColor} ${fieldInfo.bgColor} hover:shadow-md transition-all duration-200`}
                >
                  <div className={`flex-shrink-0 p-3 rounded-lg ${fieldInfo.color} bg-white shadow-sm`}>
                    {fieldInfo.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-base font-semibold text-foreground mb-1">
                      {fieldInfo.label}
                    </h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {fieldInfo.description}
                    </p>
                  </div>
                  <div className="flex-shrink-0 w-6 h-6 rounded-full border-2 border-[#C29D5B]/30 bg-white group-hover:border-[#C29D5B] transition-colors duration-200"></div>
                </motion.div>
              );
            })}
          </div>

          {/* Action Section */}
          <div className="space-y-4">
            <Button
              onClick={handleClose}
              className="w-full h-12 bg-gradient-to-r from-[#C29D5B] to-[#B08A4A] hover:from-[#B08A4A] hover:to-[#9A7A3A] text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 border-0"
            >
              <CheckCircle className="w-5 h-5 mr-2" />
              Got it, let me complete my profile
            </Button>
            <p className="text-xs text-center text-muted-foreground px-4 leading-relaxed">
              You can update these details using the forms below. All information is securely stored and protected.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
