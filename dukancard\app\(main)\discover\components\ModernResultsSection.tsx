"use client";

import { useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import ViewToggle from "./ViewToggle";
import ModernBusinessResults from "./ModernBusinessResults";
import ProductResults from "./ProductResults";
import LocationIndicator from "./LocationIndicator";
import { useDiscoverContext } from "../context/DiscoverContext";
import {
  BUSINESS_NAME_PARAM,
  PRODUCT_NAME_PARAM,
  PINCODE_PARAM,
  LOCALITY_PARAM,
  CITY_PARAM,
} from "../constants/urlParamConstants";

export default function ModernResultsSection() {
  const {
    viewType,
    sortBy,
    isSearching,
    isLoadingMore,
    businesses,
    products,
    hasMore,
    totalCount,
    isAuthenticated,
    productFilterBy,
    productSortBy,
    handleViewChange,
    handleBusinessSortChange,
    handleBusinessSearch,
    handleProductSearch,
    handleProductSortChange,
    handleProductFilterChange,
    loadMore,
  } = useDiscoverContext();

  const searchParams = useSearchParams();

  return (
    <motion.div
      className="w-full pb-16"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* View Toggle - centered and compact */}
      <div className="container mx-auto px-4 mb-6">
        <div className="flex flex-col items-center">
          <motion.h2
            className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 text-center"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            Browse Products/Services & Digital Cards
          </motion.h2>

          <ViewToggle
            viewType={viewType}
            onViewChange={handleViewChange}
            hasFilters={
              !!(
                searchParams.get(BUSINESS_NAME_PARAM) ||
                searchParams.get(PINCODE_PARAM) ||
                searchParams.get(LOCALITY_PARAM) ||
                searchParams.get(CITY_PARAM)
              )
            }
          />
        </div>
      </div>

      {/* Location Indicator */}
      <LocationIndicator />

      {/* Results Content */}
      <div className="w-full">
        {viewType === "cards" && (
          <ModernBusinessResults
            businesses={businesses}
            isAuthenticated={isAuthenticated}
            totalCount={totalCount}
            hasMore={hasMore}
            isLoadingMore={isLoadingMore}
            onLoadMore={loadMore}
            onSortChange={handleBusinessSortChange}
            onSearch={handleBusinessSearch}
            currentSortBy={sortBy}
            isLoading={isSearching}
            initialSearchTerm={searchParams.get(BUSINESS_NAME_PARAM)}
          />
        )}

        {viewType === "products" && (
          <ProductResults
            products={products}
            totalCount={totalCount}
            hasMore={hasMore}
            isLoadingMore={isLoadingMore}
            onLoadMore={loadMore}
            onSortChange={handleProductSortChange}
            onFilterChange={handleProductFilterChange}
            onSearch={handleProductSearch}
            currentSortBy={productSortBy}
            currentFilterBy={productFilterBy}
            isLoading={isSearching}
            initialSearchTerm={searchParams.get(PRODUCT_NAME_PARAM)}
          />
        )}
      </div>
    </motion.div>
  );
}
