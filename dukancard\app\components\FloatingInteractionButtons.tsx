"use client";

import { useState, useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { toast } from "sonner";
import {
  Heart,
  UserPlus,
  UserMinus,
  Loader2,
  Star,
  Share2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

// Glow effect component using simple styling
const GlowEffect = ({ color }: { color: string }) => {
  return (
    <div
      className="absolute inset-0 rounded-full blur-md pointer-events-none opacity-50"
      style={{ backgroundColor: color }}
    />
  );
};

// Pulse effect component using simple styling
const PulseEffect = ({ color }: { color: string }) => {
  return (
    <div
      className="absolute inset-0 rounded-full pointer-events-none"
      style={{
        boxShadow: `0 0 0 2px ${color}`,
      }}
    />
  );
};

interface FloatingInteractionButtonsProps {
  hasLiked: boolean;
  isSubscribed: boolean;
  isLoadingInteraction: boolean;
  onLike: () => void;
  onUnlike: () => void;
  onSubscribe: () => void;
  onUnsubscribe: () => void;
  isAuthenticated: boolean;
  isOwnBusiness?: boolean;
  isCurrentUserBusiness?: boolean;
  themeColor?: string;
  onReviewClick?: () => void;
  businessSlug?: string;
  businessName?: string;
}

export default function FloatingInteractionButtons({
  hasLiked,
  isSubscribed,
  isLoadingInteraction,
  onLike,
  onUnlike,
  onSubscribe,
  onUnsubscribe,
  isAuthenticated,
  isOwnBusiness = false,
  onReviewClick,
  businessSlug = "",
  businessName = "",
}: FloatingInteractionButtonsProps) {
  const [prevLiked, setPrevLiked] = useState(hasLiked);
  const [prevSubscribed, setPrevSubscribed] = useState(isSubscribed);
  const [isClient, setIsClient] = useState(false);

  const { ref } = useInView({
    triggerOnce: true,
    threshold: 0.5,
  });

  // Detect client-side rendering
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Detect changes in liked/subscribed state
  useEffect(() => {
    if (hasLiked !== prevLiked) {
      setPrevLiked(hasLiked);
    }
  }, [hasLiked, prevLiked]);

  useEffect(() => {
    if (isSubscribed !== prevSubscribed) {
      setPrevSubscribed(isSubscribed);
    }
  }, [isSubscribed, prevSubscribed]);

  const handleLikeClick = () => {
    if (isOwnBusiness) {
      toast.error("You cannot like your own business card", {
        description: "Business owners cannot like their own cards",
        position: "top-center",
      });
      return;
    }

    if (!isAuthenticated) {
      // Get the current card slug from the pathname
      const cardSlug = window.location.pathname.substring(1);
      window.location.href = `/login?message=Please log in to like this business card&redirect=${encodeURIComponent(cardSlug)}`;
      return;
    }

    if (hasLiked) {
      onUnlike();
    } else {
      onLike();
    }
  };

  const handleSubscribeClick = () => {
    if (isOwnBusiness) {
      toast.error("You cannot subscribe to your own business card", {
        description: "Business owners cannot subscribe to their own cards",
        position: "top-center",
      });
      return;
    }

    if (!isAuthenticated) {
      // Get the current card slug from the pathname
      const cardSlug = window.location.pathname.substring(1);
      window.location.href = `/login?message=Please log in to subscribe to this business card&redirect=${encodeURIComponent(cardSlug)}`;
      return;
    }

    if (isSubscribed) {
      onUnsubscribe();
    } else {
      onSubscribe();
    }
  };

  // Share handler
  const handleShare = () => {
    if (!businessSlug) {
      toast.error("Business slug not available.");
      return;
    }

    const shareUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessSlug}`;
    const shareText = `Check out ${businessName || "this business"}'s digital card on Dukancard!`;

    // Use Web Share API if available
    if (navigator.share) {
      navigator.share({
        title: `${businessName} - Digital Business Card`,
        text: shareText,
        url: shareUrl,
      }).catch((error) => {
        console.error('Error sharing:', error);
        // Fallback to clipboard
        copyToClipboard(shareUrl);
      });
    } else {
      // Fallback to clipboard
      copyToClipboard(shareUrl);
    }
  };

  // Helper function to copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => toast.success("Link copied to clipboard!"))
      .catch(() => toast.error("Failed to copy link."));
  };

  if (!isClient) {
    return null; // Don't render during SSR
  }

  return (
    <>
      {/* Desktop and tablet version - buttons at the bottom */}
      <div className="absolute -right-16 bottom-4 z-20 flex flex-col hidden sm:block">
        <div className="relative group">
          <div
            ref={ref}
            className="relative flex flex-col space-y-3 p-2"
          >
            {/* Like Button */}
            <div className="relative">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className={cn(
                        "h-10 w-10 rounded-full relative overflow-hidden transition-all duration-300 shadow-none bg-transparent",
                        hasLiked
                          ? "text-red-500 border-red-500 hover:text-red-600 hover:bg-transparent hover:border-red-600"
                          : "text-neutral-700 dark:text-neutral-300 border-neutral-400 dark:border-neutral-600 hover:bg-transparent"
                      )}
                      onClick={handleLikeClick}
                      disabled={isLoadingInteraction}
                      aria-label={hasLiked ? "Unlike" : "Like"}
                    >
                      {isLoadingInteraction ? (
                        <Loader2 className="h-6 w-6 animate-spin" />
                      ) : (
                        <Heart
                          className={cn("h-6 w-6", hasLiked && "fill-current")}
                        />
                      )}

                      {/* Background effects */}
                      {hasLiked && <GlowEffect color="rgba(239, 68, 68, 0.5)" />}
                      {hasLiked && <PulseEffect color="rgba(239, 68, 68, 0.7)" />}
                    </Button>
                  </TooltipTrigger>
                  {(!isAuthenticated || isOwnBusiness) && (
                    <TooltipContent
                      side="right"
                      className="bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700"
                    >
                      {isOwnBusiness ? (
                        <div className="flex flex-col gap-1">
                          <p className="font-medium text-red-500 dark:text-red-400">
                            You cannot like your own business card
                          </p>
                          <p className="text-xs text-neutral-500 dark:text-neutral-400">
                            Business owners cannot like their own cards
                          </p>
                        </div>
                      ) : (
                        <p>Please log in to like this business card</p>
                      )}
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Subscribe Button */}
            <div className="relative">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className={cn(
                        "h-10 w-10 rounded-full relative overflow-hidden transition-all duration-300 shadow-none bg-transparent",
                        isSubscribed
                          ? "text-blue-500 border-blue-500 hover:text-blue-600 hover:bg-transparent hover:border-blue-600"
                          : "text-neutral-700 dark:text-neutral-300 border-neutral-400 dark:border-neutral-600 hover:bg-transparent"
                      )}
                      onClick={handleSubscribeClick}
                      disabled={isLoadingInteraction || isOwnBusiness}
                      aria-label={isSubscribed ? "Unsubscribe" : "Subscribe"}
                    >
                      {isLoadingInteraction ? (
                        <Loader2 className="h-6 w-6 animate-spin" />
                      ) : (
                        isSubscribed ? (
                          <UserMinus className="h-6 w-6" />
                        ) : (
                          <UserPlus className="h-6 w-6" />
                        )
                      )}

                      {/* Background effects */}
                      {isSubscribed && <GlowEffect color="rgba(59, 130, 246, 0.5)" />}
                      {isSubscribed && <PulseEffect color="rgba(59, 130, 246, 0.7)" />}
                    </Button>
                  </TooltipTrigger>
                  {(!isAuthenticated || isOwnBusiness) && (
                    <TooltipContent
                      side="right"
                      className="bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700"
                    >
                      {isOwnBusiness ? (
                        <div className="flex flex-col gap-1">
                          <p className="font-medium text-red-500 dark:text-red-400">
                            You cannot subscribe to your own business card
                          </p>
                          <p className="text-xs text-neutral-500 dark:text-neutral-400">
                            Business owners cannot subscribe to their own cards
                          </p>
                        </div>
                      ) : (
                        <p>Please log in to subscribe to this business card</p>
                      )}
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Review Button */}
            {onReviewClick && (
              <div className="relative">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-10 w-10 rounded-full relative overflow-hidden transition-all duration-300 shadow-none bg-transparent text-amber-500 border-amber-500 hover:text-amber-600 hover:bg-transparent hover:border-amber-600"
                        onClick={(e) => {
                          e.preventDefault();
                          if (onReviewClick) onReviewClick();
                        }}
                        aria-label="Leave a Review"
                      >
                        <Star className="h-6 w-6" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent
                      side="right"
                      className="bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700"
                    >
                      Leave a Review
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            )}

            {/* Share Button */}
            {businessSlug && (
              <div className="relative">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-10 w-10 rounded-full relative overflow-hidden transition-all duration-300 shadow-none bg-transparent text-green-500 border-green-500 hover:text-green-600 hover:bg-transparent hover:border-green-600"
                        onClick={handleShare}
                        aria-label="Share"
                      >
                        <Share2 className="h-6 w-6" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent
                      side="right"
                      className="bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700"
                    >
                      Share this card
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile version - buttons below the card in a horizontal row */}
      <div className="w-full flex justify-center space-x-4 sm:hidden">
        {/* Like Button */}
        <Button
          variant="outline"
          size="icon"
          className={cn(
            "h-10 w-10 rounded-full relative overflow-hidden shadow-md bg-transparent sm:shadow-none",
            hasLiked
              ? "text-red-500 border-red-500 hover:text-red-600 hover:bg-transparent hover:border-red-600"
              : "text-neutral-700 dark:text-neutral-300 border-neutral-400 dark:border-neutral-600 hover:bg-transparent"
          )}
          onClick={handleLikeClick}
          disabled={isLoadingInteraction || isOwnBusiness}
          aria-label={hasLiked ? "Unlike" : "Like"}
        >
          {isLoadingInteraction ? (
            <Loader2 className="h-6 w-6 animate-spin" />
          ) : (
            <Heart
              className={cn("h-6 w-6", hasLiked && "fill-current")}
            />
          )}
          {hasLiked && <GlowEffect color="rgba(239, 68, 68, 0.5)" />}
        </Button>

        {/* Subscribe Button */}
        <Button
          variant="outline"
          size="icon"
          className={cn(
            "h-10 w-10 rounded-full relative overflow-hidden shadow-md bg-transparent sm:shadow-none",
            isSubscribed
              ? "text-blue-500 border-blue-500 hover:text-blue-600 hover:bg-transparent hover:border-blue-600"
              : "text-neutral-700 dark:text-neutral-300 border-neutral-400 dark:border-neutral-600 hover:bg-transparent"
          )}
          onClick={handleSubscribeClick}
          disabled={isLoadingInteraction || isOwnBusiness}
          aria-label={isSubscribed ? "Unsubscribe" : "Subscribe"}
        >
          {isLoadingInteraction ? (
            <Loader2 className="h-6 w-6 animate-spin" />
          ) : (
            isSubscribed ? (
              <UserMinus className="h-6 w-6" />
            ) : (
              <UserPlus className="h-6 w-6" />
            )
          )}
          {isSubscribed && <GlowEffect color="rgba(59, 130, 246, 0.5)" />}
        </Button>

        {/* Review Button */}
        {onReviewClick && (
          <Button
            variant="outline"
            size="icon"
            className="h-10 w-10 rounded-full relative overflow-hidden shadow-md bg-transparent sm:shadow-none text-amber-500 border-amber-500 hover:text-amber-600 hover:bg-transparent hover:border-amber-600"
            onClick={(e) => {
              e.preventDefault();
              if (onReviewClick) onReviewClick();
            }}
            aria-label="Leave a Review"
          >
            <Star className="h-6 w-6" />
          </Button>
        )}

        {/* Share Button */}
        {businessSlug && (
          <Button
            variant="outline"
            size="icon"
            className="h-10 w-10 rounded-full relative overflow-hidden shadow-md bg-transparent sm:shadow-none text-green-500 border-green-500 hover:text-green-600 hover:bg-transparent hover:border-green-600"
            onClick={handleShare}
            aria-label="Share"
          >
            <Share2 className="h-6 w-6" />
          </Button>
        )}
      </div>
    </>
  );
}
