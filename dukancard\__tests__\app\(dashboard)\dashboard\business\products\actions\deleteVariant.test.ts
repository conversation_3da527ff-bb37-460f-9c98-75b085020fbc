import { deleteProductVariant, deleteMultipleVariants, deleteAllProductVariants } from '@/app/(dashboard)/dashboard/business/products/actions/deleteVariant';
import { createClient } from '@/utils/supabase/server';
import { createAdminClient } from '@/utils/supabase/admin';
import { revalidatePath } from 'next/cache';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));
jest.mock('@/utils/supabase/admin', () => ({
    createAdminClient: jest.fn(),
}));
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;
const mockAdminSupabase = createAdminClient as jest.Mock;

describe('Product Variant Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('deleteProductVariant', () => {
    it('should return an error if the user is not authenticated', async () => {
      // Arrange
      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
        },
      });

      // Act
      const result = await deleteProductVariant('var-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('User not authenticated.');
    });

    it('should delete a variant successfully', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockVariant = { id: 'var-123', product_id: 'prod-123', products_services: { business_id: 'user-123' }, images: [] };
      const mockDelete = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({ data: mockVariant, error: null });

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn(() => ({
          delete: mockDelete,
          eq: mockEq,
          select: jest.fn().mockReturnThis(),
          single: mockSingle,
        })),
      });

      mockAdminSupabase.mockReturnValue({
        storage: {
            from: jest.fn().mockReturnThis(),
            remove: jest.fn().mockResolvedValue({ data: null, error: null }),
            list: jest.fn().mockResolvedValue({ data: [], error: null }),
        }
    });

      // Act
      const result = await deleteProductVariant('var-123');

      // Assert
      expect(result.success).toBe(true);
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
    });
  });

  describe('deleteMultipleVariants', () => {
    it('should delete multiple variants successfully', async () => {
        // Arrange
        const mockUser = { id: 'user-123' };
        const mockVariants = [
            { id: 'var-1', product_id: 'prod-1', products_services: { business_id: 'user-123' }, images: [] },
            { id: 'var-2', product_id: 'prod-1', products_services: { business_id: 'user-123' }, images: [] }
        ];
        const mockDelete = jest.fn().mockResolvedValue({ error: null });
        const mockIn = jest.fn().mockReturnThis();
        const mockSelect = jest.fn().mockResolvedValue({ data: mockVariants, error: null });
  
        mockSupabase.mockReturnValue({
          auth: {
            getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
          },
          from: jest.fn((table: string) => {
            if (table === 'product_variants') {
              return {
                select: jest.fn().mockReturnThis(),
                in: jest.fn().mockResolvedValue({ data: mockVariants, error: null }),
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null }),
              };
            }
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              delete: jest.fn().mockReturnThis(),
              in: jest.fn().mockReturnThis(),
            };
          }),
        });

        mockAdminSupabase.mockReturnValue({
            storage: {
                from: jest.fn().mockReturnThis(),
                remove: jest.fn().mockResolvedValue({ data: null, error: null }),
                list: jest.fn().mockResolvedValue({ data: [], error: null }),
            }
        });
  
        // Act
        const result = await deleteMultipleVariants(['var-1', 'var-2']);
  
        // Assert
        expect(result.success).toBe(true);
        expect(result.deleted_count).toBe(2);
        expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
      });
  });

  describe('deleteAllProductVariants', () => {
    it('should delete all variants for a product', async () => {
        // Arrange
        const mockUser = { id: 'user-123' };
        const mockProduct = { id: 'prod-123', business_id: 'user-123' };
        const mockDelete = jest.fn().mockResolvedValue({ error: null });
        const mockEq = jest.fn().mockReturnThis();
        const mockSingle = jest.fn().mockResolvedValue({ data: mockProduct, error: null });
  
        mockSupabase.mockReturnValue({
          auth: {
            getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
          },
          from: jest.fn((table: string) => {
            if (table === 'products_services') {
              return {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
              };
            }
            if (table === 'product_variants') {
              return {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ count: 2, error: null }),
                delete: jest.fn().mockReturnThis(),
              };
            }
            return {
              delete: jest.fn().mockReturnThis(),
              eq: jest.fn().mockResolvedValue({ error: null }),
            };
          }),
        });
  
        // Act
        const result = await deleteAllProductVariants('prod-123');
  
        // Assert
        expect(result.success).toBe(true);
      });
  });
});