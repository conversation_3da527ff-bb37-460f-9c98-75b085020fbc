"use client";

import { useEffect, useState } from "react";
import { useInView } from "react-intersection-observer";
import { motion } from "framer-motion";
import {
  CreditCard,
  Store,
  BarChart3,
  Users,
  Globe,
  ShieldCheck,
  Smartphone,
  MessageCircle,
  Settings,
  Tag,
  Palette,
  Download,
} from "lucide-react";

export default function FeatureBackground() {
  const [isClient, setIsClient] = useState(false);
  const { ref, inView: isInView } = useInView({
    threshold: 0.1,
    triggerOnce: false
  });

  // Check if we're on mobile
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    setIsClient(true);
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Define feature icons
  const featureIcons = [
    { icon: <CreditCard />, color: "var(--brand-gold)" },
    { icon: <Store />, color: "var(--brand-gold)" },
    { icon: <BarChart3 />, color: "var(--brand-gold)" },
    { icon: <Users />, color: "var(--brand-gold)" },
    { icon: <Globe />, color: "var(--brand-gold)" },
    { icon: <ShieldCheck />, color: "var(--brand-gold)" },
    { icon: <Smartphone />, color: "var(--brand-gold)" },
    { icon: <MessageCircle />, color: "var(--brand-gold)" },
    { icon: <Settings />, color: "var(--brand-gold)" },
    { icon: <Tag />, color: "var(--brand-gold)" },
    { icon: <Palette />, color: "var(--brand-gold)" },
    { icon: <Download />, color: "var(--brand-gold)" },
  ];

  // Generate random positions for floating icons
  const floatingIcons = Array.from({ length: isMobile ? 6 : 12 }, (_, i) => ({
    id: i,
    icon: featureIcons[i % featureIcons.length],
    left: Math.random() * 100,
    top: Math.random() * 100,
    size: 1 + Math.random() * 1.5,
    delay: Math.random() * 5,
    duration: 15 + Math.random() * 20,
    opacity: 0.1 + Math.random() * 0.2,
  }));

  // Generate feature cards for background
  const featureCards = Array.from({ length: isMobile ? 3 : 6 }, (_, i) => ({
    id: i,
    left: Math.random() * 100,
    top: Math.random() * 100,
    width: 80 + Math.random() * 60,
    height: 100 + Math.random() * 40,
    delay: Math.random() * 5,
    duration: 25 + Math.random() * 15,
    opacity: 0.03 + Math.random() * 0.05,
  }));

  return (
    <div ref={ref} className="absolute top-0 left-0 right-0 w-full h-full overflow-hidden pointer-events-none z-0">
      {isClient && isInView && (
        <>
          {/* Background gradient blobs */}
          <motion.div
            className="absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold)]/5 blur-3xl dark:bg-[var(--brand-gold)]/10"
            initial={{ opacity: 0.5 }}
            animate={{ opacity: 0.7 }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          <motion.div
            className="absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10"
            initial={{ opacity: 0.5 }}
            animate={{ opacity: 0.7 }}
            transition={{
              duration: 5,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 1
            }}
          />

          {/* Feature grid pattern */}
          <svg className="absolute inset-0 w-full h-full opacity-5 dark:opacity-10" xmlns="http://www.w3.org/2000/svg">
            <pattern id="grid-pattern" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="currentColor" strokeWidth="0.5" strokeOpacity="0.3" />
            </pattern>
            <rect width="100%" height="100%" fill="url(#grid-pattern)" />
          </svg>

          {/* Floating feature icons */}
          {floatingIcons.map((item) => (
            <motion.div
              key={`icon-${item.id}`}
              className="absolute"
              style={{
                left: `${item.left}%`,
                top: `${item.top}%`,
                color: item.icon.color,
                opacity: item.opacity,
                scale: item.size,
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: item.opacity }}
              transition={{
                duration: 1,
                delay: item.delay * 0.2
              }}
            >
              {item.icon.icon}
            </motion.div>
          ))}

          {/* Feature cards in background */}
          {featureCards.map((card) => (
            <motion.div
              key={`card-${card.id}`}
              className="absolute rounded-xl border border-[var(--brand-gold)]/10 dark:border-[var(--brand-gold)]/20 bg-white/5 dark:bg-white/10"
              style={{
                left: `${card.left}%`,
                top: `${card.top}%`,
                width: `${card.width}px`,
                height: `${card.height}px`,
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: card.opacity }}
              transition={{
                duration: 1,
                delay: card.delay * 0.2
              }}
            />
          ))}
        </>
      )}
    </div>
  );
}
