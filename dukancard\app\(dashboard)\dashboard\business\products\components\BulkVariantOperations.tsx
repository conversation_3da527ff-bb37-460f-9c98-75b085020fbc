"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  EyeOff,
  DollarSign,
  Check,
  X,
  Loader2,
  <PERSON><PERSON><PERSON>riangle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { ProductVariant } from "@/types/variants";
import { cn } from "@/lib/utils";

type BulkOperation =
  | "enable"
  | "disable"
  | "update_base_price"
  | "update_discounted_price"
  | "apply_discount";

interface BulkVariantOperationsProps {
  variants: ProductVariant[];
  selectedVariantIds: string[];
  onSelectionChange: (_selectedIds: string[]) => void;
  onBulkUpdate: (
    _variantIds: string[],
    _operation: BulkOperation,
    _value?: number
  ) => Promise<void>;
  disabled?: boolean;
  className?: string;
}

export default function BulkVariantOperations({
  variants,
  selectedVariantIds,
  onSelectionChange,
  onBulkUpdate,
  disabled = false,
  className,
}: BulkVariantOperationsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState<
    BulkOperation | ""
  >("");
  const [operationValue, setOperationValue] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const selectedVariants = variants.filter((variant) =>
    selectedVariantIds.includes(variant.id)
  );
  const hasSelection = selectedVariantIds.length > 0;

  const handleSelectAll = () => {
    if (selectedVariantIds.length === variants.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(variants.map((v) => v.id));
    }
  };

  const handleVariantToggle = (variantId: string) => {
    if (selectedVariantIds.includes(variantId)) {
      onSelectionChange(selectedVariantIds.filter((id) => id !== variantId));
    } else {
      onSelectionChange([...selectedVariantIds, variantId]);
    }
  };

  const handleBulkOperation = async () => {
    if (!selectedOperation || selectedVariantIds.length === 0) return;

    setIsSubmitting(true);
    try {
      let value: number | undefined;

      if (
        selectedOperation === "update_base_price" ||
        selectedOperation === "update_discounted_price" ||
        selectedOperation === "apply_discount"
      ) {
        const numValue = parseFloat(operationValue);
        if (isNaN(numValue) || numValue < 0) {
          toast.error("Please enter a valid positive number");
          return;
        }
        value = numValue;
      }

      await onBulkUpdate(selectedVariantIds, selectedOperation, value);

      // Reset form
      setSelectedOperation("");
      setOperationValue("");
      setIsOpen(false);
      onSelectionChange([]);

      toast.success(
        `Successfully updated ${selectedVariantIds.length} variant${
          selectedVariantIds.length > 1 ? "s" : ""
        }`
      );
    } catch (error) {
      console.error("Bulk operation failed:", error);
      toast.error("Failed to perform bulk operation. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getOperationDescription = () => {
    switch (selectedOperation) {
      case "enable":
        return "Make selected variants available for purchase";
      case "disable":
        return "Make selected variants unavailable for purchase";
      case "update_base_price":
        return "Set a new base price for selected variants";
      case "update_discounted_price":
        return "Set a new discounted price for selected variants";
      case "apply_discount":
        return "Apply a percentage discount to base prices of selected variants";
      default:
        return "Choose an operation to perform on selected variants";
    }
  };

  const requiresValue =
    selectedOperation === "update_base_price" ||
    selectedOperation === "update_discounted_price" ||
    selectedOperation === "apply_discount";

  const getValueLabel = () => {
    switch (selectedOperation) {
      case "update_base_price":
        return "New Base Price (₹)";
      case "update_discounted_price":
        return "New Discounted Price (₹)";
      case "apply_discount":
        return "Discount Percentage (%)";
      default:
        return "Value";
    }
  };

  const getValuePlaceholder = () => {
    switch (selectedOperation) {
      case "update_base_price":
        return "Enter new base price";
      case "update_discounted_price":
        return "Enter new discounted price";
      case "apply_discount":
        return "Enter discount percentage (e.g., 10 for 10%)";
      default:
        return "Enter value";
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Selection Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Checkbox
            checked={
              selectedVariantIds.length === variants.length &&
              variants.length > 0
            }
            onCheckedChange={handleSelectAll}
            disabled={disabled || variants.length === 0}
          />
          <span className="text-sm text-neutral-600 dark:text-neutral-400">
            {selectedVariantIds.length === 0
              ? "Select variants for bulk operations"
              : `${selectedVariantIds.length} of ${variants.length} selected`}
          </span>
        </div>

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={!hasSelection || disabled}
              className="gap-2"
            >
              <Settings className="h-4 w-4" />
              Bulk Operations
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Bulk Variant Operations</DialogTitle>
              <DialogDescription>
                Perform operations on {selectedVariantIds.length} selected
                variant{selectedVariantIds.length > 1 ? "s" : ""}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Selected Variants Preview */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Selected Variants</Label>
                <div className="max-h-32 overflow-y-auto space-y-1 p-2 border rounded-md bg-neutral-50 dark:bg-neutral-800">
                  {selectedVariants.map((variant) => (
                    <div
                      key={variant.id}
                      className="flex items-center justify-between text-xs"
                    >
                      <span className="truncate">{variant.variant_name}</span>
                      <Badge
                        variant={variant.is_available ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {variant.is_available ? "Available" : "Unavailable"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Operation Selection */}
              <div className="space-y-2">
                <Label htmlFor="operation">Operation</Label>
                <Select
                  value={selectedOperation}
                  onValueChange={(value) =>
                    setSelectedOperation(value as BulkOperation)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an operation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="enable">
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4" />
                        Enable Variants
                      </div>
                    </SelectItem>
                    <SelectItem value="disable">
                      <div className="flex items-center gap-2">
                        <EyeOff className="h-4 w-4" />
                        Disable Variants
                      </div>
                    </SelectItem>
                    <SelectItem value="update_base_price">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Update Base Price
                      </div>
                    </SelectItem>
                    <SelectItem value="update_discounted_price">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Update Discounted Price
                      </div>
                    </SelectItem>
                    <SelectItem value="apply_discount">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Apply Discount
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Operation Description */}
              {selectedOperation && (
                <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-md">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    {getOperationDescription()}
                  </p>
                </div>
              )}

              {/* Value Input */}
              <AnimatePresence>
                {requiresValue && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-2"
                  >
                    <Label htmlFor="value">{getValueLabel()}</Label>
                    <Input
                      id="value"
                      type="number"
                      step={
                        selectedOperation === "apply_discount" ? "1" : "0.01"
                      }
                      min="0"
                      max={
                        selectedOperation === "apply_discount"
                          ? "100"
                          : undefined
                      }
                      placeholder={getValuePlaceholder()}
                      value={operationValue}
                      onChange={(e) => setOperationValue(e.target.value)}
                    />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Warning for Destructive Operations */}
              {(selectedOperation === "disable" ||
                selectedOperation === "update_base_price" ||
                selectedOperation === "update_discounted_price") && (
                <div className="flex items-start gap-2 p-3 bg-amber-50 dark:bg-amber-950 rounded-md">
                  <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-amber-800 dark:text-amber-200">
                    This operation will affect {selectedVariantIds.length}{" "}
                    variant{selectedVariantIds.length > 1 ? "s" : ""}. Make sure
                    you want to proceed.
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button
                  variant="outline"
                  onClick={() => setIsOpen(false)}
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  onClick={handleBulkOperation}
                  disabled={
                    !selectedOperation ||
                    isSubmitting ||
                    (requiresValue && !operationValue)
                  }
                  className="flex-1 bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Apply
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Variant Selection List */}
      {variants.length > 0 && (
        <div className="space-y-2">
          {variants.map((variant) => (
            <div
              key={variant.id}
              className={cn(
                "flex items-center gap-3 p-3 border rounded-lg transition-colors",
                selectedVariantIds.includes(variant.id)
                  ? "bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800"
                  : "bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800"
              )}
            >
              <Checkbox
                checked={selectedVariantIds.includes(variant.id)}
                onCheckedChange={() => handleVariantToggle(variant.id)}
                disabled={disabled}
              />

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-sm text-neutral-900 dark:text-neutral-100 truncate">
                    {variant.variant_name}
                  </h4>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={variant.is_available ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {variant.is_available ? "Available" : "Unavailable"}
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-1">
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(variant.variant_values).map(
                      ([type, value]) => (
                        <Badge
                          key={`${type}-${value}`}
                          variant="outline"
                          className="text-xs"
                        >
                          {type}: {String(value)}
                        </Badge>
                      )
                    )}
                  </div>

                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    {variant.discounted_price ? (
                      <span className="text-green-600 dark:text-green-400">
                        ₹{variant.discounted_price.toLocaleString()}
                      </span>
                    ) : variant.base_price ? (
                      <span>₹{variant.base_price.toLocaleString()}</span>
                    ) : (
                      <span className="text-neutral-400">No price set</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
