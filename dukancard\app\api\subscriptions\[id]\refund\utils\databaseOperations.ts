import { SupabaseClient } from "@supabase/supabase-js";
import { RefundData } from "./types";

/**
 * Logs a manual refund request (refunds table has been removed)
 * This function now only logs the refund request for audit purposes
 */
export async function insertManualRefundRecord(
  _supabase: SupabaseClient,
  userId: string,
  subscriptionId: string,
  speed: string
) {
  try {
    // Log the manual refund request for audit purposes
    console.log(`[MANUAL_REFUND] Manual refund requested for user ${userId}, subscription ${subscriptionId}`);
    console.log(`[MANUAL_REFUND] Speed requested: ${speed}`);
    console.log(`[MANUAL_REFUND] Timestamp: ${new Date().toISOString()}`);

    // Note: Refunds table has been removed, so we only log the request
    // Manual refunds should be processed through external systems
    return { success: true };
  } catch (error) {
    console.error("Error logging manual refund record:", error);
    return { success: false, error };
  }
}

/**
 * ENHANCED: Updates the payment subscription to mark as cancelled using atomic RPC
 */
export async function updateSubscriptionAfterCancellation(
  supabase: SupabaseClient,
  subscriptionId: string
) {
  try {
    // Get subscription details first
    const { data: subscription } = await supabase
      .from("payment_subscriptions")
      .select("business_profile_id")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (!subscription) {
      console.error("Subscription not found for cancellation update");
      return { success: false, error: "Subscription not found" };
    }

    // ENHANCED: Use atomic RPC function for transaction safety
    const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
      p_subscription_id: subscriptionId,
      p_new_status: 'cancelled',
      p_business_profile_id: subscription.business_profile_id,
      p_has_active_subscription: false,
      p_additional_data: {
        cancellation_requested_at: null,
        cancelled_at: new Date().toISOString()
      },
      p_webhook_timestamp: null
    });

    if (atomicError || !atomicResult?.success) {
      console.error("Error updating subscription atomically:", atomicError || atomicResult?.error);
      return { success: false, error: atomicError || atomicResult?.error };
    }

    console.log(`[REFUND] Successfully updated subscription ${subscriptionId} and business profile ${subscription.business_profile_id} atomically`);
    return { success: true };
  } catch (error) {
    console.error("Error updating subscription:", error);
    return { success: false, error };
  }
}

/**
 * ENHANCED: Updates the business profile to mark as cancelled using atomic RPC
 */
export async function updateBusinessProfileAfterCancellation(
  supabase: SupabaseClient,
  userId: string
) {
  try {
    // Get the active subscription for this user
    const { data: subscription } = await supabase
      .from("payment_subscriptions")
      .select("razorpay_subscription_id")
      .eq("business_profile_id", userId)
      .eq("subscription_status", "active")
      .single();

    if (!subscription) {
      console.log("No active subscription found for user:", userId);
      return { success: true }; // No active subscription to cancel
    }

    // ENHANCED: Use atomic RPC function for transaction safety
    const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
      p_subscription_id: subscription.razorpay_subscription_id,
      p_new_status: 'cancelled',
      p_business_profile_id: userId,
      p_has_active_subscription: false,
      p_additional_data: {
        cancelled_at: new Date().toISOString()
      },
      p_webhook_timestamp: null
    });

    if (atomicError || !atomicResult?.success) {
      console.error("Error updating subscription atomically:", atomicError || atomicResult?.error);
      return { success: false, error: atomicError || atomicResult?.error };
    }

    console.log(`[REFUND] Successfully cancelled subscription for user ${userId} atomically`);
    return { success: true };
  } catch (error) {
    console.error("Exception updating business profile:", error);
    return { success: false, error };
  }
}

/**
 * Logs a refund record (refunds table has been removed)
 * This function now only logs the refund data for audit purposes
 */
export async function insertRefundRecord(
  _supabase: SupabaseClient,
  userId: string,
  paymentId: string,
  subscriptionId: string,
  refundData: RefundData,
  speed: string
) {
  try {
    // Log the refund data for audit purposes
    console.log(`[REFUND_RECORD] Refund processed for user ${userId}`);
    console.log(`[REFUND_RECORD] Payment ID: ${paymentId}`);
    console.log(`[REFUND_RECORD] Subscription ID: ${subscriptionId}`);
    console.log(`[REFUND_RECORD] Refund ID: ${refundData.id}`);
    console.log(`[REFUND_RECORD] Amount: ${refundData.amount} ${refundData.currency}`);
    console.log(`[REFUND_RECORD] Status: ${refundData.status}`);
    console.log(`[REFUND_RECORD] Speed requested: ${refundData.speed_requested || speed}`);

    if (refundData.speed_processed) {
      console.log(`[REFUND_RECORD] Speed processed: ${refundData.speed_processed}`);
    }

    if (refundData.notes && Object.keys(refundData.notes).length > 0) {
      console.log(`[REFUND_RECORD] Notes:`, refundData.notes);
    }

    console.log(`[REFUND_RECORD] Timestamp: ${new Date().toISOString()}`);

    // Note: Refunds table has been removed, so we only log the refund data
    // Refund tracking is now handled through webhook events and processed_webhook_events table
    return { success: true };
  } catch (error) {
    console.error("Error logging refund record:", error);
    return { success: false, error };
  }
}
