import { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { Loader2 } from "lucide-react";
import ModernRefundPolicyClient from "./ModernRefundPolicyClient";

export const metadata: Metadata = {
  title: "Cancellation and Refund Policy",
  description:
    "Learn about Dukancard's cancellation and refund policy for subscription issues and payment problems.",
};

export default function RefundPolicyPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense
        fallback={
          <div className="min-h-screen flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-[var(--brand-gold)]" />
          </div>
        }
      >
        <ModernRefundPolicyClient />
      </Suspense>
    </div>
  );
}
