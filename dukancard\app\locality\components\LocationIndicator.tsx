"use client";

import { useSearchParams } from "next/navigation";
import { MapPin, Search } from "lucide-react";
import { useLocalityContext } from "../context/LocalityContext";
import {
  BUSINESS_NAME_PARAM,
  PRODUCT_NAME_PARAM,
} from "@/app/(main)/discover/constants/urlParamConstants";

export default function LocationIndicator() {
  const searchParams = useSearchParams();
  const { searchResult, viewType, locality } = useLocalityContext();

  const businessName = searchParams.get(BUSINESS_NAME_PARAM);
  const productName = searchParams.get(PRODUCT_NAME_PARAM);

  // Format location for display
  const locationTitle = `${locality.name}, ${locality.divisionName}`;
  const fullLocation = `${locality.name}, ${locality.divisionName}, ${locality.district}, ${locality.stateName} - ${locality.pincode}`;

  // Determine the location text and icon based on search parameters
  let locationText = `All businesses and products in ${locationTitle}`;
  let LocationIcon = MapPin;
  let detailText = `Showing businesses and products from ${fullLocation}`;
  let highlightText = "";

  // If searching for a business or product
  if (businessName || productName) {
    LocationIcon = Search;
    const searchType = viewType === "cards" ? "businesses" : "products";
    const searchTerm = viewType === "cards" ? businessName : productName;
    
    locationText = `Search results for "${searchTerm}" in ${locationTitle}`;
    detailText = `Showing ${searchType} matching "${searchTerm}" in ${fullLocation}`;
    highlightText = searchTerm || "";
  }

  // If we have search results, show the count
  if (searchResult) {
    const count = searchResult.totalCount;
    const itemType = viewType === "cards" 
      ? (count === 1 ? "business" : "businesses") 
      : (count === 1 ? "product" : "products");
    
    detailText = `Found ${count} ${itemType} in ${fullLocation}`;
    
    if (highlightText) {
      detailText = `Found ${count} ${itemType} matching "${highlightText}" in ${fullLocation}`;
    }
  }

  return (
    <div className="container mx-auto px-4 mb-6">
      <div className="flex items-center mb-1">
        <LocationIcon className="mr-2 h-5 w-5 text-primary" />
        <h2 className="text-xl font-semibold">{locationText}</h2>
      </div>
      <p className="text-sm text-muted-foreground ml-7">{detailText}</p>
    </div>
  );
}
