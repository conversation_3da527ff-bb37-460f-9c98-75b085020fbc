"use client";

import { motion } from "framer-motion";
import { ReactNode } from "react";

interface FloatingCardProps {
  children: ReactNode;
  className?: string;
  delay?: number;
}

export default function FloatingCard({
  children,
  className = "",
  delay = 0,
}: FloatingCardProps) {
  return (
    <motion.div
      className={`relative bg-white/90 dark:bg-neutral-800/90 rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50 shadow-lg backdrop-blur-sm overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
    >
      {/* Decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-10 -right-10 w-20 h-20 rounded-full bg-[var(--brand-gold-rgb)]/5 blur-xl dark:bg-[var(--brand-gold-rgb)]/10"></div>
        <div className="absolute -bottom-10 -left-10 w-20 h-20 rounded-full bg-purple-500/5 blur-xl dark:bg-purple-500/10"></div>
      </div>
      
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-[var(--brand-gold)]/5 dark:from-neutral-800/5 dark:to-[var(--brand-gold)]/10 pointer-events-none"></div>
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
}
