"use client";

import { useState, useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle2, XCircle } from "lucide-react";

interface FeatureCardProps {
  icon: React.ReactElement & {
    props: {
      className?: string;
    };
  };
  title: string;
  description: string;
  index: number;
  available?: boolean;
}

export default function FeatureCard({
  icon,
  title,
  description,
  index,
  available = true,
}: FeatureCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(cardRef, { once: true, amount: 0.2 });

  // Helper function to determine the gradient color based on icon className
  const getGradientColor = (): string => {
    if (!icon.props.className) return "var(--brand-gold)";

    const className = icon.props.className;

    if (className.includes("text-[var(--brand-gold)]")) return "var(--brand-gold)";
    if (className.includes("text-blue-500")) return "#3b82f6";
    if (className.includes("text-purple-500")) return "#8b5cf6";
    if (className.includes("text-emerald-500")) return "#10b981";

    return "var(--brand-gold)";
  };

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="h-full"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card className="h-full bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 overflow-hidden relative group">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5 dark:opacity-10 pointer-events-none">
          <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
            <pattern id={`grid-pattern-${index}`} width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" strokeWidth="0.5" />
            </pattern>
            <rect width="100%" height="100%" fill={`url(#grid-pattern-${index})`} />
          </svg>
        </div>

        {/* Card content */}
        <div className="p-6 flex flex-col h-full relative z-10">
          {/* Icon container */}
          <motion.div
            className={`bg-neutral-100 dark:bg-neutral-800 p-3 rounded-lg inline-block mb-4 relative overflow-hidden ${
              isHovered ? "shadow-md" : ""
            }`}
            animate={{
              scale: isHovered ? 1.05 : 1,
              boxShadow: isHovered
                ? "0 4px 12px rgba(var(--brand-gold-rgb), 0.2)"
                : "0 0 0 rgba(0, 0, 0, 0)",
            }}
            transition={{ duration: 0.3 }}
          >
            {/* Icon */}
            <motion.div
              animate={{
                rotate: isHovered ? [0, -5, 5, 0] : 0,
              }}
              transition={{ duration: 0.5 }}
            >
              {icon}
            </motion.div>

            {/* Glow effect */}
            <motion.div
              className="absolute inset-0 bg-current opacity-10 rounded-lg"
              animate={{
                opacity: isHovered ? 0.2 : 0.1,
              }}
              transition={{ duration: 0.3 }}
            />
          </motion.div>

          {/* Title and availability badge */}
          <div className="flex items-start justify-between mb-2">
            <h3 className="text-lg font-semibold">{title}</h3>
            {available !== undefined && (
              <Badge
                variant={available ? "default" : "outline"}
                className={`ml-2 ${
                  available
                    ? "bg-green-500/10 text-green-600 dark:bg-green-500/20 dark:text-green-400 border-green-500/30"
                    : "bg-neutral-500/10 text-neutral-600 dark:bg-neutral-500/20 dark:text-neutral-400 border-neutral-500/30"
                }`}
              >
                {available ? (
                  <CheckCircle2 className="w-3 h-3 mr-1" />
                ) : (
                  <XCircle className="w-3 h-3 mr-1" />
                )}
                {available ? "Available" : "Coming Soon"}
              </Badge>
            )}
          </div>

          {/* Description */}
          <p className="text-sm text-neutral-600 dark:text-neutral-400 flex-grow">
            {description}
          </p>

          {/* Hover effect - bottom border */}
          <motion.div
            className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r"
            style={{
              backgroundImage: `linear-gradient(to right, ${getGradientColor()}, transparent)`,
            }}
            initial={{ scaleX: 0, originX: 0 }}
            animate={{ scaleX: isHovered ? 1 : 0 }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </Card>
    </motion.div>
  );
}
