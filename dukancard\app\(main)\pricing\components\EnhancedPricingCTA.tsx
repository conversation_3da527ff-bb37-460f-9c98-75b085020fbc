"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ArrowR<PERSON>, Sparkles, CalendarCheck } from "lucide-react";
import Link from "next/link";
import AnimatedTitle from "./animations/AnimatedTitle";

type AnimationVariant = {
  hidden: { opacity: number; y: number };
  visible: {
    opacity: number;
    y: number;
    transition: { duration: number; ease: string };
  } | ((_i: number) => {
    opacity: number;
    y: number;
    transition: { duration: number; delay: number; ease: string };
  });
};

interface EnhancedPricingCTAProps {
  title: string;
  description: string;
  buttons: { text: string; variant: "default" | "outline" }[];
  sectionFadeIn: AnimationVariant;
  itemFadeIn: AnimationVariant;
}

export default function EnhancedPricingCTA({
  title,
  description,
}: EnhancedPricingCTAProps) {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.3 });

  return (
    <section
      ref={sectionRef}
      className="py-16 px-4 md:px-6 lg:px-8 relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          className="absolute inset-0 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : {}}
          transition={{ duration: 0.7 }}
        />

        {/* Animated particles */}
        {isInView && (
          <>
            <motion.div
              className="absolute top-1/4 left-1/4 w-2 h-2 rounded-full bg-[var(--brand-gold)]/30"
              animate={{
                y: [0, -30, 0],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />

            <motion.div
              className="absolute top-3/4 left-1/3 w-3 h-3 rounded-full bg-[var(--brand-gold)]/30"
              animate={{
                y: [0, -40, 0],
                opacity: [0.3, 0.7, 0.3]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                repeatType: "reverse",
                delay: 1
              }}
            />

            <motion.div
              className="absolute top-1/3 right-1/4 w-2 h-2 rounded-full bg-[var(--brand-gold)]/30"
              animate={{
                y: [0, -20, 0],
                opacity: [0.3, 0.5, 0.3]
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                repeatType: "reverse",
                delay: 0.5
              }}
            />

            <motion.div
              className="absolute top-2/3 right-1/3 w-4 h-4 rounded-full bg-[var(--brand-gold)]/30"
              animate={{
                y: [0, -50, 0],
                opacity: [0.3, 0.8, 0.3]
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                repeatType: "reverse",
                delay: 1.5
              }}
            />
          </>
        )}
      </div>

      <div className="max-w-4xl mx-auto text-center relative z-10">
        <AnimatedTitle
          title={title}
          highlightWords={["right"]}
          subtitle={description}
          className="mb-10"
        />

        {/* CTA Buttons - Get Started and Contact buttons */}
        <motion.div
          className="flex flex-col sm:flex-row justify-center gap-4 mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          {/* Get Started Button */}
          <Link href="/login">
            <Button
              variant="default"
              size="lg"
              className="px-6 py-6 rounded-full text-lg font-medium relative overflow-hidden bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900"
            >
              <span className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Get Started
                <motion.div
                  animate={{ x: [0, 5, 0] }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    repeatType: "loop",
                    ease: "easeInOut",
                  }}
                >
                  <ArrowRight className="w-5 h-5" />
                </motion.div>
              </span>

              {/* Animated glow effect for button */}
              <motion.span
                className="absolute inset-0 -z-10"
                animate={{
                  background: [
                    "radial-gradient(circle at 50% 50%, rgba(var(--brand-gold-rgb), 0.3) 0%, transparent 50%)",
                    "radial-gradient(circle at 50% 50%, rgba(var(--brand-gold-rgb), 0.4) 0%, transparent 50%)",
                    "radial-gradient(circle at 50% 50%, rgba(var(--brand-gold-rgb), 0.3) 0%, transparent 50%)",
                  ],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              />
            </Button>
          </Link>

          {/* Contact Button */}
          <Link href="/contact">
            <Button
              variant="outline"
              size="lg"
              className="px-6 py-6 rounded-full text-lg font-medium border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10"
            >
              <span className="flex items-center gap-2">
                <CalendarCheck className="w-5 h-5" />
                Contact Us
              </span>
            </Button>
          </Link>
        </motion.div>

        {/* Additional info */}
        <motion.p
          className="mt-8 text-sm text-muted-foreground"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : {}}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          No credit card required for free trial. Cancel anytime.
        </motion.p>
      </div>
    </section>
  );
}
