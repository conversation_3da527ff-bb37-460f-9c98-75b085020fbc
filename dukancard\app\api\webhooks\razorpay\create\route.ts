import { NextRequest, NextResponse } from "next/server";
import { createWebhook, CreateWebhookParams } from "@/lib/razorpay/services/webhook";

/**
 * API route to create a Razorpay webhook
 * 
 * This endpoint creates a webhook for a Razorpay account.
 * 
 * Request body:
 * {
 *   "account_id": "acc_123456789",
 *   "url": "https://example.com/webhooks/razorpay",
 *   "alert_email": "<EMAIL>",
 *   "secret": "webhook_secret",
 *   "events": [
 *     "payment.authorized",
 *     "payment.failed",
 *     "payment.captured",
 *     "subscription.charged",
 *     "subscription.cancelled"
 *   ]
 * }
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "id": "JebiXkKGYwua5L",
 *     "created_at": **********,
 *     "updated_at": **********,
 *     "service": "beta-api-live",
 *     "owner_id": "JOGUdtKu3dB03d",
 *     "owner_type": "merchant",
 *     "context": [],
 *     "disabled_at": 0,
 *     "url": "https://google.com",
 *     "alert_email": "<EMAIL>",
 *     "secret_exists": true,
 *     "entity": "webhook",
 *     "active": true,
 *     "events": [
 *       "payment.authorized",
 *       "payment.failed",
 *       "payment.captured",
 *       "payment.dispute.created",
 *       "refund.failed",
 *       "refund.created"
 *     ]
 *   }
 * }
 */
export async function POST(req: NextRequest) {
  try {
    // Get the request body
    const body = await req.json();

    // Extract parameters
    const {
      account_id,
      url,
      alert_email,
      secret,
      events
    } = body;

    // Validate required parameters
    if (!account_id) {
      return NextResponse.json(
        { success: false, message: "Missing account_id parameter" },
        { status: 400 }
      );
    }

    if (!url) {
      return NextResponse.json(
        { success: false, message: "Missing url parameter" },
        { status: 400 }
      );
    }

    if (!events || !Array.isArray(events) || events.length === 0) {
      return NextResponse.json(
        { success: false, message: "Missing or invalid events parameter" },
        { status: 400 }
      );
    }

    // Create webhook parameters
    const webhookParams: CreateWebhookParams = {
      url,
      events
    };

    // Add optional parameters if provided
    if (alert_email) {
      webhookParams.alert_email = alert_email;
    }

    if (secret) {
      webhookParams.secret = secret;
    }

    // Create the webhook
    const result = await createWebhook(account_id, webhookParams);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return success response
    return NextResponse.json(
      { success: true, data: result.data },
      { status: 201 }
    );
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK_CREATE] Error creating webhook:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Error creating webhook",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
