import { NextRequest, NextResponse } from "next/server";
import { handleRazorpayWebhook } from "@/lib/razorpay/webhooks/handleWebhook";
import { RazorpaySubscriptionEventType } from "@/lib/razorpay/webhooks/types";
import { validateWebhook } from "@/lib/razorpay/webhooks/validation";

/**
 * Razorpay webhook handler
 * @param req The incoming request
 * @returns The response
 *
 * This endpoint handles all Razorpay webhook events, including:
 * - subscription.authenticated
 * - subscription.activated
 * - subscription.charged
 * - subscription.pending
 * - subscription.halted
 * - subscription.cancelled
 * - subscription.completed
 * - subscription.updated
 * - payment.authorized
 * - payment.captured
 * - payment.failed
 * - invoice.paid
 * - refund.created
 * - refund.processed
 * - refund.failed
 *
 * Example webhook payload for subscription.charged event:
 * {
 *   "entity": "event",
 *   "account_id": "acc_JOGUdtKu3dB03d",
 *   "event": "subscription.charged",
 *   "contains": ["payment", "subscription"],
 *   "payload": {
 *     "payment": {
 *       "entity": {
 *         "id": "pay_JebiXkKGYwua5L",
 *         "entity": "payment",
 *         "amount": 10000,
 *         "currency": "INR",
 *         "status": "captured",
 *         "order_id": "order_JebiXkKGYwua5L",
 *         "invoice_id": null,
 *         "international": false,
 *         "method": "upi",
 *         "amount_refunded": 0,
 *         "refund_status": null,
 *         "captured": true,
 *         "description": "Subscription payment for plan: basic-plan-monthly",
 *         "card_id": null,
 *         "bank": null,
 *         "wallet": null,
 *         "vpa": "success@razorpay",
 *         "email": "<EMAIL>",
 *         "contact": "+************",
 *         "notes": {
 *           "subscription_id": "sub_JebiXkKGYwua5L"
 *         },
 *         "fee": 236,
 *         "tax": 36,
 *         "error_code": null,
 *         "error_description": null,
 *         "error_source": null,
 *         "error_step": null,
 *         "error_reason": null,
 *         "acquirer_data": {
 *           "rrn": "*********",
 *           "upi_transaction_id": "RAZORPAY*********"
 *         },
 *         "created_at": **********
 *       }
 *     },
 *     "subscription": {
 *       "id": "sub_JebiXkKGYwua5L",
 *       "entity": "subscription",
 *       "plan_id": "plan_JebiXkKGYwua5L",
 *       "customer_id": "cust_JebiXkKGYwua5L",
 *       "status": "active",
 *       "current_start": **********,
 *       "current_end": **********,
 *       "ended_at": null,
 *       "quantity": 1,
 *       "notes": {
 *         "business_id": "*********"
 *       },
 *       "charge_at": **********,
 *       "start_at": **********,
 *       "end_at": null,
 *       "auth_attempts": 1,
 *       "total_count": 12,
 *       "paid_count": 1,
 *       "customer_notify": true,
 *       "created_at": **********,
 *       "expire_by": null,
 *       "short_url": "https://rzp.io/i/abcdefgh",
 *       "has_scheduled_changes": false,
 *       "change_scheduled_at": null,
 *       "source": "api",
 *       "payment_method": "upi",
 *       "offer_id": null,
 *       "remaining_count": 11
 *     }
 *   },
 *   "created_at": **********
 * }
 */
export async function POST(req: NextRequest) {
  try {
    // First, clone the request to ensure we can read the body multiple times
    // We don't need to use the cloned request directly
    req.clone();

    // Get the raw request body as text first
    let rawBody: string;
    try {
      rawBody = await req.text();

      // Log webhook received
      console.log("[RAZORPAY_WEBHOOK] Received webhook with body length:", rawBody.length);

      // Check if the body is empty
      if (!rawBody || rawBody.trim() === '') {
        console.error("[RAZORPAY_WEBHOOK] Empty request body received");
        return NextResponse.json(
          { success: false, message: "Empty request body received" },
          { status: 400 }
        );
      }
    } catch (bodyError) {
      console.error("[RAZORPAY_WEBHOOK] Failed to read request body:", bodyError);
      return NextResponse.json(
        { success: false, message: "Failed to read request body" },
        { status: 400 }
      );
    }

    // Get the signature from the headers
    const signature = req.headers.get("x-razorpay-signature") || "";
    if (!signature) {
      console.error("[RAZORPAY_WEBHOOK] Missing signature header");
      return NextResponse.json(
        { success: false, message: "Missing signature header" },
        { status: 400 }
      );
    }

    // Get the unique Razorpay event ID for idempotency
    const razorpayEventId = req.headers.get("x-razorpay-event-id") || "";

    // Try to parse the JSON payload
    let payload;
    try {
      payload = JSON.parse(rawBody);

      // Check if the payload is empty or not an object
      if (!payload || typeof payload !== 'object' || Object.keys(payload).length === 0) {
        console.error("[RAZORPAY_WEBHOOK] Empty or invalid payload object");
        return NextResponse.json(
          { success: false, message: "Empty or invalid payload object" },
          { status: 400 }
        );
      }
    } catch (jsonError) {
      console.error("[RAZORPAY_WEBHOOK] Failed to parse JSON payload:", jsonError);

      // If the content type indicates this should be JSON, return an error
      const contentType = req.headers.get("content-type") || "";
      if (contentType.includes("application/json")) {
        return NextResponse.json(
          { success: false, message: "Invalid JSON payload" },
          { status: 400 }
        );
      }

      // For non-JSON content types, we'll create a simple payload wrapper
      // This allows handling of form data or other formats that Razorpay might send
      payload = {
        rawContent: rawBody,
        contentType: contentType,
        event: "unknown"
      };
    }

    // Enhanced webhook validation
    const expectedAccountId = process.env.RAZORPAY_ACCOUNT_ID;
    const validationResult = validateWebhook(payload, expectedAccountId);

    if (!validationResult.valid) {
      console.error("[RAZORPAY_WEBHOOK] Webhook validation failed:", validationResult.errors);
      return NextResponse.json(
        {
          success: false,
          message: "Webhook validation failed",
          errors: validationResult.errors
        },
        { status: 400 }
      );
    }

    // Log warnings if any
    if (validationResult.warnings.length > 0) {
      console.warn("[RAZORPAY_WEBHOOK] Webhook validation warnings:", validationResult.warnings);
    }

    // Validate the event type
    const eventType = payload.event;
    if (!eventType) {
      console.error("[RAZORPAY_WEBHOOK] Missing event type in payload");
      return NextResponse.json(
        { success: false, message: "Missing event type in payload" },
        { status: 400 }
      );
    }

    // Check if this is a subscription or payment event
    const isSubscriptionEvent = Object.values(RazorpaySubscriptionEventType).includes(eventType as RazorpaySubscriptionEventType);

    // Log the event type for tracking
    console.log(`[RAZORPAY_WEBHOOK] Processing ${eventType} event (ID: ${razorpayEventId})`);

    // For non-handled events, acknowledge but don't process
    if (!isSubscriptionEvent) {
      console.log(`[RAZORPAY_WEBHOOK] Unhandled event type: ${eventType}`);
      return NextResponse.json(
        { success: true, message: "Unhandled event acknowledged" },
        { status: 200 }
      );
    }

    // Handle the webhook - pass the raw body for signature verification,
    // the parsed payload for processing, and the Razorpay event ID for idempotency
    const result = await handleRazorpayWebhook(payload, signature, undefined, rawBody, razorpayEventId);

    if (!result.success) {
      console.error(`[RAZORPAY_WEBHOOK] Error handling webhook: ${result.message}`);

      // Return a 202 Accepted status even for errors to prevent Razorpay from retrying
      // We'll handle retries ourselves using our error tracking system
      return NextResponse.json(
        {
          success: false,
          message: result.message,
          error_id: result.error_id,
          note: "Error logged for retry. No need to resend this webhook."
        },
        { status: 202 }
      );
    }

    // Return success response
    return NextResponse.json(
      { success: true, message: result.message },
      { status: 200 }
    );
  } catch (error) {
    // Log detailed error information
    console.error("[RAZORPAY_WEBHOOK] Error processing webhook:", error);

    // Create a more detailed error message
    const errorMessage = error instanceof Error
      ? `${error.name}: ${error.message}`
      : String(error);

    // Return a 202 Accepted status even for errors to prevent Razorpay from retrying
    // We'll handle retries ourselves using our error tracking system
    return NextResponse.json(
      {
        success: false,
        message: `Error processing webhook: ${errorMessage}`,
        note: "Error logged for retry. No need to resend this webhook."
      },
      { status: 202 }
    );
  }
}
