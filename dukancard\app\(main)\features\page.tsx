import ModernFeaturesClient from "./ModernFeaturesClient";
import { Metadata } from "next"; // Import Metadata type

export async function generateMetadata(): Promise<Metadata> {
  const title = "Dukancard Features"; // More specific title
  const description =
    "Discover Dukancard's features: customizable digital cards, online storefronts, product listings, QR codes, analytics, and more for Indian businesses."; // Refined description
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/features`;
  const ogImage = `${siteUrl}/opengraph-image.png`; // Default OG image

  return {
    title, // Uses template: "Dukancard Features - Dukancard"
    description,
    keywords: [ // Added keywords
      "Dukancard features",
      "digital business card features",
      "online storefront India",
      "QR code business card",
      "business analytics tool",
      "product listing platform",
    ],
    alternates: {
      canonical: "/features", // Relative canonical path
    },
    openGraph: {
      title: title,
      description: description,
      url: pageUrl,
      siteName: "Dukancard",
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "Features of Dukancard Digital Business Cards",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: title,
      description: description,
      images: [ogImage],
    },
    // Add WebPage Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: "Dukancard",
          url: siteUrl,
        },
      }),
    },
  };
}

export default async function FeaturesPage() {
  return <ModernFeaturesClient />;
}
