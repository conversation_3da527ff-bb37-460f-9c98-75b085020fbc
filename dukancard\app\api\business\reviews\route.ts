import { createClient } from '@/utils/supabase/server';
import { createAdminClient } from '@/utils/supabase/admin';
import { NextRequest, NextResponse } from 'next/server';





export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = 8; // Optimized for 2-column grid (1x8, 2x4)
    const sortBy = searchParams.get('sort') || 'newest';
    const businessProfileId = searchParams.get('businessProfileId');

    if (!businessProfileId) {
      return NextResponse.json(
        { error: 'Business profile ID is required' },
        { status: 400 }
      );
    }

    // Verify the user has a business profile
    const { data: businessProfile, error: profileError } = await supabase
      .from('business_profiles')
      .select('id')
      .eq('id', user.id)
      .single();

    if (profileError || !businessProfile) {
      return NextResponse.json(
        { error: 'You do not have access to this business profile' },
        { status: 403 }
      );
    }

    // Verify the requested business profile exists
    const { data: requestedProfile, error: requestedProfileError } = await supabase
      .from('business_profiles')
      .select('id')
      .eq('id', businessProfileId)
      .single();

    if (requestedProfileError || !requestedProfile) {
      return NextResponse.json(
        { error: 'Business profile not found' },
        { status: 404 }
      );
    }

    // Use admin client to bypass RLS for fetching reviews
    const adminClient = createAdminClient();

    // Calculate pagination
    const from = (page - 1) * perPage;

    // Simple query for all sorting options (no name sorting)
    let baseQuery = adminClient
      .from('ratings_reviews')
      .select(`
        id,
        rating,
        review_text,
        created_at,
        updated_at,
        business_profile_id,
        user_id
      `, { count: 'exact' })
      .eq('business_profile_id', businessProfileId)
      .neq('user_id', businessProfileId);

    // Apply sorting
    switch (sortBy) {
      case "oldest":
        baseQuery = baseQuery.order("created_at", { ascending: true });
        break;
      case "highest_rating":
        baseQuery = baseQuery.order("rating", { ascending: false });
        break;
      case "lowest_rating":
        baseQuery = baseQuery.order("rating", { ascending: true });
        break;
      case "newest":
      default:
        baseQuery = baseQuery.order("created_at", { ascending: false });
        break;
    }


    // Get count first
    const { count: totalCount, error: countError } = await baseQuery;
    if (countError) {
      console.error('Error counting reviews:', countError);
      return NextResponse.json(
        { error: 'Failed to count reviews' },
        { status: 500 }
      );
    }

    // Get paginated data
    const { data: reviews, error: reviewsError } = await baseQuery.range(from, from + perPage - 1);
    if (reviewsError) {
      console.error('Error fetching reviews:', reviewsError);
      return NextResponse.json(
        { error: 'Failed to fetch reviews' },
        { status: 500 }
      );
    }

    let processedReviews: Array<{
      id: string;
      rating: number;
      review_text: string | null;
      created_at: string;
      updated_at: string | null;
      business_profile_id: string;
      user_id: string;
      reviewer_type: 'business' | 'customer';
      reviewer_name: string;
      reviewer_avatar: string | null;
      reviewer_slug: string | null;
    }> = [];

    if (reviews && reviews.length > 0) {
      // Get user IDs for profile lookup
      const userIds = reviews.map(review => review.user_id);

      // Fetch both customer and business profiles
      const [businessProfiles, customerProfiles] = await Promise.all([
        adminClient
          .from('business_profiles')
          .select('id, business_name, business_slug, logo_url')
          .in('id', userIds),
        adminClient
          .from('customer_profiles')
          .select('id, name, avatar_url')
          .in('id', userIds)
      ]);

      // Define interfaces for profile data
      interface BusinessProfileForReview {
        id: string;
        business_name: string | null;
        business_slug: string | null;
        logo_url: string | null;
      }

      interface CustomerProfileForReview {
        id: string;
        name: string | null;
        avatar_url: string | null;
      }

      // Create profile maps
      const businessProfilesMap = (businessProfiles.data || []).reduce((map, profile) => {
        map[profile.id] = profile;
        return map;
      }, {} as Record<string, BusinessProfileForReview>);

      const customerProfilesMap = (customerProfiles.data || []).reduce((map, profile) => {
        map[profile.id] = profile;
        return map;
      }, {} as Record<string, CustomerProfileForReview>);

      // Process reviews with profile information
      processedReviews = reviews.map(review => {
        const userId = review.user_id;
        const isBusiness = !!businessProfilesMap[userId];

        if (isBusiness) {
          const businessProfile = businessProfilesMap[userId];
          return {
            ...review,
            reviewer_type: 'business' as const,
            reviewer_name: businessProfile?.business_name || 'Business User',
            reviewer_avatar: businessProfile?.logo_url,
            reviewer_slug: businessProfile?.business_slug
          };
        } else {
          const customerProfile = customerProfilesMap[userId];
          return {
            ...review,
            reviewer_type: 'customer' as const,
            reviewer_name: customerProfile?.name || 'Customer',
            reviewer_avatar: customerProfile?.avatar_url,
            reviewer_slug: null
          };
        }
      });


    }

    const totalPages = Math.ceil((totalCount || 0) / perPage);

    return NextResponse.json({
      reviews: processedReviews,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        perPage
      }
    });
  } catch (_error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
