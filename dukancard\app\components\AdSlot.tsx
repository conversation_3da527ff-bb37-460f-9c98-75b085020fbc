"use client";

import React from "react";

interface AdSlotProps {
  adKey: string; // Unique key for the ad slot
  className?: string; // Optional additional classes
}

const AdSlot: React.FC<AdSlotProps> = ({ adKey, className }) => {
  // Only show placeholder for ad slots
  // This component is used when no custom ads are available
  return (
    <div
      className={`flex items-center justify-center w-full h-full bg-neutral-200 dark:bg-neutral-700 border border-dashed border-neutral-400 dark:border-neutral-600 rounded-lg text-neutral-500 dark:text-neutral-400 text-sm p-4 ${className}`}
      data-ad-key={adKey}
    >
      <div className="text-center">
        <p className="font-medium">Advertisement</p>
        <p className="text-xs">(Slot: {adKey})</p>
      </div>
    </div>
  );
};

export default AdSlot;
