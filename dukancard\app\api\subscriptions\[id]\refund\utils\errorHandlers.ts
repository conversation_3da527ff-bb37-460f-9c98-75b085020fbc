import { NextResponse } from "next/server";
import { RequestDetails } from "./types";

/**
 * Handles errors from the Razorpay API
 */
export function handleRazorpayError(
  errorData: {
    error?: {
      code?: string;
      description?: string;
      [key: string]: unknown;
    };
    [key: string]: unknown;
  },
  requestSent?: RequestDetails
) {
  // Check for specific error codes and provide more helpful messages
  let errorMessage = `Failed to process refund: ${
    errorData.error?.description || "Unknown error"
  }`;
  let statusCode = 500;

  // Handle specific error cases
  if (errorData.error?.code === "BAD_REQUEST_ERROR") {
    // This could be due to invalid amount format or other request issues
    errorMessage = `Invalid refund request: ${
      errorData.error?.description || "Unknown error"
    }. Please ensure the amount is valid and in the correct format (paise for INR).`;
    statusCode = 400;
  } else if (
    errorData.error?.description?.includes("payment has been fully refunded")
  ) {
    // Payment already refunded
    errorMessage = "This payment has already been fully refunded.";
    statusCode = 400;
  } else if (
    errorData.error?.description?.includes(
      "refund amount provided is greater than amount captured"
    )
  ) {
    // Refund amount exceeds payment amount
    errorMessage =
      "The refund amount is greater than the original payment amount.";
    statusCode = 400;
  }

  return NextResponse.json(
    {
      success: false,
      error: errorMessage,
      details: errorData,
      requestSent,
    },
    { status: statusCode }
  );
}

/**
 * Handles unexpected errors
 */
export function handleUnexpectedError(error: unknown) {
  console.error("Unexpected error in refund API:", error);

  const errorMessage =
    error instanceof Error ? error.message : "An unexpected error occurred";

  return NextResponse.json(
    { success: false, error: errorMessage },
    { status: 500 }
  );
}
