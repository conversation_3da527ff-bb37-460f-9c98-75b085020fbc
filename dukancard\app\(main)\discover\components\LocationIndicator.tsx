"use client";

import { motion } from "framer-motion";
import { MapPin, Globe, Building2 } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { PINCODE_PARAM, CITY_PARAM, LOCALITY_PARAM } from "../constants/urlParamConstants";
import { useDiscoverContext } from "../context/DiscoverContext";

export default function LocationIndicator() {
  const searchParams = useSearchParams();
  const { searchResult, viewType } = useDiscoverContext();

  const pincode = searchParams.get(PINCODE_PARAM);
  const city = searchParams.get(CITY_PARAM);
  const locality = searchParams.get(LOCALITY_PARAM);

  // Determine the location text and icon based on search parameters
  let locationText = "All over India";
  let LocationIcon = Globe;
  let detailText = "Showing businesses and products from across the country";
  let highlightText = "";

  if (pincode) {
    locationText = `Pincode: ${pincode}`;
    LocationIcon = MapPin;

    // Add locality if available
    if (locality && locality !== "_any") {
      highlightText = locality;
      locationText += `, ${locality}`;
    }

    // Add city and state if available from search result
    if (searchResult?.location?.city) {
      detailText = `${searchResult.location.city}, ${searchResult.location.state}`;
      if (!highlightText) {
        highlightText = searchResult.location.city;
      }
    } else {
      detailText = "Showing nearby businesses and products";
    }
  } else if (city) {
    locationText = `${city}`;
    LocationIcon = Building2;
    highlightText = city;
    detailText = "Showing businesses and products in this city";
  }

  // Customize detail text based on view type
  if (viewType === "cards") {
    detailText = detailText.replace("businesses and products", "businesses");
  } else if (viewType === "products") {
    detailText = detailText.replace("businesses and products", "products/services");
  }

  return (
    <motion.div
      className="container mx-auto px-4 mb-6"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="bg-white/80 dark:bg-neutral-900/80 backdrop-blur-md rounded-xl border border-neutral-200/50 dark:border-neutral-800/50 p-4 flex items-center justify-center shadow-sm relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-50">
          <div className="absolute -top-12 -right-12 w-24 h-24 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/5 to-amber-500/5 blur-xl dark:from-[var(--brand-gold)]/10 dark:to-amber-500/10"></div>
          <div className="absolute -bottom-12 -left-12 w-24 h-24 rounded-full bg-gradient-to-tr from-purple-500/5 to-blue-500/5 blur-xl dark:from-purple-500/10 dark:to-blue-500/10"></div>
        </div>

        <div className="flex flex-col items-center text-center relative z-10">
          <div className="flex items-center gap-2 mb-1">
            <div className="relative">
              <div className="absolute inset-0 bg-[var(--brand-gold)]/20 rounded-full blur-sm"></div>
              <LocationIcon className="h-5 w-5 text-[var(--brand-gold)] relative z-10" />
            </div>
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-200">
              {locationText}
            </h3>
          </div>
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            {highlightText ?
              detailText.split(highlightText).map((part, i, arr) =>
                i === arr.length - 1 ? (
                  <span key={i}>{part}</span>
                ) : (
                  <span key={i}>
                    {part}
                    <span className="font-medium text-[var(--brand-gold)]">{highlightText}</span>
                  </span>
                )
              )
              : detailText
            }
          </p>
        </div>
      </div>
    </motion.div>
  );
}
