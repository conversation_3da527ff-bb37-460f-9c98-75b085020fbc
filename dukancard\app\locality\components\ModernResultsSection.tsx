"use client";

import { useRef, useCallback } from "react";
import { Loader2 } from "lucide-react";
import { useLocalityContext } from "../context/LocalityContext";
import BusinessResultsGrid from "@/app/(main)/discover/components/BusinessResultsGrid";
import ProductResultsGrid from "@/app/(main)/discover/components/ProductResultsGrid";
import BusinessSortControls from "@/app/(main)/discover/components/BusinessSortControls";
import ProductSortControls from "@/app/(main)/discover/components/ProductSortControls";
import NoResultsMessage from "@/app/(main)/discover/components/NoResultsMessage";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

export default function ModernResultsSection() {
  const {
    viewType,
    businesses,
    products,
    isSearching,
    isLoadingMore,
    hasMore,
    totalCount,
    loadMore,
  } = useLocalityContext();

  const observer = useRef<IntersectionObserver | null>(null);
  const lastItemRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (isSearching || isLoadingMore) return;
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          loadMore();
        }
      });

      if (node) observer.current.observe(node);
    },
    [isSearching, isLoadingMore, hasMore, loadMore]
  );

  // Determine if we have results to show
  const hasResults =
    (viewType === "cards" && businesses.length > 0) ||
    (viewType === "products" && products.length > 0);

  // Determine if we're showing the initial loading state
  const isInitialLoading = isSearching && totalCount === 0;

  return (
    <div className="container mx-auto px-4 pb-20">
      {/* Sort controls */}
      <div className="mb-6">
        {viewType === "cards" ? <BusinessSortControls /> : <ProductSortControls />}
      </div>

      {/* Results */}
      {isInitialLoading ? (
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-lg">Loading...</span>
        </div>
      ) : !hasResults ? (
        <NoResultsMessage viewType={viewType} />
      ) : (
        <>
          {viewType === "cards" ? (
            <BusinessResultsGrid businesses={businesses as BusinessCardData[]} lastItemRef={lastItemRef} />
          ) : (
            <ProductResultsGrid products={products} lastItemRef={lastItemRef} />
          )}

          {/* Loading more indicator */}
          {isLoadingMore && (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
              <span className="ml-2">Loading more...</span>
            </div>
          )}
        </>
      )}
    </div>
  );
}
