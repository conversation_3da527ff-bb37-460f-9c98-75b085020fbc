"use server";

import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { createClient } from "@/utils/supabase/server";
import { LocalitySearchResult } from "../context/types";
import { fetchBusinessesByLocalityAndLocation } from "./businessActions";
import { fetchProductsByLocality } from "./productActions";

// Action to find businesses or products based on locality and view type
export async function searchLocalityData(params: {
  localityName: string;
  pincode: string;
  viewType: "cards" | "products";
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
  productType?: "physical" | "service" | null;
  businessName?: string;
  productName?: string;
}): Promise<{
  data?: LocalitySearchResult;
  error?: string;
}> {
  const {
    localityName,
    pincode,
    viewType,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
    productName,
  } = params;

  // Check if locality name and pincode are provided
  if (!localityName || !pincode) {
    return { error: "Locality name and pincode are required." };
  }

  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { session }, } = await supabase.auth.getSession();
    const isAuthenticated = !!session;

    // Define location object for the result
    const location = {
      type: "pincode" as const,
      value: pincode,
    };

    if (viewType === "cards") {
      // Fetch businesses by locality
      const result = await fetchBusinessesByLocalityAndLocation({
        localityName,
        pincode,
        page,
        limit,
        sortBy,
      });

      if (result.error) {
        return { error: result.error };
      }

      return {
        data: {
          location,
          businesses: result.data?.businesses || [],
          isAuthenticated,
          totalCount: result.data?.totalCount || 0,
          hasMore: result.data?.hasMore || false,
          nextPage: result.data?.nextPage || null,
        },
      };
    } else {
      // viewType === "products"
      // Fetch products by locality
      const result = await fetchProductsByLocality({
        localityName,
        pincode,
        productName,
        page,
        limit,
        sortBy,
        productType,
      });

      if (result.error) {
        return { error: result.error };
      }

      return {
        data: {
          location,
          products: result.data?.products || [],
          isAuthenticated,
          totalCount: result.data?.totalCount || 0,
          hasMore: result.data?.hasMore || false,
          nextPage: result.data?.nextPage || null,
        },
      };
    }
  } catch (error) {
    console.error("Error in searchLocalityData:", error);
    return { error: "An unexpected error occurred. Please try again." };
  }
}
