import AnalyticsClient from "./AnalyticsClient";
import { Metadata } from "next";
import { siteConfig } from "@/lib/site-config";

export async function generateMetadata(): Promise<Metadata> {
  const title = `Analytics & Insights`;
  const description =
    "Learn how to track and analyze visitor engagement with your Dukancard digital business card. Understand your analytics dashboard, visitor metrics, and how to use data to improve your digital presence.";
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || siteConfig.url;
  const pageUrl = `${siteUrl}/support/analytics`;
  const ogImage = `${siteUrl}/opengraph-image.png`;

  return {
    title,
    description,
    keywords: [
      "Dukancard analytics",
      "business card metrics",
      "visitor tracking",
      "engagement analytics",
      "digital card statistics",
      "customer insights",
    ],
    alternates: {
      canonical: "/support/analytics",
    },
    openGraph: {
      title,
      description,
      url: pageUrl,
      siteName: siteConfig.name,
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: `${siteConfig.name} Analytics & Insights Guide`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [ogImage],
    },
    // Add WebPage Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: siteConfig.name,
          url: siteUrl,
        },
      }),
    },
  };
}

export default function AnalyticsPage() {
  return <AnalyticsClient />;
}
