"use client";

import React from "react";
import { motion } from "framer-motion";
import { Settings } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface ProductItemsPerPageSelectorProps {
  itemsPerPage: number;
  onItemsPerPageChange: (_itemsPerPage: number) => void;
  isLoading?: boolean;
  className?: string;
}

export default function ProductItemsPerPageSelector({
  itemsPerPage: _itemsPerPage,
  onItemsPerPageChange,
  isLoading = false,
  className,
}: ProductItemsPerPageSelectorProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className={cn("flex items-center gap-3", className)}
    >
      <Label
        htmlFor="items-per-page"
        className="text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2 whitespace-nowrap"
      >
        <Settings className="h-4 w-4 text-primary" />
        Items per page
      </Label>
      
      <Select
        value={_itemsPerPage.toString()}
        onValueChange={(value) => onItemsPerPageChange(parseInt(value))}
        disabled={isLoading}
      >
        <SelectTrigger
          id="items-per-page"
          className="w-24 h-9 text-sm border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
        >
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg bg-white dark:bg-neutral-900">
          <SelectItem 
            value="10" 
            className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer"
          >
            10
          </SelectItem>
          <SelectItem 
            value="25" 
            className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer"
          >
            25
          </SelectItem>
          <SelectItem 
            value="50" 
            className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer"
          >
            50
          </SelectItem>
          <SelectItem 
            value="100" 
            className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer"
          >
            100
          </SelectItem>
        </SelectContent>
      </Select>
    </motion.div>
  );
}
