"use client";

import { motion } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface ReviewCardSkeletonProps {
  index?: number;
}

export default function ReviewCardSkeleton({ index = 0 }: ReviewCardSkeletonProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.05 }}
      className={cn(
        "rounded-xl border border-neutral-200 dark:border-neutral-800",
        "bg-white dark:bg-black",
        "shadow-sm p-4 transition-all duration-300",
        "relative overflow-hidden"
      )}
    >
      {/* Card background with subtle pattern */}
      <div
        className="absolute inset-0 pointer-events-none opacity-5 dark:opacity-10"
        style={{
          backgroundImage: `url("/decorative/card-texture.svg")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      ></div>

      <div className="relative z-10">
        {/* Business info and rating skeleton */}
        <div className="flex items-start mb-3">
          <div className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div>
              <Skeleton className="h-5 w-32 mb-2" />
              {/* Rating stars */}
              <div className="flex items-center gap-1 mt-1">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Skeleton key={i} className="h-4 w-4" />
                ))}
              </div>
              {/* Date below ratings */}
              <Skeleton className="h-3 w-24 mt-1" />
            </div>
          </div>
        </div>

        {/* Review text skeleton */}
        <div className="mb-4">
          <Skeleton className="h-20 w-full rounded-lg" />
        </div>

        {/* Action buttons skeleton */}
        <div className="flex items-center justify-between mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50">
          <Skeleton className="h-8 w-28" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>
      </div>
    </motion.div>
  );
}
