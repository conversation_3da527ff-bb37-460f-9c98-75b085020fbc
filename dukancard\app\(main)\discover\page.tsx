import { Suspense } from "react";
import ModernDiscoverClient from "./ModernDiscoverClient";
import { Metadata } from "next";
import ModernResultsSkeleton from "./ModernResultsSkeleton";
import {
  BUSINESS_NAME_PARAM,
  PINCODE_PARAM,
} from "./constants/urlParamConstants";

// SEO Metadata
export async function generateMetadata(): Promise<Metadata> {
  const title = "Discover Local Businesses & Products";
  const description =
    "Find and explore local businesses and products using Dukancard. Search by pincode, locality, or business name to discover shops, services, products, and professionals near you in India.";
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/discover`;
  const ogImage = `${siteUrl}/opengraph-image.png`; // Default OG image

  return {
    title, // Uses template: "Discover Local Businesses - Dukancard"
    description,
    keywords: [
      "discover local business",
      "find shops near me",
      "search business by pincode",
      "search business by name",
      "local business directory India",
      "Dukancard discover",
      "nearby services",
      "local products",
      "search by locality",
      "business cards",
      "products and services",
      "infinite scroll",
    ],
    alternates: {
      canonical: "/discover", // Relative canonical path
    },
    openGraph: {
      title: title,
      description: description,
      url: pageUrl,
      siteName: "Dukancard",
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "Discover Local Businesses on Dukancard",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: title,
      description: description,
      images: [ogImage],
    },
    // Add WebPage Schema with SearchAction
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: "Dukancard",
          url: siteUrl,
        },
        // Add SearchAction for structured data
        potentialAction: [
          {
            "@type": "SearchAction",
            target: {
              "@type": "EntryPoint",
              urlTemplate: `${siteUrl}/discover?${PINCODE_PARAM}={pincode}`,
            },
            "query-input": "required name=pincode",
            description: "Search for businesses and products by pincode",
          },
          {
            "@type": "SearchAction",
            target: {
              "@type": "EntryPoint",
              urlTemplate: `${siteUrl}/discover?${BUSINESS_NAME_PARAM}={businessName}`,
            },
            "query-input": "required name=businessName",
            description: "Search for businesses by name",
          },
        ],
        // Add breadcrumb for better navigation structure
        breadcrumb: {
          "@type": "BreadcrumbList",
          itemListElement: [
            {
              "@type": "ListItem",
              position: 1,
              name: "Home",
              item: siteUrl,
            },
            {
              "@type": "ListItem",
              position: 2,
              name: "Discover",
              item: pageUrl,
            },
          ],
        },
      }),
    },
  };
}

// Server Component - Handles initial rendering
async function DiscoverPageContent() {
  return <ModernDiscoverClient />;
}

// Main Page Component using Suspense for streaming
export default function DiscoverPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense fallback={<ModernResultsSkeleton />}>
        <DiscoverPageContent />
      </Suspense>
    </div>
  );
}
