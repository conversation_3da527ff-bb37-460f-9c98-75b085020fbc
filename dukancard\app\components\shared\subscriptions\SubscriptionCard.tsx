'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User<PERSON>inus, Loader2, ExternalLink, User, Building2 } from 'lucide-react';
import { unsubscribeFromBusiness } from '@/lib/actions/interactions';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Shared types for subscription data
export interface ProfileData {
  id: string;
  name: string | null;
  slug: string | null;
  logo_url?: string | null;
  avatar_url?: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
  type: 'business' | 'customer';
}

export interface SubscriptionData {
  id: string;
  profile: ProfileData | null;
}

interface SubscriptionCardProps {
  subscriptionId: string;
  profile: ProfileData;
  onUnsubscribeSuccess?: (_subscriptionId: string) => void;
  showUnsubscribe?: boolean;
  variant?: 'default' | 'compact';
}

export default function SubscriptionCard({
  subscriptionId,
  profile,
  onUnsubscribeSuccess,
  showUnsubscribe = true,
  variant = 'default',
}: SubscriptionCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleUnsubscribe = async () => {
    if (!onUnsubscribeSuccess) return;

    setIsLoading(true);
    try {
      if (!profile.id) {
        toast.error("Cannot unsubscribe: Missing profile ID");
        return;
      }

      const result = await unsubscribeFromBusiness(profile.id);

      if (result.success) {
        toast.success(`Unsubscribed from ${profile.name || 'profile'}.`);
        onUnsubscribeSuccess(subscriptionId);
      } else {
        toast.error(`Failed to unsubscribe: ${result.error || 'Unknown error'}`);
      }
    } catch (_error) {
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Format the full address
  const addressParts = [
    profile.address_line,
    profile.city,
    profile.state,
    profile.pincode ? `PIN: ${profile.pincode}` : null
  ].filter(Boolean);

  const location = addressParts.join(', ');
  const profileUrl = profile.type === 'business' ? `/${profile.slug}` : `/profile/${profile.slug}`;
  const imageUrl = profile.logo_url || profile.avatar_url;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "rounded-lg border p-0 overflow-hidden transition-all duration-300",
        "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",
        isHovered ? "shadow-md transform -translate-y-1" : "shadow-sm",
        variant === 'compact' && "max-w-sm"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Card with header image background */}
      <div className="relative">
        {/* Decorative header */}
        <div
          className={cn(
            "w-full bg-gradient-to-r from-blue-500/20 to-[var(--brand-gold)]/20 dark:from-blue-900/30 dark:to-[var(--brand-gold)]/30",
            variant === 'compact' ? "h-16" : "h-20"
          )}
        >
          {/* Decorative pattern overlay */}
          <div
            className="absolute inset-0 opacity-10 dark:opacity-20 bg-repeat"
            style={{
              backgroundImage: `url("/decorative/card-texture.svg")`,
              backgroundSize: "200px",
            }}
          />
        </div>

        {/* Avatar - positioned to overlap the header */}
        <div className={cn(
          "absolute left-4",
          variant === 'compact' ? "-bottom-4" : "-bottom-6"
        )}>
          <div className="p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800">
            <Avatar className={cn(
              "border border-neutral-200 dark:border-neutral-700 shadow-sm",
              variant === 'compact' ? "h-12 w-12" : "h-16 w-16"
            )}>
              {imageUrl ? (
                <AvatarImage src={imageUrl} alt={profile.name ?? 'Profile'} />
              ) : null}
              <AvatarFallback className={cn(
                "bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 text-blue-600 dark:text-blue-300 font-semibold",
                variant === 'compact' ? "text-lg" : "text-xl"
              )}>
                {profile.type === 'customer' ? (
                  <User className={variant === 'compact' ? "h-4 w-4" : "h-6 w-6"} />
                ) : (
                  profile.name?.charAt(0).toUpperCase() ?? 'P'
                )}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>

      {/* Card content */}
      <div className={cn(
        "px-4 pb-4",
        variant === 'compact' ? "pt-6" : "pt-8"
      )}>
        <div className="flex flex-col">
          <div className="mb-3">
            <h3 className={cn(
              "font-semibold text-neutral-800 dark:text-neutral-200 group flex items-center gap-1",
              variant === 'compact' ? "text-base" : "text-lg"
            )}>
              {profile.slug ? (
                <Link
                  href={profileUrl}
                  className="hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1"
                  target="_blank"
                >
                  {profile.name}
                  <ExternalLink className="h-3.5 w-3.5 opacity-70" />
                </Link>
              ) : (
                <span>{profile.name}</span>
              )}
            </h3>
            {location && (
              <p className={cn(
                "text-neutral-500 dark:text-neutral-400 mt-1 flex items-center",
                variant === 'compact' ? "text-xs" : "text-sm"
              )}>
                <span className="inline-block h-1 w-1 rounded-full bg-neutral-300 dark:bg-neutral-700 mr-2"></span>
                {location}
              </p>
            )}
            {profile.type === 'customer' && (
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1 flex items-center">
                <User className="h-3 w-3 mr-1" />
                Customer
              </p>
            )}
            {profile.type === 'business' && (
              <p className="text-xs text-green-600 dark:text-green-400 mt-1 flex items-center">
                <Building2 className="h-3 w-3 mr-1" />
                Business
              </p>
            )}
          </div>

          {/* Action button */}
          {showUnsubscribe && onUnsubscribeSuccess && (
            <Button
              variant="outline"
              size={variant === 'compact' ? "sm" : "sm"}
              onClick={handleUnsubscribe}
              disabled={isLoading}
              className={cn(
                "mt-2 w-full border-neutral-200 dark:border-neutral-700 transition-all duration-200",
                "hover:bg-red-50 hover:text-red-600 hover:border-red-200",
                "dark:hover:bg-red-950/30 dark:hover:text-red-400 dark:hover:border-red-900/50"
              )}
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <UserMinus className="mr-2 h-4 w-4" />
              )}
              Unsubscribe
            </Button>
          )}
        </div>
      </div>
    </motion.div>
  );
}
