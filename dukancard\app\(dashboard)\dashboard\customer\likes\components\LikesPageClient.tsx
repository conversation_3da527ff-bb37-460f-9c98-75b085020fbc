"use client";

import { useState, useEffect, useCallback } from "react";
import { Heart } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import LikeListClient from "../LikeListClient";
import { LikeSearch, LikeListSkeleton } from "@/app/components/shared/likes";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { formatIndianNumberShort } from "@/lib/utils";
import { LikeWithProfile } from "../actions";

interface LikesPageClientProps {
  initialLikes: LikeWithProfile[];
  totalCount: number;
  currentPage: number;
  searchTerm: string;
}

export default function LikesPageClient({
  initialLikes,
  totalCount,
  currentPage,
  searchTerm: initialSearchTerm
}: LikesPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);

  // Calculate total pages
  const itemsPerPage = 12; // Optimized for 3-column grid
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // Handle search
  const handleSearch = useCallback((newSearchTerm: string) => {
    setIsLoading(true);
    setSearchTerm(newSearchTerm);

    const params = new URLSearchParams(searchParams);
    if (newSearchTerm) {
      params.set('search', newSearchTerm);
    } else {
      params.delete('search');
    }
    params.delete('page'); // Reset to first page when searching

    router.push(`/dashboard/customer/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setIsLoading(true);

    const params = new URLSearchParams(searchParams);
    if (page > 1) {
      params.set('page', page.toString());
    } else {
      params.delete('page');
    }

    router.push(`/dashboard/customer/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Reset loading state when data changes
  useEffect(() => {
    setIsLoading(false);
  }, [initialLikes]);

  return (
    <div className="space-y-6">
      {/* Header Section with proper layout */}
      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-xl bg-muted hidden sm:block">
              <Heart className="w-6 h-6 text-rose-600 dark:text-rose-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-foreground">
                Liked Businesses
              </h1>
              <p className="text-muted-foreground mt-1">
                {formatIndianNumberShort(totalCount)} {totalCount === 1 ? 'business' : 'businesses'} you&apos;ve liked
              </p>
            </div>
          </div>

          {/* Search Section - aligned to the right on desktop */}
          <div className="w-full sm:w-80">
            <LikeSearch
              onSearch={handleSearch}
              initialSearchTerm={searchTerm}
              placeholder="Search businesses by name..."
            />
          </div>
        </div>

        {/* Results count */}
        {searchTerm && !isLoading && (
          <div className="text-sm text-muted-foreground border-l-4 border-primary pl-4">
            Found {formatIndianNumberShort(totalCount)} {totalCount === 1 ? 'business' : 'businesses'}
            {searchTerm ? ` matching "${searchTerm}"` : ''}
          </div>
        )}
      </div>

      {/* Content Section */}
      <div className="space-y-6">
        {/* Show skeleton loader when loading */}
        {isLoading ? (
          <LikeListSkeleton />
        ) : (
          <>
            {/* Like List */}
            <LikeListClient initialLikes={initialLikes} />

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center pt-6">
                <Pagination>
                  <PaginationContent>
                    {currentPage > 1 && (
                      <PaginationItem>
                        <PaginationPrevious
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(currentPage - 1);
                          }}
                        />
                      </PaginationItem>
                    )}

                    {/* Generate page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(pageNum);
                            }}
                            isActive={currentPage === pageNum}
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    {currentPage < totalPages && (
                      <PaginationItem>
                        <PaginationNext
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(currentPage + 1);
                          }}
                        />
                      </PaginationItem>
                    )}
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
