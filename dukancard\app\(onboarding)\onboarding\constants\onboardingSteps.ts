import { OnboardingFormData } from "../types/onboarding";

export const stepFields: (keyof OnboardingFormData)[][] = [
  ["businessName", "email"], // Step 1
  ["memberName", "title", "phone", "businessCategory", "businessSlug"], // Step 2
  ["addressLine", "pincode", "city", "state", "locality", "businessStatus"], // Step 3
  ["planId"], // Step 4
];

export const STEP_TITLES = [
  "Business Details", 
  "Card Information", 
  "Address & Status", 
  "Choose Your Plan"
];

export const STEP_DESCRIPTIONS = [
  "Let's set up your business profile",
  "Let's create your unique digital business card",
  "Complete your business address and status",
  "Select the perfect plan for your business"
];

export const getStepDescription = (stepIndex: number, hasExistingSubscription?: boolean): string => {
  if (stepIndex === 3 && hasExistingSubscription) {
    return "Review your current plan and complete setup";
  }
  return STEP_DESCRIPTIONS[stepIndex] || "";
};
