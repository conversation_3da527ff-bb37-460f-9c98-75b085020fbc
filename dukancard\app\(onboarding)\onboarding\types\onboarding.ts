import { z } from "zod";
import { UseFormReturn } from "react-hook-form";
import { IndianMobileSchema } from "@/lib/schemas/authSchemas";
import { PricingPlan } from "@/lib/PricingPlans";

// Define the full schema for all steps
export const formSchema = z.object({
  businessName: z.string().min(2, {
    message: "Business name must be at least 2 characters.",
  }),
  email: z.string().email({ message: "Please enter a valid email." }),
  memberName: z.string().min(2, { message: "Your name is required." }),
  title: z.string().min(2, { message: "Your title/designation is required." }),
  phone: IndianMobileSchema,
  businessCategory: z.string().min(1, { message: "Business category is required." }),
  businessSlug: z
    .string()
    .min(3, { message: "URL slug must be at least 3 characters." })
    .regex(/^[a-z0-9-]+$/, {
      message:
        "URL slug can only contain lowercase letters, numbers, and hyphens.",
    }),
  // Address fields
  addressLine: z.string().min(1, { message: "Address line is required." }),
  pincode: z.string()
    .min(6, { message: "Pincode must be 6 digits." })
    .max(6, { message: "Pincode must be 6 digits." })
    .regex(/^\d+$/, { message: "Pincode must contain only digits." }),
  city: z.string().min(1, { message: "City is required." }),
  state: z.string().min(1, { message: "State is required." }),
  locality: z.string().min(1, { message: "Locality/area is required." }),
  // Business status
  businessStatus: z.enum(["online", "offline"]).default("online"),
  planId: z.string().min(1, { message: "Please select a plan." }),
});

export type OnboardingFormData = z.infer<typeof formSchema>;

export type User = {
  id: string;
  email?: string;
  phone?: string;
  app_metadata?: {
    provider?: string;
    [key: string]: unknown;
  };
  user_metadata?: {
    display_name?: string;
    full_name?: string;
    name?: string;
    [key: string]: unknown;
  };
};

export interface OnboardingClientProps {
  redirectSlug?: string | null;
  message?: string | null;
}

export interface OnboardingState {
  currentStep: number;
  showPlans: boolean;
  selectedPlan: PricingPlan | null;
  user: User | null;
  slugAvailable: boolean | null;
  slugToCheck: string;
  cardRedirect: string | null;
  messageParam: string | null;
  isSubmitIntended: boolean;
  isLoadingExistingData: boolean;
  existingData: ExistingBusinessProfileData | null;
}

export interface ExistingBusinessProfileData {
  businessName?: string;
  email?: string;
  memberName?: string;
  title?: string;
  phone?: string;
  businessCategory?: string;
  businessSlug?: string;
  addressLine?: string;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
  businessStatus?: "online" | "offline";
  planId?: string;
  hasExistingSubscription?: boolean;
}

export interface StepComponentProps {
  form: UseFormReturn<OnboardingFormData>;
  isSubmitting: boolean;
  user: User | null;
  existingData: ExistingBusinessProfileData | null;
  slugAvailable?: boolean | null;
  isCheckingSlug?: boolean;
  slugToCheck?: string;
  setSlugToCheck?: (_slug: string) => void;
  setSlugAvailable?: (_available: boolean | null) => void;
  selectedPlan?: PricingPlan | null;
  setSelectedPlan?: (_plan: PricingPlan | null) => void;
  showPlans?: boolean;
  setShowPlans?: (_show: boolean) => void;
  availableLocalities?: string[];
  isPincodeLoading?: boolean;
  handlePincodeChange?: (_pincode: string) => void;
}
