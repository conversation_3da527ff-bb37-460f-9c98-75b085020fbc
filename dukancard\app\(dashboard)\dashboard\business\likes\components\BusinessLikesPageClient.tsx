"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Heart, MessageSquare, Star } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";

import { cn, formatIndianNumberShort } from "@/lib/utils";
import { LikeSearch, LikePagination, LikeListSkeleton } from "@/app/components/shared/likes";
import BusinessLikesReceivedList from "./BusinessLikesReceivedList";
import BusinessMyLikesList from "./BusinessMyLikesList";
import { BusinessLikeReceived, BusinessMyLike } from "../actions";

interface BusinessLikesPageClientProps {
  initialLikesReceived: BusinessLikeReceived[];
  likesReceivedCount: number;
  likesReceivedCurrentPage: number;
  initialMyLikes: BusinessMyLike[];
  myLikesCount: number;
  myLikesCurrentPage: number;
  searchTerm: string;
  activeTab: string;
}

export default function BusinessLikesPageClient({
  initialLikesReceived,
  likesReceivedCount,
  likesReceivedCurrentPage,
  initialMyLikes,
  myLikesCount,
  myLikesCurrentPage,
  searchTerm: initialSearchTerm,
  activeTab: initialActiveTab
}: BusinessLikesPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [activeTab, setActiveTab] = useState(initialActiveTab);

  // Calculate current data based on active tab
  const _currentData = activeTab === 'my-likes' ? initialMyLikes : initialLikesReceived;
  const currentCount = activeTab === 'my-likes' ? myLikesCount : likesReceivedCount;
  const currentPage = activeTab === 'my-likes' ? myLikesCurrentPage : likesReceivedCurrentPage;
  const totalPages = Math.ceil(currentCount / 10);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  // Handle tab change
  const handleTabChange = useCallback((newTab: string) => {
    setIsLoading(true);
    setActiveTab(newTab);

    const params = new URLSearchParams(searchParams);
    if (newTab === 'my-likes') {
      params.set('tab', 'my-likes');
    } else {
      params.delete('tab');
    }
    params.delete('page'); // Reset to first page when changing tabs
    params.delete('search'); // Clear search when changing tabs
    setSearchTerm('');

    router.push(`/dashboard/business/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Handle search
  const handleSearch = useCallback((newSearchTerm: string) => {
    setIsLoading(true);
    setSearchTerm(newSearchTerm);

    const params = new URLSearchParams(searchParams);
    if (newSearchTerm) {
      params.set('search', newSearchTerm);
    } else {
      params.delete('search');
    }
    params.delete('page'); // Reset to first page when searching

    router.push(`/dashboard/business/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setIsLoading(true);

    const params = new URLSearchParams(searchParams);
    if (page > 1) {
      params.set('page', page.toString());
    } else {
      params.delete('page');
    }

    router.push(`/dashboard/business/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Reset loading state when data changes
  useEffect(() => {
    setIsLoading(false);
  }, [initialLikesReceived, initialMyLikes]);

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Likes Header - Full Width */}
      <motion.div variants={itemVariants}>
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
          <div className="p-3 rounded-xl bg-muted hidden sm:block">
            <Heart className="w-6 h-6 text-foreground" />
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-foreground">
              Business Likes
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage your likes and see who liked your business
            </p>
          </div>
        </div>
      </motion.div>

      {/* Tabs Section - Full Width */}
      <motion.div variants={itemVariants}>
        <div className="flex gap-2 border-b border-border">
          <Button
            variant={activeTab === 'likes-received' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => handleTabChange('likes-received')}
            className={cn(
              "rounded-b-none border-b-2 border-transparent",
              activeTab === 'likes-received'
                ? "border-b-[var(--brand-gold)] bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]"
                : ""
            )}
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            Likes Received ({formatIndianNumberShort(likesReceivedCount)})
          </Button>
          <Button
            variant={activeTab === 'my-likes' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => handleTabChange('my-likes')}
            className={cn(
              "rounded-b-none border-b-2 border-transparent",
              activeTab === 'my-likes'
                ? "border-b-[var(--brand-gold)] bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]"
                : ""
            )}
          >
            <Star className="w-4 h-4 mr-2" />
            My Likes ({formatIndianNumberShort(myLikesCount)})
          </Button>
        </div>
      </motion.div>

      {/* Search Section - Full Width */}
      {activeTab === 'my-likes' && (
        <motion.div variants={itemVariants}>
          <LikeSearch
            onSearch={handleSearch}
            initialSearchTerm={searchTerm}
            placeholder="Search businesses by name..."
          />
        </motion.div>
      )}

      {/* Content Section - Full Width */}
      <motion.div variants={itemVariants}>
        {/* Count display - Only show for My Likes tab when searching */}
        {activeTab === 'my-likes' && searchTerm && !isLoading && (
          <div className="mb-6 text-sm text-muted-foreground">
            Found {formatIndianNumberShort(currentCount)} {currentCount === 1 ? 'result' : 'results'}
            {searchTerm ? ` matching "${searchTerm}"` : ''}
          </div>
        )}

        {/* Show skeleton loader when loading */}
        {isLoading ? (
          <LikeListSkeleton />
        ) : (
          <>
            {/* Tab Content */}
            {activeTab === 'likes-received' && (
              <BusinessLikesReceivedList initialLikes={initialLikesReceived} />
            )}
            {activeTab === 'my-likes' && (
              <BusinessMyLikesList initialLikes={initialMyLikes} />
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-8">
                <LikePagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </motion.div>
    </motion.div>
  );
}
