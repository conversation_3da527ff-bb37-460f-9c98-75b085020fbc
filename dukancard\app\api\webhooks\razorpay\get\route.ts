import { NextRequest, NextResponse } from "next/server";
import { fetchWebhook } from "@/lib/razorpay/services/webhook";

/**
 * API route to fetch a Razorpay webhook by ID
 * 
 * This endpoint retrieves details of a specific webhook for a Razorpay account.
 * 
 * Query parameters:
 * - account_id: The Razorpay account ID
 * - webhook_id: The webhook ID to fetch
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "id": "HK890egfiItP3H",
 *     "created_at": **********,
 *     "updated_at": **********,
 *     "owner_id": "H3kYHQ635sBwXG",
 *     "owner_type": "merchant",
 *     "context": [],
 *     "disabled_at": 0,
 *     "url": "https://en1mwkqo5ioct.x.pipedream.net",
 *     "alert_email": "<EMAIL>",
 *     "secret_exists": true,
 *     "entity": "webhook",
 *     "active": true,
 *     "events": [
 *       "payment.authorized",
 *       "payment.failed",
 *       "payment.captured",
 *       "payment.dispute.created",
 *       "refund.failed",
 *       "refund.created"
 *     ]
 *   }
 * }
 */
export async function GET(req: NextRequest) {
  try {
    // Get the query parameters
    const url = new URL(req.url);
    const accountId = url.searchParams.get("account_id");
    const webhookId = url.searchParams.get("webhook_id");

    // Validate required parameters
    if (!accountId) {
      return NextResponse.json(
        { success: false, message: "Missing account_id parameter" },
        { status: 400 }
      );
    }

    if (!webhookId) {
      return NextResponse.json(
        { success: false, message: "Missing webhook_id parameter" },
        { status: 400 }
      );
    }

    // Fetch the webhook
    const result = await fetchWebhook(accountId, webhookId);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return success response
    return NextResponse.json(
      { success: true, data: result.data },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK_GET] Error fetching webhook:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Error fetching webhook",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
