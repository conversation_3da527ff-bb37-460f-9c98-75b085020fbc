"use client";

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Camera, Loader2, X } from "lucide-react";
import { useAvatarUpload } from "../hooks/useAvatarUpload";
import ImageCropDialog from "@/app/(dashboard)/dashboard/business/card/components/ImageCropDialog";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface AvatarUploadProps {
  initialAvatarUrl?: string;
  userName?: string | null;
  onUpdateAvatar: (_url: string) => void;
}

export default function AvatarUpload({
  initialAvatarUrl,
  userName,
  onUpdateAvatar,
}: AvatarUploadProps) {
  const {
    localPreviewUrl,
    isAvatarUploading,
    imageToCrop,
    onFileSelect,
    handleCropComplete,
    handleCropDialogClose,
    handleAvatarDelete,
    avatarErrorDisplay,
  } = useAvatarUpload({
    initialAvatarUrl,
    onUpdateAvatar,
  });

  // Generate initials from name
  const getInitials = (name?: string | null) => {
    if (!name) return "U";

    const parts = name.split(/\s+/);
    if (parts.length === 1) {
      return name.substring(0, 2).toUpperCase();
    }

    return (
      parts[0].charAt(0) + parts[parts.length - 1].charAt(0)
    ).toUpperCase();
  };

  const initials = getInitials(userName);

  // Handle delete with confirmation
  const handleDeleteClick = () => {
    const currentAvatarUrl = localPreviewUrl || initialAvatarUrl;
    if (!currentAvatarUrl) return;

    if (confirm("Are you sure you want to remove your profile picture?")) {
      handleAvatarDelete(currentAvatarUrl);
    }
  };

  // Check if avatar exists
  const hasAvatar = !!(localPreviewUrl || initialAvatarUrl);

  return (
    <div className="flex flex-col items-center space-y-4">
      <motion.div
        className="relative"
        whileHover={{ scale: 1.05 }}
        transition={{ type: "spring", stiffness: 300, damping: 15 }}
      >
        <Avatar
          className={cn(
            "h-24 w-24",
            "border-2 border-[var(--brand-gold)]",
            "shadow-lg",
            "ring-2 ring-[var(--brand-gold)]/20",
            "transition-all duration-300"
          )}
        >
          {localPreviewUrl || initialAvatarUrl ? (
            <AvatarImage
              src={localPreviewUrl || initialAvatarUrl}
              alt={userName || "User"}
            />
          ) : null}
          <AvatarFallback
            className={cn(
              "bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900",
              "text-blue-800 dark:text-blue-200 text-xl"
            )}
          >
            {initials}
          </AvatarFallback>
        </Avatar>

        <motion.label
          htmlFor="avatar-upload"
          className={cn(
            "absolute bottom-0 right-0 p-1.5 rounded-full",
            "bg-[var(--brand-gold)] text-white cursor-pointer",
            "hover:bg-[var(--brand-gold)]/90 transition-colors",
            "shadow-md"
          )}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          <Camera className="h-4 w-4" />
          <span className="sr-only">Upload avatar</span>
        </motion.label>

        {/* Delete button - only show when avatar exists */}
        {hasAvatar && (
          <motion.button
            onClick={handleDeleteClick}
            className={cn(
              "absolute top-0 right-0 p-1.5 rounded-full",
              "bg-red-500 text-white cursor-pointer",
              "hover:bg-red-600 transition-colors",
              "shadow-md"
            )}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            disabled={isAvatarUploading}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Remove avatar</span>
          </motion.button>
        )}

        <Input
          id="avatar-upload"
          type="file"
          accept="image/png, image/jpeg, image/gif, image/webp"
          className="hidden"
          onChange={(e) => onFileSelect(e.target.files?.[0] || null)}
          disabled={isAvatarUploading}
        />
      </motion.div>

      {isAvatarUploading && (
        <div className="flex items-center text-sm text-neutral-500 dark:text-neutral-400">
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Uploading...
        </div>
      )}

      {avatarErrorDisplay && (
        <div className="text-sm text-red-500">{avatarErrorDisplay}</div>
      )}

      {/* Image Crop Dialog */}
      <ImageCropDialog
        isOpen={!!imageToCrop}
        imgSrc={imageToCrop}
        onCropComplete={handleCropComplete}
        onClose={handleCropDialogClose}
      />
    </div>
  );
}
