"use client";

import React, { useRef } from "react";
import { motion, useInView } from "framer-motion";
import Link from "next/link";
import { ArrowLef<PERSON>, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardDescription, <PERSON><PERSON>ooter, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import AnimatedTitle from "./AnimatedTitle";
import EnhancedFAQSection from "./EnhancedFAQSection";

interface QuickHelpItem {
  text: string;
}

interface QuickHelpSection {
  title: string;
  items: QuickHelpItem[];
}

interface GuideStep {
  title: string;
  steps: string[];
  tip?: string;
}

interface GuideSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  content: GuideStep[];
}

interface RelatedResource {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
}

interface FAQItem {
  id: number;
  question: string;
  answer: string;
}

interface EnhancedSupportSubPageProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  quickHelp: QuickHelpSection[];
  guideSections: GuideSection[];
  faqs: FAQItem[];
  relatedResources?: RelatedResource[];
  navigationButtons: Array<{
    label: string;
    href: string;
  }>;
}

export default function EnhancedSupportSubPage({
  title,
  description,
  icon,
  quickHelp,
  guideSections,
  faqs,
  relatedResources,
  navigationButtons,
}: EnhancedSupportSubPageProps) {
  const heroRef = useRef<HTMLDivElement>(null);
  const isHeroInView = useInView(heroRef, { once: true, amount: 0.3 });

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemFadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="min-h-screen py-12 px-4 md:px-6 lg:px-8 bg-background">
      {/* Enhanced Hero Section */}
      <section ref={heroRef} className="max-w-7xl mx-auto mb-16 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-3/4 h-3/4 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/3 rounded-full blur-3xl"></div>
        </div>

        <motion.div
          initial="hidden"
          animate={isHeroInView ? "visible" : "hidden"}
          variants={fadeIn}
          className="text-center relative z-10"
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={isHeroInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.5 }}
          >
            <Link
              href="/support"
              className="inline-flex items-center text-muted-foreground hover:text-[var(--brand-gold)] mb-8 transition-colors group"
            >
              <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform" />
              Back to Support Center
            </Link>
          </motion.div>

          <motion.div
            className="flex justify-center mb-8"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isHeroInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="bg-gradient-to-br from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/10 p-6 rounded-2xl backdrop-blur-sm border border-[var(--brand-gold)]/20">
              {icon}
            </div>
          </motion.div>

          <AnimatedTitle
            title={title}
            highlightWords={[title.split(' ').pop() || '']}
            subtitle={description}
            size="medium"
            className="mb-8"
          />

          {/* Enhanced Navigation Buttons */}
          <motion.div
            className="flex flex-wrap justify-center gap-3 mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={isHeroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            {navigationButtons.map((button, index) => (
              <motion.div
                key={button.href}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={isHeroInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
              >
                <Button
                  asChild
                  variant="outline"
                  className="border-[var(--brand-gold)]/50 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 hover:border-[var(--brand-gold)] transition-all duration-300"
                >
                  <a href={button.href}>
                    {button.label}
                  </a>
                </Button>
              </motion.div>
            ))}
          </motion.div>

          {/* Enhanced Quick Help Section */}
          <motion.div
            className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 max-w-4xl mx-auto shadow-lg"
            initial={{ opacity: 0, y: 30 }}
            animate={isHeroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center justify-center">
              <span className="bg-[var(--brand-gold)]/10 p-2 rounded-lg mr-3">
                💡
              </span>
              Quick Help
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
              {quickHelp.map((section, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                  animate={isHeroInView ? { opacity: 1, x: 0 } : {}}
                  transition={{ duration: 0.5, delay: 1 + index * 0.1 }}
                >
                  <h3 className="font-semibold text-foreground mb-4 text-lg">{section.title}</h3>
                  <ul className="space-y-3">
                    {section.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start">
                        <span className="bg-[var(--brand-gold)]/20 text-[var(--brand-gold)] rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0 mt-0.5 text-sm font-medium">
                          →
                        </span>
                        <span className="text-muted-foreground leading-relaxed">{item.text}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </section>

      {/* Enhanced Guide Sections */}
      <section className="max-w-7xl mx-auto mb-16">
        {guideSections.map((section, _sectionIndex) => (
          <motion.div
            key={section.id}
            id={section.id}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeIn}
            className="mb-20"
          >
            <motion.div
              className="flex items-center mb-12"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div className="bg-gradient-to-br from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/10 p-4 rounded-xl mr-6 backdrop-blur-sm border border-[var(--brand-gold)]/20">
                {section.icon}
              </div>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-foreground">
                  {section.title}
                </h2>
                <div className="h-1 w-20 bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] rounded-full mt-2"></div>
              </div>
            </motion.div>

            <div className="grid gap-8">
              {section.content.map((item, index) => (
                <motion.div
                  key={index}
                  variants={itemFadeIn}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-foreground mb-6 flex items-center">
                      <span className="bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] rounded-lg w-8 h-8 flex items-center justify-center mr-3 text-sm font-bold">
                        {index + 1}
                      </span>
                      {item.title}
                    </h3>
                    <ol className="space-y-4 mb-6">
                      {item.steps.map((step, stepIndex) => (
                        <li key={stepIndex} className="flex items-start">
                          <span className="bg-[var(--brand-gold)]/20 text-[var(--brand-gold)] rounded-full w-7 h-7 flex items-center justify-center mr-4 flex-shrink-0 mt-0.5 text-sm font-semibold">
                            {stepIndex + 1}
                          </span>
                          <span className="text-muted-foreground leading-relaxed">{step}</span>
                        </li>
                      ))}
                    </ol>

                    {item.tip && (
                      <div className="bg-gradient-to-r from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/5 border-l-4 border-[var(--brand-gold)] p-6 rounded-r-xl">
                        <h4 className="text-[var(--brand-gold)] font-bold mb-3 flex items-center">
                          <span className="mr-2">💡</span>
                          Pro Tip
                        </h4>
                        <p className="text-muted-foreground leading-relaxed">{item.tip}</p>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        ))}
      </section>

      {/* Enhanced FAQ Section */}
      <EnhancedFAQSection faqs={faqs} />

      {/* Enhanced Related Resources Section */}
      {relatedResources && relatedResources.length > 0 && (
        <section className="max-w-7xl mx-auto mb-16">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="text-center mb-12"
          >
            <AnimatedTitle
              title="Related Resources"
              highlightWords={["Resources"]}
              subtitle="Explore these guides to get the most out of your Dukancard experience"
              size="medium"
            />
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            {relatedResources.map((resource, index) => (
              <motion.div key={index} variants={itemFadeIn}>
                <Link href={resource.href} className="block h-full group">
                  <Card className="h-full bg-card/50 backdrop-blur-sm border border-border/50 hover:border-[var(--brand-gold)]/50 hover:shadow-xl transition-all duration-300 cursor-pointer group-hover:scale-105">
                    <CardHeader className="pb-4">
                      <div className="bg-gradient-to-br from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/10 p-4 rounded-xl w-fit group-hover:scale-110 transition-transform duration-300">
                        {resource.icon}
                      </div>
                      <CardTitle className="text-xl mt-4 group-hover:text-[var(--brand-gold)] transition-colors">
                        {resource.title}
                      </CardTitle>
                      <CardDescription className="text-muted-foreground leading-relaxed">
                        {resource.description}
                      </CardDescription>
                    </CardHeader>
                    <CardFooter className="pt-0">
                      <Button
                        variant="ghost"
                        className="w-full justify-start p-0 text-[var(--brand-gold)] hover:text-[var(--brand-gold)] hover:bg-transparent group-hover:translate-x-1 transition-transform"
                      >
                        View Guide <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </section>
      )}
    </div>
  );
}
