import {
  sendEmailOTP,
  verifyEmailOTP,
  validateEmail,
  validateOTP,
} from '@/backend/supabase/services/auth/emailOtpService';
import { supabase } from '@/lib/supabase';

// Mock the supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      signInWithOtp: jest.fn(),
      verifyOtp: jest.fn(),
    },
  },
}));

describe('emailOtpService', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendEmailOTP', () => {
    it('should return success on valid email', async () => {
      (supabase.auth.signInWithOtp as jest.Mock).mockResolvedValue({ error: null });
      const response = await sendEmailOTP('<EMAIL>');
      expect(response.success).toBe(true);
      expect(response.message).toBe('OTP sent to your email address. Please check your inbox.');
    });

    it('should handle rate limit errors', async () => {
      (supabase.auth.signInWithOtp as jest.Mock).mockResolvedValue({ error: { message: 'rate limit exceeded' } });
      const response = await sendEmailOTP('<EMAIL>');
      expect(response.success).toBe(false);
      expect(response.message).toBe('Too many requests. Please wait a moment before trying again.');
    });

    it('should handle invalid email errors', async () => {
        (supabase.auth.signInWithOtp as jest.Mock).mockResolvedValue({ error: { message: 'invalid email' } });
        const response = await sendEmailOTP('invalid-email');
        expect(response.success).toBe(false);
        expect(response.message).toBe('Please enter a valid email address.');
      });
  
      it('should handle generic errors', async () => {
        (supabase.auth.signInWithOtp as jest.Mock).mockResolvedValue({ error: { message: 'Some other error' } });
        const response = await sendEmailOTP('<EMAIL>');
        expect(response.success).toBe(false);
        expect(response.message).toBe('Some other error');
      });
  
      it('should handle unexpected errors', async () => {
        (supabase.auth.signInWithOtp as jest.Mock).mockRejectedValue(new Error('Network error'));
        const response = await sendEmailOTP('<EMAIL>');
        expect(response.success).toBe(false);
        expect(response.message).toBe('An unexpected error occurred. Please try again.');
      });
  });

  describe('verifyEmailOTP', () => {
    it('should return success on valid OTP', async () => {
        (supabase.auth.verifyOtp as jest.Mock).mockResolvedValue({ error: null });
        const response = await verifyEmailOTP('<EMAIL>', '123456');
        expect(response.success).toBe(true);
        expect(response.message).toBe('Successfully signed in!');
      });
  
      it('should handle invalid or expired OTP errors', async () => {
        (supabase.auth.verifyOtp as jest.Mock).mockResolvedValue({ error: { message: 'invalid token' } });
        const response = await verifyEmailOTP('<EMAIL>', '123456');
        expect(response.success).toBe(false);
        expect(response.message).toBe('Invalid or expired OTP. Please try again.');
      });
  
      it('should handle too many attempts errors', async () => {
        (supabase.auth.verifyOtp as jest.Mock).mockResolvedValue({ error: { message: 'too many attempts' } });
        const response = await verifyEmailOTP('<EMAIL>', '123456');
        expect(response.success).toBe(false);
        expect(response.message).toBe('Too many verification attempts. Please wait before trying again.');
      });
  
      it('should handle generic errors', async () => {
        (supabase.auth.verifyOtp as jest.Mock).mockResolvedValue({ error: { message: 'Some other error' } });
        const response = await verifyEmailOTP('<EMAIL>', '123456');
        expect(response.success).toBe(false);
        expect(response.message).toBe('Some other error');
      });
  
      it('should handle unexpected errors', async () => {
        (supabase.auth.verifyOtp as jest.Mock).mockRejectedValue(new Error('Network error'));
        const response = await verifyEmailOTP('<EMAIL>', '123456');
        expect(response.success).toBe(false);
        expect(response.message).toBe('An unexpected error occurred. Please try again.');
      });
  });

  describe('validateEmail', () => {
    it('should return valid for a correct email', () => {
        const result = validateEmail('<EMAIL>');
        expect(result.isValid).toBe(true);
      });
  
      it('should return invalid for an empty email', () => {
        const result = validateEmail('');
        expect(result.isValid).toBe(false);
        expect(result.message).toBe('Email is required');
      });
  
      it('should return invalid for a malformed email', () => {
        const result = validateEmail('test.com');
        expect(result.isValid).toBe(false);
        expect(result.message).toBe('Please enter a valid email address');
      });
  });

  describe('validateOTP', () => {
    it('should return valid for a correct OTP', () => {
        const result = validateOTP('123456');
        expect(result.isValid).toBe(true);
      });
  
      it('should return invalid for an empty OTP', () => {
        const result = validateOTP('');
        expect(result.isValid).toBe(false);
        expect(result.message).toBe('OTP is required');
      });
  
      it('should return invalid for an OTP with incorrect length', () => {
        const result = validateOTP('123');
        expect(result.isValid).toBe(false);
        expect(result.message).toBe('OTP must be 6 digits');
      });
  
      it('should return invalid for an OTP with non-numeric characters', () => {
        const result = validateOTP('12345a');
        expect(result.isValid).toBe(false);
        expect(result.message).toBe('OTP must contain only numbers');
      });
  });
});
