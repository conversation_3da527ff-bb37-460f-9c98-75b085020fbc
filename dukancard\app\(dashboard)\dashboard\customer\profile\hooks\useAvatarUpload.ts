"use client";

import { useState, useTransition } from "react";
import { toast } from "sonner";
import {
  uploadAvatarAndGetUrl,
  updateAvatarUrl,
  deleteCustomerAvatar,
} from "../avatar-actions";
import { compressImageModerateClient } from "@/lib/utils/client-image-compression";

export type AvatarUploadStatus = "idle" | "uploading" | "success" | "error";

interface UseAvatarUploadOptions {
  initialAvatarUrl?: string;
  onUpdateAvatar: (_url: string) => void;
}

export function useAvatarUpload({ onUpdateAvatar }: UseAvatarUploadOptions) {
  const [avatarUploadStatus, setAvatarUploadStatus] =
    useState<AvatarUploadStatus>("idle");
  const [avatarUploadError, setAvatarUploadError] = useState<string | null>(
    null
  );
  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(null);
  const [isAvatarUploading, startAvatarUploadTransition] = useTransition();
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null);

  // File selection handler
  const onFileSelect = (file: File | null) => {
    if (localPreviewUrl) {
      URL.revokeObjectURL(localPreviewUrl);
      setLocalPreviewUrl(null);
    }

    if (file) {
      if (file.size > 15 * 1024 * 1024) {
        toast.error("File size must be less than 15MB.");
        setAvatarUploadStatus("idle");
        setAvatarUploadError("File size must be less than 15MB.");
        setLocalPreviewUrl(null);
        return;
      }

      // Prepare for cropping
      setOriginalFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImageToCrop(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setAvatarUploadStatus("idle");
      setAvatarUploadError(null);
      setLocalPreviewUrl(null);
    }
  };

  // Avatar upload handler
  const handleAvatarUpload = async (file: File) => {
    setAvatarUploadStatus("uploading");
    setAvatarUploadError(null);

    startAvatarUploadTransition(async () => {
      const formData = new FormData();
      formData.append("avatarFile", file);

      const result = await uploadAvatarAndGetUrl(formData);

      if (result.success && result.url) {
        const newAvatarUrl = result.url;

        // Update preview
        setAvatarUploadStatus("success");

        // Clean up preview URL
        setLocalPreviewUrl(null);
        if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);

        toast.success("Avatar uploaded successfully!");

        // Save URL to DB immediately
        try {
          const updateResult = await updateAvatarUrl(newAvatarUrl);
          if (!updateResult.success) {
            toast.error(
              `Avatar uploaded, but failed to save URL: ${updateResult.error}`
            );
          }

          // Update parent component state after successful DB save
          if (updateResult.success) {
            onUpdateAvatar(newAvatarUrl);
          }
        } catch (err) {
          console.error("Error saving avatar URL:", err);
          toast.error("Error saving avatar URL after upload.");
        }
      } else {
        setAvatarUploadStatus("error");
        const errorMessage = result.error || "Failed to upload avatar.";
        setAvatarUploadError(errorMessage);
        setLocalPreviewUrl(null);
        if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);

        // Show user-friendly error message
        if (errorMessage.includes("File size must be less than 15MB")) {
          toast.error("Image too large", {
            description: "Please select an image smaller than 15MB",
          });
        } else if (errorMessage.includes("Invalid file type")) {
          toast.error("Invalid file type", {
            description: "Please select a JPG, PNG, WebP, or GIF image",
          });
        } else {
          toast.error("Upload failed", {
            description: errorMessage,
          });
        }
      }
    });
  };

  // Handle crop completion
  const handleCropComplete = async (croppedBlob: Blob | null) => {
    setImageToCrop(null); // Close dialog

    if (croppedBlob && originalFile) {
      try {
        // Convert blob to file for compression
        const croppedFile = new File([croppedBlob], originalFile.name, {
          type: "image/png", // Canvas outputs PNG
        });

        // Compress image on client-side first - targeting under 50KB for avatars
        const compressionResult = await compressImageModerateClient(
          croppedFile,
          {
            maxDimension: 400,
            targetSizeKB: 45, // Target 45KB to ensure we stay under 50KB
          }
        );

        // Convert compressed blob back to file
        const compressedFile = new File(
          [compressionResult.blob],
          originalFile.name,
          {
            type: compressionResult.blob.type,
          }
        );

        const previewUrl = URL.createObjectURL(compressedFile);
        setLocalPreviewUrl(previewUrl);
        handleAvatarUpload(compressedFile);
      } catch (error) {
        console.error("Image compression failed:", error);
        toast.error("Failed to process image. Please try a different image.");
        setOriginalFile(null);
        const fileInput = document.querySelector(
          'input[type="file"]'
        ) as HTMLInputElement;
        if (fileInput) fileInput.value = "";
      }
    } else {
      // Handle crop cancellation or error
      setOriginalFile(null);
      const fileInput = document.querySelector(
        'input[type="file"]'
      ) as HTMLInputElement;
      if (fileInput) fileInput.value = "";
    }
  };

  // Handle crop dialog close
  const handleCropDialogClose = () => {
    setImageToCrop(null);
    setOriginalFile(null);
    // Clear the file input
    const fileInput = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement;
    if (fileInput) fileInput.value = "";
  };

  // Handle avatar deletion
  const handleAvatarDelete = async (avatarUrl: string) => {
    startAvatarUploadTransition(async () => {
      try {
        setAvatarUploadStatus("uploading"); // Reuse uploading status for deletion
        setAvatarUploadError(null);

        const result = await deleteCustomerAvatar(avatarUrl);

        if (result.success) {
          setAvatarUploadStatus("success");
          setLocalPreviewUrl(null);
          onUpdateAvatar(""); // Clear the avatar URL
          toast.success("Avatar deleted successfully!");
        } else {
          setAvatarUploadStatus("error");
          setAvatarUploadError(result.error || "Failed to delete avatar");
          toast.error(result.error || "Failed to delete avatar");
        }
      } catch (error) {
        setAvatarUploadStatus("error");
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete avatar";
        setAvatarUploadError(errorMessage);
        toast.error(errorMessage);
      }
    });
  };

  // Avatar error display component
  const avatarErrorDisplay =
    avatarUploadStatus === "error" && avatarUploadError
      ? avatarUploadError
      : null;

  return {
    avatarUploadStatus,
    avatarUploadError,
    localPreviewUrl,
    isAvatarUploading,
    imageToCrop,
    onFileSelect,
    handleAvatarUpload,
    handleCropComplete,
    handleCropDialogClose,
    handleAvatarDelete,
    avatarErrorDisplay,
  };
}
