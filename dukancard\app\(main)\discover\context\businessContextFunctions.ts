"use client";

import { useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchDiscoverCombined } from "../actions/combinedActions";
import { fetchMoreBusinessCardsCombined } from "../actions/businessActions";
import { DISCOVER_BUSINESSES_PER_PAGE } from "../constants/paginationConstants";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import {
  BUSINESS_NAME_PARAM,
  BUSINESS_SORT_PARAM,
  PINCODE_PARAM,
  LOCALITY_PARAM,
} from "../constants/urlParamConstants";
import { DiscoverSearchResult, ViewType } from "./types";

// Business context functions
export function useBusinessContextFunctions(
  viewType: ViewType,
  setIsSearching: (_value: boolean) => void,
  setSearchResult: (_value: DiscoverSearchResult | null) => void,
  setIsAuthenticated: (_value: boolean) => void,
  setBusinesses: (_value: BusinessCardData[]) => void,
  setHasMore: (_value: boolean) => void,
  setTotalCount: (_value: number) => void,
  setCurrentPage: (_value: number) => void,
  setSortBy: (_value: BusinessSortBy) => void,
  setSearchError: (_value: string | null) => void,
  businesses: BusinessCardData[],
  sortBy: BusinessSortBy
) {
  const searchParams = useSearchParams();
  const [, startSearchTransition] = useTransition();

  // Handle business sort change
  const handleBusinessSortChange = (sortOption: BusinessSortBy) => {
    if (sortOption !== sortBy) {
      setSortBy(sortOption);
      setIsSearching(true);

      // Update URL to include the sort option
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        // Set the new parameter and remove the old one
        url.searchParams.set(BUSINESS_SORT_PARAM, sortOption);
        url.searchParams.delete("sortBy");
        window.history.replaceState({}, "", url.toString());
      }

      // If we already have search results, perform the search again with the new sort
      if (viewType === "cards") {
        const businessName = searchParams.get(BUSINESS_NAME_PARAM) || null;
        const pincode = searchParams.get(PINCODE_PARAM) || null;
        let locality = searchParams.get(LOCALITY_PARAM) || null;

        // Handle "_any" locality value
        if (locality === "_any") {
          locality = "";
        }

        startSearchTransition(async () => {
          const result = await searchDiscoverCombined({
            businessName,
            pincode,
            locality,
            viewType,
            page: 1,
            limit: DISCOVER_BUSINESSES_PER_PAGE,
            businessSort: sortOption,
          });

          if (result.data) {
            setSearchResult(result.data);
            setIsAuthenticated(result.data.isAuthenticated);

            if (result.data.businesses) {
              setBusinesses(result.data.businesses);
            }

            setHasMore(result.data.hasMore);
            setTotalCount(result.data.totalCount);
            setCurrentPage(1);
          } else {
            setSearchError(result.error || "Failed to fetch results.");
          }
          setIsSearching(false);
        });
      }
    }
  };

  // Handle business search
  const handleBusinessSearch = (term: string) => {
    // Prevent unnecessary API calls
    if (viewType !== "cards") {
      return;
    }

    setIsSearching(true);

    // Add a flag to prevent duplicate calls
    const currentSearchTerm = term;

    // Update URL to include the search term
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href);
      if (currentSearchTerm) {
        url.searchParams.set(BUSINESS_NAME_PARAM, currentSearchTerm);
      } else {
        url.searchParams.delete(BUSINESS_NAME_PARAM);
      }
      window.history.replaceState({}, "", url.toString());
    }

    // If search term is empty, reset to initial state
    if (!currentSearchTerm) {
      // If we have pincode/locality, search with those parameters
      const pincode = searchParams.get(PINCODE_PARAM) || null;
      let locality = searchParams.get(LOCALITY_PARAM) || null;

      // Handle "_any" locality value
      if (locality === "_any") {
        locality = "";
      }

      startSearchTransition(async () => {
        // Add a small delay to prevent race conditions
        await new Promise((resolve) => setTimeout(resolve, 100));

        const result = await searchDiscoverCombined({
          businessName: null,
          pincode,
          locality,
          viewType,
          page: 1,
          limit: DISCOVER_BUSINESSES_PER_PAGE,
          businessSort: sortBy,
        });

        if (result.data) {
          setSearchResult(result.data);
          setIsAuthenticated(result.data.isAuthenticated);

          if (result.data.businesses) {
            setBusinesses(result.data.businesses);
          }

          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(1);
        } else {
          setSearchError(result.error || "Failed to fetch results.");
        }
        setIsSearching(false);
      });
      return;
    }

    // If search term is provided, search with it
    const pincode = searchParams.get(PINCODE_PARAM) || null;
    let locality = searchParams.get(LOCALITY_PARAM) || null;

    // Handle "_any" locality value
    if (locality === "_any") {
      locality = "";
    }

    // Reset state before new search
    setSearchError(null);
    setBusinesses([]);
    setCurrentPage(1);
    setHasMore(false);
    setTotalCount(0);

    startSearchTransition(async () => {
      // Add a small delay to prevent race conditions
      await new Promise((resolve) => setTimeout(resolve, 100));

      const result = await searchDiscoverCombined({
        businessName: currentSearchTerm,
        pincode,
        locality,
        viewType,
        page: 1,
        limit: DISCOVER_BUSINESSES_PER_PAGE,
        businessSort: sortBy,
      });

      if (result.data) {
        setSearchResult(result.data);
        setIsAuthenticated(result.data.isAuthenticated);

        if (result.data.businesses) {
          setBusinesses(result.data.businesses);
        }

        setHasMore(result.data.hasMore);
        setTotalCount(result.data.totalCount);
        setCurrentPage(1);
      } else {
        setSearchError(result.error || "Failed to fetch results.");
      }
      setIsSearching(false);
    });
  };

  // Load more businesses
  const loadMoreBusinesses = async (
    nextPage: number,
    isLoadingMore: boolean,
    setIsLoadingMore: (_value: boolean) => void
  ) => {
    if (isLoadingMore) {
      return;
    }

    const businessName = searchParams.get(BUSINESS_NAME_PARAM) || null;
    const pincode = searchParams.get(PINCODE_PARAM) || null;
    let locality = searchParams.get(LOCALITY_PARAM) || null;

    // Handle "_any" locality value
    if (locality === "_any") {
      locality = "";
    }

    setIsLoadingMore(true);

    try {
      // Add a small delay to prevent race conditions
      await new Promise((resolve) => setTimeout(resolve, 300));

      const result = await fetchMoreBusinessCardsCombined({
        businessName,
        pincode,
        locality,
        page: nextPage,
        limit: DISCOVER_BUSINESSES_PER_PAGE,
        sortBy,
      });

      if (result.data && result.data.businesses) {
        if (result.data.businesses.length > 0) {
          const newBusinesses = result.data.businesses;

          // Filter out any businesses that already exist in the current list to avoid duplicates
          const existingIds = new Set(businesses.map((b) => b.id));
          const uniqueNewBusinesses = newBusinesses.filter(
            (business) => !existingIds.has(business.id)
          );

          if (uniqueNewBusinesses.length > 0) {
            // Combine previous businesses with new ones
            const updatedBusinesses = [...businesses, ...uniqueNewBusinesses];
            setBusinesses(updatedBusinesses);
            // Use the hasMore flag from the server
            setHasMore(result.data.hasMore);
            setCurrentPage(nextPage);
            return true;
          } else {
            // If all new businesses were duplicates, stop loading more
            console.warn(
              "All new businesses were duplicates, stopping infinite scroll"
            );
            setHasMore(false);
            return false;
          }
        } else {
          setHasMore(false);
          return false;
        }
      } else {
        setHasMore(false);
        return false;
      }
    } catch (error) {
      console.error("Error loading more businesses:", error);
      setHasMore(false);
      return false;
    } finally {
      setIsLoadingMore(false);
    }
  };

  return {
    handleBusinessSortChange,
    handleBusinessSearch,
    loadMoreBusinesses,
  };
}
