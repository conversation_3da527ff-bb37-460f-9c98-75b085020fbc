"use client";

import { Skeleton } from "@/components/ui/skeleton";

export default function ProductGridSkeleton() {
  return (
    <div className="space-y-6">
      {/* Product Grid Skeleton */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div
            key={index}
            className="border border-neutral-200/80 dark:border-neutral-800/80 rounded-xl overflow-hidden bg-white dark:bg-neutral-900 transition-all duration-300 w-full"
          >
            <Skeleton className="h-48 w-full" />
            <div className="p-2 sm:p-3 md:p-4 space-y-2 sm:space-y-3">
              <Skeleton className="h-4 sm:h-5 w-3/4" />
              <Skeleton className="h-3 sm:h-4 w-1/2" />
              <div className="flex justify-between items-center pt-1 sm:pt-2">
                <Skeleton className="h-5 sm:h-6 w-16 sm:w-20" />
                <Skeleton className="h-6 sm:h-8 w-6 sm:w-8 rounded-full" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
