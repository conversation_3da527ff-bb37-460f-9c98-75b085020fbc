"use client";

import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useLocalityContext } from "../context/LocalityContext";

export default function ErrorSection() {
  const { searchError } = useLocalityContext();

  if (!searchError) return null;

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{searchError}</AlertDescription>
    </Alert>
  );
}
