"use client";

import { createContext, useContext, useState, ReactNode } from "react";

interface PricingCardContextType {
  featuresExpanded: boolean;
  setFeaturesExpanded: (_expanded: boolean) => void;
  toggleFeatures: () => void;
}

const PricingCardContext = createContext<PricingCardContextType | undefined>(undefined);

export function PricingCardProvider({ children }: { children: ReactNode }) {
  const [featuresExpanded, setFeaturesExpanded] = useState(false);

  const toggleFeatures = () => {
    setFeaturesExpanded(!featuresExpanded);
  };

  return (
    <PricingCardContext.Provider value={{
      featuresExpanded,
      setFeaturesExpanded,
      toggleFeatures
    }}>
      {children}
    </PricingCardContext.Provider>
  );
}

export function usePricingCard() {
  const context = useContext(PricingCardContext);
  if (context === undefined) {
    throw new Error('usePricingCard must be used within a PricingCardProvider');
  }
  return context;
}
