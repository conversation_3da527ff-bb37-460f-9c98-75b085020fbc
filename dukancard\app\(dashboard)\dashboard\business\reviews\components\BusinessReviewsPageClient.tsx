"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Star, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import BusinessReviewListClient from "./BusinessReviewListClient";
import BusinessMyReviewListClient from "./BusinessMyReviewListClient";
import { cn } from "@/lib/utils";
import { formatIndianNumberShort } from "@/lib/utils";

interface BusinessReviewsPageClientProps {
  businessProfileId: string;
  reviewsReceivedCount: number;
  myReviewsCount: number;
}

export default function BusinessReviewsPageClient({
  businessProfileId,
  reviewsReceivedCount,
  myReviewsCount
}: BusinessReviewsPageClientProps) {
  const [activeTab, setActiveTab] = useState("received");

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Reviews Header - Full Width */}
      <motion.div variants={itemVariants}>
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
          <div className="p-3 rounded-xl bg-muted hidden sm:block">
            <Star className="w-6 h-6 text-foreground" />
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-foreground">
              Business Reviews
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage reviews for your business
            </p>
          </div>
        </div>
      </motion.div>

      {/* Tabs Section - Full Width */}
      <motion.div variants={itemVariants}>
        <div className="flex space-x-1 border-b border-border">
          <Button
            variant={activeTab === 'received' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('received')}
            className={cn(
              "rounded-b-none border-b-2 border-transparent",
              activeTab === 'received'
                ? "border-b-[var(--brand-gold)] bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]"
                : ""
            )}
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            Reviews Received ({formatIndianNumberShort(reviewsReceivedCount)})
          </Button>
          <Button
            variant={activeTab === 'given' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('given')}
            className={cn(
              "rounded-b-none border-b-2 border-transparent",
              activeTab === 'given'
                ? "border-b-[var(--brand-gold)] bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]"
                : ""
            )}
          >
            <Star className="w-4 h-4 mr-2" />
            My Reviews ({formatIndianNumberShort(myReviewsCount)})
          </Button>
        </div>
      </motion.div>

      {/* Content Section - Full Width */}
      <motion.div variants={itemVariants}>
        {activeTab === 'received' && (
          <BusinessReviewListClient businessProfileId={businessProfileId} />
        )}
        {activeTab === 'given' && (
          <BusinessMyReviewListClient />
        )}
      </motion.div>
    </motion.div>
  );
}
