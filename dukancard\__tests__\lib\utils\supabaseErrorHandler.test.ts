import { handleSupabaseAuthError, isEmailRateLimitError, isRateLimitError, isTemporaryError, getRetryDelay } from "@/lib/utils/supabaseErrorHandler";

// Mock AuthError since it's a class and needs to be mocked for testing
jest.mock('@supabase/supabase-js', () => ({
  AuthError: jest.fn().mockImplementation((message, status, code) => {
    const error = new Error(message);
    (error as any).status = status;
    (error as any).code = code;
    return error;
  }),
}));

import { AuthError } from "@supabase/supabase-js";

describe('supabaseErrorHandler', () => {
  describe('handleSupabaseAuthError', () => {
    it('should return a default message for null error', () => {
      expect(handleSupabaseAuthError(null)).toBe('An error occurred. Please try again.');
    });

    it('should return a default message for generic Error object', () => {
      expect(handleSupabaseAuthError(new Error('Something went wrong'))).toBe('An error occurred. Please try again.');
    });

    it('should return user-friendly message for known AuthError codes', () => {
      const authError = new AuthError('Email rate limit exceeded', 429, 'over_email_send_rate_limit');
      expect(handleSupabaseAuthError(authError)).toBe('Email rate limit exceeded. Please wait before requesting another OTP.');
    });

    it('should return user-friendly message for common message patterns (token expired)', () => {
      const error = new Error('token has expired');
      expect(handleSupabaseAuthError(error)).toBe('Your session has expired. Please sign in again.');
    });

    it('should return user-friendly message for common message patterns (invalid token)', () => {
      const error = new Error('invalid token');
      expect(handleSupabaseAuthError(error)).toBe('Invalid code. Please check and try again.');
    });

    it('should return user-friendly message for common message patterns (rate limit)', () => {
      const error = new Error('rate limit exceeded');
      expect(handleSupabaseAuthError(error)).toBe('Too many attempts. Please wait before trying again.');
    });

    it('should return user-friendly message for common message patterns (email already exists)', () => {
      const error = new Error('email already exists');
      expect(handleSupabaseAuthError(error)).toBe('An account with this email already exists.');
    });

    it('should return user-friendly message for common message patterns (invalid email)', () => {
      const error = new Error('invalid email');
      expect(handleSupabaseAuthError(error)).toBe('Please enter a valid email address.');
    });

    it('should return user-friendly message for common message patterns (weak password)', () => {
      const error = new Error('weak password');
      expect(handleSupabaseAuthError(error)).toBe('Password does not meet security requirements.');
    });

    it('should return user-friendly message for network errors', () => {
      const error = new Error('Failed to fetch');
      expect(handleSupabaseAuthError(error)).toBe('Network error. Please check your internet connection and try again.');
    });

    it('should return default message for unknown AuthError code', () => {
      const authError = new AuthError('Unknown error', 500, 'unknown_code');
      expect(handleSupabaseAuthError(authError)).toBe('An error occurred. Please try again.');
    });

    it('should return default message for unknown error message', () => {
      const error = new Error('Some completely new error');
      expect(handleSupabaseAuthError(error)).toBe('An error occurred. Please try again.');
    });
  });

  describe('isRateLimitError', () => {
    it('should return true for known rate limit AuthError codes', () => {
      const error1 = new AuthError('Email rate limit exceeded', 429, 'over_email_send_rate_limit');
      const error2 = new AuthError('Too many requests', 429, 'over_request_rate_limit');
      const error3 = new AuthError('Too many SMS', 429, 'over_sms_send_rate_limit');
      expect(isRateLimitError(error1)).toBe(true);
      expect(isRateLimitError(error2)).toBe(true);
      expect(isRateLimitError(error3)).toBe(true);
    });

    it('should return false for non-rate limit AuthError codes', () => {
      const error = new AuthError('User not found', 404, 'user_not_found');
      expect(isRateLimitError(error)).toBe(false);
    });

    it('should return false for generic Error object', () => {
      expect(isRateLimitError(new Error('Some error'))).toBe(false);
    });

    it('should return false for null error', () => {
      expect(isRateLimitError(null)).toBe(false);
    });
  });

  describe('isEmailRateLimitError', () => {
    it('should return true for over_email_send_rate_limit code', () => {
      const error = new AuthError('Email rate limit exceeded', 429, 'over_email_send_rate_limit');
      expect(isEmailRateLimitError(error)).toBe(true);
    });

    it('should return false for other rate limit codes', () => {
      const error = new AuthError('Too many requests', 429, 'over_request_rate_limit');
      expect(isEmailRateLimitError(error)).toBe(false);
    });

    it('should return false for non-rate limit codes', () => {
      const error = new AuthError('User not found', 404, 'user_not_found');
      expect(isEmailRateLimitError(error)).toBe(false);
    });
  });

  describe('isTemporaryError', () => {
    it('should return true for temporary error codes', () => {
      const error1 = new AuthError('Email rate limit exceeded', 429, 'over_email_send_rate_limit');
      const error2 = new AuthError('Request timed out', 408, 'request_timeout');
      expect(isTemporaryError(error1)).toBe(true);
      expect(isTemporaryError(error2)).toBe(true);
    });

    it('should return false for non-temporary error codes', () => {
      const error = new AuthError('User not found', 404, 'user_not_found');
      expect(isTemporaryError(error)).toBe(false);
    });
  });

  describe('getRetryDelay', () => {
    it('should return correct delay for email rate limit', () => {
      const error = new AuthError('Email rate limit exceeded', 429, 'over_email_send_rate_limit');
      expect(getRetryDelay(error)).toBe(60);
    });

    it('should return correct delay for general rate limit', () => {
      const error = new AuthError('Too many requests', 429, 'over_request_rate_limit');
      expect(getRetryDelay(error)).toBe(300);
    });

    it('should return null for non-rate limit errors', () => {
      const error = new AuthError('User not found', 404, 'user_not_found');
      expect(getRetryDelay(error)).toBeNull();
    });
  });
});
