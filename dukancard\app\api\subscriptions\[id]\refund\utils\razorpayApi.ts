/**
 * Razorpay API functions for subscription refunds
 */

import { getRazorpayApiHeaders } from "@/lib/razorpay/utils/auth";
import { RefundData } from "./types";

// Razorpay API URL
const RAZORPAY_API_URL = process.env.RAZORPAY_API_URL || "https://api.razorpay.com/v2";

/**
 * Process a refund for a payment
 */
export async function processSubscriptionRefund(
  paymentId: string,
  options: {
    amount?: number;
    speed?: string;
    notes?: Record<string, string>;
  },
  _subscriptionId: string
): Promise<
  | { success: true; refundData: RefundData }
  | { success: false; error: string; errorData?: Record<string, unknown> }
> {
  try {
    console.log(`Processing refund for payment ${paymentId} with options:`, options);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Prepare request body
    const requestBody: Record<string, unknown> = {};

    // Add amount if provided
    if (options.amount !== undefined) {
      requestBody.amount = options.amount;
    }

    // Add speed if provided
    if (options.speed) {
      requestBody.speed = options.speed;
    }

    // Add notes if provided
    if (options.notes) {
      requestBody.notes = options.notes;
    }

    // Make API request to Razorpay
    const response = await fetch(
      `${RAZORPAY_API_URL.replace('/v2', '/v1')}/payments/${paymentId}/refund`,
      {
        method: "POST",
        headers: {
          ...headers,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      }
    );

    // Parse response
    const data = await response.json();

    // Check if the request was successful
    if (!response.ok) {
      console.error("Error processing refund:", data);
      return {
        success: false,
        error: data.error?.description || "Failed to process refund",
        errorData: data,
      };
    }

    // Format the response to match the expected RefundData type
    const refundData: RefundData = {
      id: data.id,
      entity: data.entity,
      amount: data.amount,
      currency: data.currency,
      payment_id: data.payment_id,
      notes: data.notes || {},
      receipt: data.receipt,
      acquirer_data: data.acquirer_data,
      created_at: data.created_at,
      batch_id: data.batch_id,
      status: data.status,
      speed_processed: data.speed_processed,
      speed_requested: data.speed_requested,
    };

    console.log("Refund processed successfully:", refundData);
    return { success: true, refundData };
  } catch (error) {
    console.error("Exception processing refund:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Get subscription details from Razorpay
 */
export async function getSubscriptionDetails(subscriptionId: string) {
  try {
    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request to Razorpay
    const response = await fetch(
      `${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}`,
      {
        method: "GET",
        headers,
      }
    );

    // Parse response
    const data = await response.json();

    // Check if the request was successful
    if (!response.ok) {
      console.error("Error getting subscription details:", data);
      return {
        success: false,
        error: data.error?.description || "Failed to get subscription details",
      };
    }

    return { success: true, data };
  } catch (error) {
    console.error("Exception getting subscription details:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Get invoices for a subscription from Razorpay
 */
export async function getInvoicesForSubscription(subscriptionId: string) {
  try {
    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request to Razorpay
    const response = await fetch(
      `${RAZORPAY_API_URL.replace('/v2', '/v1')}/invoices?subscription_id=${subscriptionId}`,
      {
        method: "GET",
        headers,
      }
    );

    // Parse response
    const data = await response.json();

    // Check if the request was successful
    if (!response.ok) {
      console.error("Error getting invoices for subscription:", data);
      return {
        success: false,
        error: data.error?.description || "Failed to get invoices for subscription",
      };
    }

    return { success: true, invoices: data };
  } catch (error) {
    console.error("Exception getting invoices for subscription:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Get payments from Razorpay
 */
export async function getPayments(count: number = 10) {
  try {
    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request to Razorpay
    const response = await fetch(
      `${RAZORPAY_API_URL.replace('/v2', '/v1')}/payments?count=${count}`,
      {
        method: "GET",
        headers,
      }
    );

    // Parse response
    const data = await response.json();

    // Check if the request was successful
    if (!response.ok) {
      console.error("Error getting payments:", data);
      return {
        success: false,
        error: data.error?.description || "Failed to get payments",
      };
    }

    return { success: true, payments: data };
  } catch (error) {
    console.error("Exception getting payments:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Cancel a subscription in Razorpay
 */
export async function cancelSubscription(subscriptionId: string) {
  try {
    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request to Razorpay
    const response = await fetch(
      `${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/cancel`,
      {
        method: "POST",
        headers: {
          ...headers,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cancel_at_cycle_end: 0, // Cancel immediately
        }),
      }
    );

    // Parse response
    const data = await response.json();

    // Check if the request was successful
    if (!response.ok) {
      console.error("Error cancelling subscription:", data);
      return {
        success: false,
        error: data.error?.description || "Failed to cancel subscription",
      };
    }

    return { success: true, data };
  } catch (error) {
    console.error("Exception cancelling subscription:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}
