"use client";

import { motion } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";

export default function AnimatedBusinessGridSkeleton() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-4">
      {Array.from({ length: 6 }).map((_, index) => (
        <SkeletonCard key={index} index={index} />
      ))}
    </div>
  );
}

function SkeletonCard({ index }: { index: number }) {
  return (
    <motion.div
      className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-sm p-2 sm:p-3 md:p-4 h-full flex flex-col w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
    >
      {/* Header with logo and name - centered */}
      <div className="flex flex-col items-center text-center mb-4">
        <Skeleton className="h-12 w-12 sm:h-16 sm:w-16 rounded-full flex-shrink-0 mb-2" />
        <div className="space-y-2 w-full px-2">
          <Skeleton className="h-5 w-3/4 mx-auto" />
        </div>
      </div>

      {/* Business details */}
      <div className="space-y-2 mb-3 flex-grow">
        {/* Location with multiple lines */}
        <div className="flex gap-1.5 sm:gap-2 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg p-2 sm:p-2.5 border border-neutral-100 dark:border-neutral-800">
          <Skeleton className="h-4 w-4 rounded-full flex-shrink-0 mt-0.5" />
          <div className="flex-1 space-y-1.5">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-4/5" />
            <Skeleton className="h-3 w-3/5" />
          </div>
        </div>

        {/* Category badge */}
        <div className="flex items-center justify-center mt-2">
          <Skeleton className="h-6 w-24 rounded-full" />
        </div>
      </div>

      {/* Stats row */}
      <div className="flex items-center justify-between pt-2 border-t border-neutral-100 dark:border-neutral-800 mt-auto">
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-4 w-12" />
      </div>


    </motion.div>
  );
}
