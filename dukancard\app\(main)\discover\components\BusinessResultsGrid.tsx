"use client";

import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import AnimatedBusinessGrid from "./AnimatedBusinessGrid";

interface BusinessResultsGridProps {
  businesses: BusinessCardData[];
  lastItemRef: (_node: HTMLDivElement | null) => void;
}

export default function BusinessResultsGrid({
  businesses,
  lastItemRef,
}: BusinessResultsGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {businesses.map((business, index) => {
        if (index === businesses.length - 1) {
          return (
            <div key={business.id} ref={lastItemRef}>
              <AnimatedBusinessGrid
                businesses={[business]}
                isAuthenticated={false}
              />
            </div>
          );
        }
        return (
          <div key={business.id}>
            <AnimatedBusinessGrid
              businesses={[business]}
              isAuthenticated={false}
            />
          </div>
        );
      })}
    </div>
  );
}
