"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { formatDistanceToNow } from "date-fns";


import {
  CheckCircle2,
  Clock,
  CreditCard,
  AlertTriangle,
  PauseCircle,
  XCircle,
} from "lucide-react";
// Unused import removed: import FlipTimer from "@/app/(dashboard)/dashboard/business/components/FlipTimer";
import { SubscriptionStatusType } from "@/lib/types/subscription";
import {
  SUBSCRIPTION_STATUS
} from "@/lib/razorpay/webhooks/handlers/utils";

interface ModernSubscriptionStatusCardProps {
  subscriptionId: string;
  status: SubscriptionStatusType;
  planName: string;
  planCycle: "monthly" | "yearly";
  amount: number;
  currency?: string; // Made optional since we're using ₹ symbol directly
  nextBillingDate?: string | null;
  lastPaymentDate?: string | null;
  lastPaymentMethod?: string | null;
  createdAt?: string | null;
  expiresAt?: string | null; // Current period end date
  pausedAt?: string | null;
  cancellationRequestedAt?: string | null;
  cancelledAt?: string | null;
  isEligibleForRefund?: boolean;
  // Additional date fields for better display
  subscriptionStartDate?: string | null; // When subscription starts (especially for trial users)
  subscriptionChargeTime?: string | null; // When the next payment will be charged
  trialEndDate?: string | null; // When the trial period ends
}

export default function ModernSubscriptionStatusCard({
  subscriptionId: _subscriptionId, // Using _subscriptionId to avoid linting error
  status,
  planName,
  planCycle,
  amount,
  currency: _currency = "INR", // Prefix with underscore to indicate it's unused
  nextBillingDate,
  lastPaymentDate,
  lastPaymentMethod,
  createdAt,
  expiresAt, // Current period end date
  pausedAt: _pausedAt, // Using _pausedAt to avoid linting error
  cancellationRequestedAt: _cancellationRequestedAt, // Using _cancellationRequestedAt to avoid linting error
  cancelledAt,
  isEligibleForRefund,
  // Additional date fields
  subscriptionStartDate,
  subscriptionChargeTime,
  trialEndDate
}: ModernSubscriptionStatusCardProps) {
  // Format dates for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "Not available";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-IN", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (_error) {
      return "Invalid date";
    }
  };

  // Format relative time (e.g., "2 days ago")
  const formatRelativeTime = (dateString: string | null | undefined) => {
    if (!dateString) return "";
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (_error) {
      return "";
    }
  };

  // Get payment method info
  const getPaymentMethodInfo = (method: string | null | undefined) => {
    // For authenticated subscriptions or when method is not available, show "Pending" instead of "Unknown"
    if (!method) {
      if (status === SUBSCRIPTION_STATUS.AUTHENTICATED) {
        return { icon: <Clock className="h-4 w-4 text-blue-500" />, text: "Pending" };
      }
      return { icon: <CreditCard className="h-4 w-4" />, text: "Not available" };
    }

    const methodLower = method.toLowerCase();

    if (methodLower === "card") {
      return { icon: <CreditCard className="h-4 w-4 text-blue-500" />, text: "Credit/Debit Card" };
    } else if (methodLower === "upi") {
      return { icon: <CreditCard className="h-4 w-4 text-green-500" />, text: "UPI" };
    } else if (methodLower === "netbanking") {
      return { icon: <CreditCard className="h-4 w-4 text-purple-500" />, text: "Net Banking" };
    } else if (methodLower === "wallet") {
      return { icon: <CreditCard className="h-4 w-4 text-orange-500" />, text: "Wallet" };
    } else {
      return { icon: <CreditCard className="h-4 w-4" />, text: method };
    }
  };

  // Get status info (color, icon, text) using centralized constants
  const getStatusInfo = (status: SubscriptionStatusType) => {
    switch (status) {
      case SUBSCRIPTION_STATUS.ACTIVE:
        return {
          color: "bg-green-500",
          textColor: "text-green-700 dark:text-green-300",
          borderColor: "border-green-200 dark:border-green-800",
          bgColor: "bg-green-50 dark:bg-green-900/20",
          icon: <CheckCircle2 className="h-5 w-5 text-green-500" />,
          text: "Active",
          description: "Your subscription is active and all features are available."
        };
      case SUBSCRIPTION_STATUS.AUTHENTICATED:
        return {
          color: "bg-blue-500",
          textColor: "text-blue-700 dark:text-blue-300",
          borderColor: "border-blue-200 dark:border-blue-800",
          bgColor: "bg-blue-50 dark:bg-blue-900/20",
          icon: <CheckCircle2 className="h-5 w-5 text-blue-500" />,
          text: "Authenticated",
          description: "Your subscription has been authenticated and will be activated soon."
        };
      case SUBSCRIPTION_STATUS.PENDING:
        return {
          color: "bg-yellow-500",
          textColor: "text-yellow-700 dark:text-yellow-300",
          borderColor: "border-yellow-200 dark:border-yellow-800",
          bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
          icon: <Clock className="h-5 w-5 text-yellow-500" />,
          text: "Pending",
          description: "Your subscription is pending activation."
        };
      case SUBSCRIPTION_STATUS.HALTED:
      case "paused":
        return {
          color: "bg-orange-500",
          textColor: "text-orange-700 dark:text-orange-300",
          borderColor: "border-orange-200 dark:border-orange-800",
          bgColor: "bg-orange-50 dark:bg-orange-900/20",
          icon: <PauseCircle className="h-5 w-5 text-orange-500" />,
          text: "Paused",
          description: "Your subscription has been temporarily paused."
        };
      case SUBSCRIPTION_STATUS.CANCELLED:
        return {
          color: "bg-red-500",
          textColor: "text-red-700 dark:text-red-300",
          borderColor: "border-red-200 dark:border-red-800",
          bgColor: "bg-red-50 dark:bg-red-900/20",
          icon: <XCircle className="h-5 w-5 text-red-500" />,
          text: "Cancelled",
          description: "Your subscription has been cancelled."
        };
      default:
        return {
          color: "bg-gray-500",
          textColor: "text-gray-700 dark:text-gray-300",
          borderColor: "border-gray-200 dark:border-gray-800",
          bgColor: "bg-gray-50 dark:bg-gray-900/20",
          icon: <AlertTriangle className="h-5 w-5 text-gray-500" />,
          text: "Unknown",
          description: "Subscription status is unknown."
        };
    }
  };

  const statusInfo = getStatusInfo(status);
  const paymentMethodInfo = getPaymentMethodInfo(lastPaymentMethod);

  // No timeline events needed for this simplified version

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full"
      >
        <div className="overflow-hidden bg-white dark:bg-black border border-neutral-200/80 dark:border-neutral-800/80 rounded-lg relative">
          {/* Background gradient effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent dark:from-blue-500/10 dark:to-transparent pointer-events-none"></div>

          <div className="p-0 relative z-10">
            {/* Modern Status Header */}
            <div className="p-6 bg-white dark:bg-black border-b border-neutral-200/80 dark:border-neutral-800/80">
              <div className="flex flex-col space-y-6">
                {/* Subscription Title with Icon */}
                <div className="flex items-center gap-3">
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    className={`w-12 h-12 rounded-xl flex items-center justify-center ${statusInfo.color} bg-opacity-20 dark:bg-opacity-30`}
                  >
                    <div className="text-[var(--primary)]">{statusInfo.icon}</div>
                  </motion.div>
                  <div>
                    <h3 className="text-xl font-semibold">Your Subscription</h3>
                    <p className="text-sm text-muted-foreground">{statusInfo.description}</p>
                  </div>
                </div>

                {/* Plan Details Card */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 p-4 rounded-xl bg-white/50 dark:bg-black/30 border border-neutral-200/80 dark:border-neutral-800/80">
                  <div className="flex flex-col space-y-1">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider">Plan</span>
                    <span className="font-medium">{planName} ({planCycle})</span>
                  </div>
                  <div className="flex flex-col space-y-1">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider">Amount</span>
                    <span className="font-medium">₹ {amount.toLocaleString()}</span>
                  </div>
                  <div className="flex flex-col space-y-1">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider">Payment Method</span>
                    <span className="font-medium flex items-center gap-1">
                      {paymentMethodInfo.icon} {paymentMethodInfo.text}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Current Status Section */}
            <div className="p-6">
              <motion.div
                className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white/90 to-white/70 dark:from-black/90 dark:to-black/70 border border-neutral-200/80 dark:border-neutral-800/80"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {/* Randomly moving blurred circles with collision avoidance */}
                <div className="absolute inset-0 overflow-hidden">
                  {/* Main circle - larger and more prominent */}
                  <motion.div
                    className={`absolute rounded-full ${statusInfo.color} blur-md`}
                    initial={{
                      x: "50%",
                      y: "30%",
                      scale: 1,
                      opacity: 0.6
                    }}
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [0.6, 0.7, 0.6]
                    }}
                    transition={{
                      repeat: Infinity,
                      repeatType: "reverse",
                      duration: 8,
                      ease: "easeInOut"
                    }}
                    style={{
                      width: "120px",
                      height: "120px",
                      transform: "translate(-50%, -50%)"
                    }}
                  />

                  {/* Randomly moving circles with collision avoidance */}
                  {Array.from({ length: 12 }).map((_, i) => {
                    // Generate random properties for each circle
                    const size = Math.random() * 80 + 20; // 20-100px
                    const opacity = Math.random() * 0.3 + 0.1; // 0.1-0.4 opacity

                    // Define starting positions that cover the entire container
                    // Each circle starts at a different position spread across the container
                    let startX, startY;

                    // Distribute circles evenly across the container initially
                    if (i < 4) {
                      // Top section
                      startX = Math.random() * 100; // 0-100%
                      startY = Math.random() * 30; // 0-30%
                    } else if (i < 8) {
                      // Middle section
                      startX = Math.random() * 100; // 0-100%
                      startY = 30 + Math.random() * 40; // 30-70%
                    } else {
                      // Bottom section
                      startX = Math.random() * 100; // 0-100%
                      startY = 70 + Math.random() * 30; // 70-100%
                    }

                    // Generate multiple random waypoints for each circle to create complex paths
                    const numWaypoints = 3 + Math.floor(Math.random() * 3); // 3-5 waypoints
                    const xWaypoints = Array.from({ length: numWaypoints }, () => Math.random() * 100);
                    const yWaypoints = Array.from({ length: numWaypoints }, () => Math.random() * 100);

                    // Random duration for movement - vary by size (smaller = faster)
                    const speedFactor = 1 - (size - 20) / 80; // 1 for smallest, 0 for largest
                    const duration = 15 + (Math.random() * 20 + 15) * (1 - speedFactor * 0.5); // 15-50 seconds

                    // Random delay before animation starts
                    const delay = Math.random() * 5; // 0-5 seconds

                    // Random blur amount
                    const blurAmount = Math.floor(Math.random() * 3) + 1; // 1-3
                    const blurClass = `blur-${blurAmount === 1 ? 'md' : blurAmount === 2 ? 'lg' : 'xl'}`;

                    return (
                      <motion.div
                        key={`circle-${i}`}
                        className={`absolute rounded-full ${statusInfo.color} ${blurClass}`}
                        initial={{
                          left: `${startX}%`,
                          top: `${startY}%`,
                          opacity: opacity,
                          x: "0%",
                          y: "0%"
                        }}
                        animate={{
                          x: xWaypoints.map(x => `${x - startX}%`),
                          y: yWaypoints.map(y => `${y - startY}%`),
                          opacity: [opacity, ...Array(numWaypoints - 1).fill(opacity * 1.2), opacity],
                          scale: [1, ...Array(numWaypoints - 1).fill(0).map(() => 1 + Math.random() * 0.3), 1],
                        }}
                        transition={{
                          repeat: Infinity,
                          repeatType: "reverse",
                          duration: duration,
                          ease: "easeInOut",
                          delay: delay,
                          times: Array.from({ length: numWaypoints + 1 }, (_, i) => i / numWaypoints)
                        }}
                        style={{
                          width: `${size}px`,
                          height: `${size}px`,
                          zIndex: Math.floor(Math.random() * 10),
                        }}
                      />
                    );
                  })}

                  {/* Additional floating particles */}
                  {Array.from({ length: 8 }).map((_, i) => {
                    const size = Math.random() * 15 + 5; // 5-20px

                    // Distribute particles evenly across the container
                    let startX, startY;
                    if (i < 2) {
                      // Top left
                      startX = Math.random() * 40; // 0-40%
                      startY = Math.random() * 40; // 0-40%
                    } else if (i < 4) {
                      // Top right
                      startX = 60 + Math.random() * 40; // 60-100%
                      startY = Math.random() * 40; // 0-40%
                    } else if (i < 6) {
                      // Bottom left
                      startX = Math.random() * 40; // 0-40%
                      startY = 60 + Math.random() * 40; // 60-100%
                    } else {
                      // Bottom right
                      startX = 60 + Math.random() * 40; // 60-100%
                      startY = 60 + Math.random() * 40; // 60-100%
                    }

                    // Generate random waypoints for complex movement
                    const numWaypoints = 4; // 4 waypoints for a complex path
                    const xWaypoints = Array.from({ length: numWaypoints }, () => Math.random() * 100);
                    const yWaypoints = Array.from({ length: numWaypoints }, () => Math.random() * 100);

                    const duration = Math.random() * 15 + 10; // 10-25 seconds

                    return (
                      <motion.div
                        key={`particle-${i}`}
                        className={`absolute rounded-full ${statusInfo.color} blur-sm`}
                        initial={{
                          left: `${startX}%`,
                          top: `${startY}%`,
                          opacity: 0.2,
                          x: "0%",
                          y: "0%"
                        }}
                        animate={{
                          x: xWaypoints.map(x => `${x - startX}%`),
                          y: yWaypoints.map(y => `${y - startY}%`),
                          opacity: [0.1, 0.3, 0.2, 0.3, 0.1],
                        }}
                        transition={{
                          repeat: Infinity,
                          repeatType: "mirror",
                          duration: duration,
                          ease: "easeInOut",
                          delay: Math.random() * 2
                        }}
                        style={{
                          width: `${size}px`,
                          height: `${size}px`,
                        }}
                      />
                    );
                  })}
                </div>

                {/* Content */}
                <div className="relative z-10 p-8 flex flex-col items-center text-center">
                  {/* Status Icon with Animation */}
                  <motion.div
                    className={`w-24 h-24 rounded-full ${statusInfo.color} flex items-center justify-center mb-8 relative`}
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 15
                    }}
                  >
                    {/* Glow effect behind the icon */}
                    <div className={`absolute inset-0 ${statusInfo.color} rounded-full blur-lg opacity-50`}></div>

                    {/* Pulsing ring animation */}
                    <motion.div
                      className={`absolute inset-0 ${statusInfo.color} rounded-full`}
                      initial={{ scale: 0.85, opacity: 0.5 }}
                      animate={{
                        scale: [0.85, 1.15, 0.85],
                        opacity: [0.5, 0, 0.5]
                      }}
                      transition={{
                        repeat: Infinity,
                        duration: 3,
                        ease: "easeInOut"
                      }}
                    />

                    <div className="text-white relative z-10">
                      <div className="text-[2.5rem]">{statusInfo.icon}</div>
                    </div>
                  </motion.div>

                  {/* Status Text */}
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="mb-4"
                  >
                    <h3 className="text-3xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80">{statusInfo.text}</h3>
                    <p className="text-muted-foreground text-base">{statusInfo.description}</p>
                  </motion.div>

                  {/* Important Dates */}
                  <motion.div
                    className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full mt-6"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    {/* Created Date */}
                    {createdAt && (
                      <div className="bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30">
                        <span className="text-xs text-muted-foreground uppercase tracking-wider mb-1">Created</span>
                        <span className="font-medium">{formatDate(createdAt)}</span>
                        <span className="text-xs text-muted-foreground">{formatRelativeTime(createdAt)}</span>
                      </div>
                    )}

                    {/* Cancelled Date */}
                    {status === SUBSCRIPTION_STATUS.CANCELLED && cancelledAt && (
                      <div className="bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30">
                        <span className="text-xs text-muted-foreground uppercase tracking-wider mb-1">Cancelled</span>
                        <span className="font-medium">{formatDate(cancelledAt)}</span>
                        <span className="text-xs text-muted-foreground">{formatRelativeTime(cancelledAt)}</span>
                      </div>
                    )}

                    {/* Last Payment Date */}
                    {status === SUBSCRIPTION_STATUS.ACTIVE && lastPaymentDate && (
                      <div className="bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30">
                        <span className="text-xs text-muted-foreground uppercase tracking-wider mb-1">Last Payment</span>
                        <span className="font-medium">{formatDate(lastPaymentDate)}</span>
                        <span className="text-xs text-muted-foreground">{formatRelativeTime(lastPaymentDate)}</span>
                      </div>
                    )}

                    {/* Refund Eligibility */}
                    {status === SUBSCRIPTION_STATUS.ACTIVE && isEligibleForRefund && (
                      <div className="bg-purple-50/30 dark:bg-purple-900/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-purple-100/30 dark:border-purple-800/30">
                        <span className="text-xs text-purple-600 dark:text-purple-400 uppercase tracking-wider mb-1">Refund Eligible</span>
                        <span className="font-medium text-purple-700 dark:text-purple-300">7-Day Refund Window</span>
                        <span className="text-xs text-purple-600/80 dark:text-purple-400/80">Full refund available</span>
                      </div>
                    )}

                    {/* Trial End Date - For trial users with active trials */}
                    {trialEndDate && new Date(trialEndDate) > new Date() && (
                      <div className="bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30">
                        <span className="text-xs text-muted-foreground uppercase tracking-wider mb-1">Trial Ends</span>
                        <span className="font-medium">{formatDate(trialEndDate)}</span>
                        <span className="text-xs text-muted-foreground">{formatRelativeTime(trialEndDate)}</span>
                      </div>
                    )}

                    {/* Subscription Start Date - For authenticated subscriptions */}
                    {status === "authenticated" && subscriptionStartDate && (
                      <div className="bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30">
                        <span className="text-xs text-muted-foreground uppercase tracking-wider mb-1">Starts On</span>
                        <span className="font-medium">{formatDate(subscriptionStartDate)}</span>
                        <span className="text-xs text-muted-foreground">{formatRelativeTime(subscriptionStartDate)}</span>
                      </div>
                    )}

                    {/* Fallback to nextBillingDate if subscriptionStartDate is not available */}
                    {status === "authenticated" && !subscriptionStartDate && nextBillingDate && (
                      <div className="bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30">
                        <span className="text-xs text-muted-foreground uppercase tracking-wider mb-1">Starts On</span>
                        <span className="font-medium">{formatDate(nextBillingDate)}</span>
                        <span className="text-xs text-muted-foreground">{formatRelativeTime(nextBillingDate)}</span>
                      </div>
                    )}

                    {/* Next Charge Date - For authenticated subscriptions */}
                    {status === "authenticated" && subscriptionChargeTime && (
                      <div className="bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30">
                        <span className="text-xs text-muted-foreground uppercase tracking-wider mb-1">First Payment On</span>
                        <span className="font-medium">{formatDate(subscriptionChargeTime)}</span>
                        <span className="text-xs text-muted-foreground">{formatRelativeTime(subscriptionChargeTime)}</span>
                      </div>
                    )}

                    {/* Subscription Expiry Date - For all active/authenticated subscriptions */}
                    {(status === SUBSCRIPTION_STATUS.ACTIVE || status === SUBSCRIPTION_STATUS.AUTHENTICATED) && expiresAt && (
                      <div className="bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30">
                        <span className="text-xs text-muted-foreground uppercase tracking-wider mb-1">Current Period Ends</span>
                        <span className="font-medium">{formatDate(expiresAt)}</span>
                        <span className="text-xs text-muted-foreground">{formatRelativeTime(expiresAt)}</span>
                      </div>
                    )}
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
