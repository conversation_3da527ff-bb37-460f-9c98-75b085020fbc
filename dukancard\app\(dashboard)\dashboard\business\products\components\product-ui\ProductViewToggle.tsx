"use client";

import { motion } from "framer-motion";
import { LayoutGrid, Table2, Filter, Infinity as InfinityIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Tabs,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

import { useProducts } from "../../context/ProductsContext";
import { ProductItemsPerPageSelector } from "./index";

export default function ProductViewToggle() {
  const {
    viewType,
    setViewType,
    products,
    totalCount,
    itemsPerPage,
    setItemsPerPage,
    planLimit,
    getFilterStatusText,
    currentPage,
    isLoading
  } = useProducts();

  // Calculate display range
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalCount);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 py-6"
    >
      {/* Left side - Product count and filters */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-3">
        <div className="text-sm text-neutral-700 dark:text-neutral-300">
          {products.length === 0 ? (
            <span className="font-medium">No products found</span>
          ) : (
            <span>
              Showing <span className="font-semibold text-neutral-900 dark:text-neutral-100">{startItem}</span> to{" "}
              <span className="font-semibold text-neutral-900 dark:text-neutral-100">{endItem}</span> of{" "}
              <span className="font-semibold text-neutral-900 dark:text-neutral-100">{totalCount}</span> products
              {planLimit !== Infinity && (
                <span className="text-neutral-500 dark:text-neutral-400 ml-1">
                  (limit: {planLimit})
                </span>
              )}
            </span>
          )}
        </div>

        {/* Filter badges */}
        <div className="flex items-center gap-2">
          {getFilterStatusText() && (
            <Badge className="bg-primary/10 text-primary border-primary/20 text-xs font-medium px-2 py-1 rounded-full">
              <Filter className="w-3 h-3 mr-1" />
              Filtered
            </Badge>
          )}
          {planLimit === Infinity && (
            <Badge className="bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 dark:from-purple-900/30 dark:to-pink-900/30 dark:text-purple-400 border-0 text-xs font-medium px-2 py-1 rounded-full">
              <InfinityIcon className="w-3 h-3 mr-1" />
              Unlimited Plan
            </Badge>
          )}
        </div>
      </div>

      {/* Right side - View toggle and items per page */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        {/* Items per page selector */}
        <ProductItemsPerPageSelector
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={setItemsPerPage}
          isLoading={isLoading}
        />

        {/* View toggle */}
        <Tabs value={viewType} onValueChange={(value) => setViewType(value as "table" | "grid")} className="w-auto">
          <TabsList className="grid w-40 grid-cols-2 bg-white dark:bg-neutral-900 border border-neutral-200/60 dark:border-neutral-800/60 rounded-xl p-1 shadow-sm">
            <TabsTrigger
              value="table"
              className="text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-all duration-200 px-3 py-2 flex items-center justify-center gap-2"
            >
              <Table2 className="w-4 h-4" />
              <span>Table</span>
            </TabsTrigger>
            <TabsTrigger
              value="grid"
              className="text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-all duration-200 px-3 py-2 flex items-center justify-center gap-2"
            >
              <LayoutGrid className="w-4 h-4" />
              <span>Grid</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
    </motion.div>
  );
}
