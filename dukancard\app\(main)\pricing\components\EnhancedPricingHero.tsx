"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import AnimatedTitle from "./animations/AnimatedTitle";
import { Spark<PERSON>, <PERSON><PERSON>C<PERSON><PERSON>, <PERSON> } from "lucide-react";

export default function EnhancedPricingHero() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.3 });

  return (
    <section
      ref={sectionRef}
      className="relative py-8 md:py-12 px-4 overflow-hidden"
    >
      {/* Decorative elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        {/* Animated sparkles */}
        {isInView && (
          <>
            <motion.div
              className="absolute top-1/4 left-[15%] text-[var(--brand-gold)] opacity-30"
              initial={{ scale: 0, rotate: 0 }}
              animate={{ scale: 1, rotate: 360 }}
              transition={{ duration: 1.5, ease: "backOut" }}
            >
              <Sparkles size={24} />
            </motion.div>

            <motion.div
              className="absolute top-1/3 right-[20%] text-[var(--brand-gold)] opacity-30"
              initial={{ scale: 0, rotate: 0 }}
              animate={{ scale: 1, rotate: -360 }}
              transition={{ duration: 1.5, delay: 0.2, ease: "backOut" }}
            >
              <Sparkles size={32} />
            </motion.div>

            <motion.div
              className="absolute bottom-1/4 left-[25%] text-[var(--brand-gold)] opacity-30"
              initial={{ scale: 0, rotate: 0 }}
              animate={{ scale: 1, rotate: 360 }}
              transition={{ duration: 1.5, delay: 0.4, ease: "backOut" }}
            >
              <Star size={28} />
            </motion.div>

            <motion.div
              className="absolute bottom-1/3 right-[15%] text-[var(--brand-gold)] opacity-30"
              initial={{ scale: 0, rotate: 0 }}
              animate={{ scale: 1, rotate: -360 }}
              transition={{ duration: 1.5, delay: 0.6, ease: "backOut" }}
            >
              <BadgeCheck size={24} />
            </motion.div>
          </>
        )}
      </div>

      <div className="max-w-5xl mx-auto">
        {/* Main content */}
        <div className="text-center mb-12">
          <AnimatedTitle
            title="Simple Transparent Pricing"
            highlightWords={["Transparent"]}
            subtitle="Choose the perfect plan for your business needs and elevate your digital presence with Dukancard's premium digital card solutions."
            className="mb-8"
          />

          {/* Feature badges */}
          <div className="flex flex-wrap justify-center gap-3 mt-8">
            {[
              { text: "Free Plan Available", icon: <BadgeCheck size={14} /> },
              { text: "No Hidden Fees", icon: <BadgeCheck size={14} /> },
              { text: "Cancel Anytime", icon: <BadgeCheck size={14} /> },
              { text: "Secure Payments", icon: <BadgeCheck size={14} /> },
            ].map((badge, index) => (
              <motion.div
                key={index}
                className="flex items-center gap-1.5 bg-background/80 backdrop-blur-sm border border-border px-3 py-1.5 rounded-full text-xs font-medium"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
              >
                <span className="text-[var(--brand-gold)]">{badge.icon}</span>
                <span>{badge.text}</span>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Animated card illustration */}
        <motion.div
          className="relative max-w-md mx-auto mt-12 mb-8"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={isInView ? { opacity: 1, scale: 1 } : {}}
          transition={{ duration: 0.7, delay: 0.3 }}
        >
          <div className="relative">
            {/* Card stack effect */}
            <div className="absolute top-4 left-1/2 -translate-x-1/2 w-[90%] h-32 bg-[var(--brand-gold)]/10 rounded-xl transform rotate-6"></div>
            <div className="absolute top-2 left-1/2 -translate-x-1/2 w-[95%] h-32 bg-[var(--brand-gold)]/20 rounded-xl transform -rotate-3"></div>

            {/* Main card */}
            <div className="relative bg-card border border-[var(--brand-gold)]/30 rounded-xl p-6 shadow-lg">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold">Dukancard</h3>
                  <p className="text-sm text-muted-foreground">Digital Business Card</p>
                </div>
                <div className="bg-[var(--brand-gold)] text-black dark:text-neutral-900 text-xs font-bold px-2 py-1 rounded">
                  PREMIUM
                </div>
              </div>

              <div className="space-y-3">
                <div className="h-2 bg-muted rounded-full w-full"></div>
                <div className="h-2 bg-muted rounded-full w-3/4"></div>
                <div className="h-2 bg-muted rounded-full w-1/2"></div>
              </div>

              <div className="mt-6 flex justify-between items-center">
                <div className="text-2xl font-bold">₹99<span className="text-sm font-normal text-muted-foreground">/mo</span></div>
                <motion.div
                  className="text-[var(--brand-gold)]"
                  animate={{ rotate: [0, 10, 0, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                >
                  <Sparkles size={20} />
                </motion.div>
              </div>
            </div>
          </div>

          {/* Animated glow effect */}
          <motion.div
            className="absolute inset-0 rounded-xl bg-[var(--brand-gold)]/20 blur-xl -z-10"
            animate={{
              opacity: [0.2, 0.5, 0.2],
              scale: [1, 1.05, 1]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        </motion.div>
      </div>
    </section>
  );
}
