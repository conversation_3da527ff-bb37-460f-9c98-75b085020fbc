"use client";

import { useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchLocalityCombined } from "../actions/combinedActions";
import { fetchMoreBusinessCardsByLocalityCombined } from "../actions/businessActions";
import { LOCALITY_BUSINESSES_PER_PAGE } from "../constants/paginationConstants";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import {
  BUSINESS_NAME_PARAM,
} from "@/app/(main)/discover/constants/urlParamConstants";
import { LocalitySearchResult, ViewType, SerializableLocality } from "./types";

export function useBusinessContextFunctions(
  locality: SerializableLocality,
  viewType: ViewType,
  setIsSearching: React.Dispatch<React.SetStateAction<boolean>>,
  setSearchResult: React.Dispatch<React.SetStateAction<LocalitySearchResult | null>>,
  setIsAuthenticated: React.Dispatch<React.SetStateAction<boolean>>,
  setBusinesses: React.Dispatch<React.SetStateAction<BusinessProfilePublicData[] | BusinessCardData[]>>,
  setHasMore: React.Dispatch<React.SetStateAction<boolean>>,
  setTotalCount: React.Dispatch<React.SetStateAction<number>>,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>,
  setSortBy: React.Dispatch<React.SetStateAction<BusinessSortBy>>,
  setSearchError: React.Dispatch<React.SetStateAction<string | null>>,
  businesses: BusinessProfilePublicData[] | BusinessCardData[],
  sortBy: BusinessSortBy
) {
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  // Handle business sort change
  const handleBusinessSortChange = (newSortBy: BusinessSortBy) => {
    setSortBy(newSortBy);
    startTransition(async () => {
      setIsSearching(true);
      setSearchError(null);

      try {
        const result = await searchLocalityCombined({
          localityName: locality.name,
          pincode: locality.pincode,
          viewType: "cards",
          sortBy: newSortBy,
          businessName: searchParams.get(BUSINESS_NAME_PARAM) || undefined,
        });

        if (result.error) {
          setSearchError(result.error);
          return;
        }

        if (result.data) {
          setSearchResult(result.data);
          setBusinesses(result.data.businesses || []);
          setIsAuthenticated(result.data.isAuthenticated);
          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(1);
        }
      } catch (error) {
        console.error("Error in handleBusinessSortChange:", error);
        setSearchError("An unexpected error occurred. Please try again.");
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Handle business search
  const handleBusinessSearch = async (businessName: string) => {
    setIsSearching(true);
    setSearchError(null);

    try {
      const result = await searchLocalityCombined({
        localityName: locality.name,
        pincode: locality.pincode,
        viewType: "cards",
        sortBy,
        businessName: businessName || undefined,
      });

      if (result.error) {
        setSearchError(result.error);
        return;
      }

      if (result.data) {
        setSearchResult(result.data);
        setBusinesses(result.data.businesses || []);
        setIsAuthenticated(result.data.isAuthenticated);
        setHasMore(result.data.hasMore);
        setTotalCount(result.data.totalCount);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error("Error in handleBusinessSearch:", error);
      setSearchError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  // Load more businesses
  const loadMoreBusinesses = async () => {
    if (viewType !== "cards") return;

    const nextPage = businesses.length > 0 ? Math.ceil(businesses.length / LOCALITY_BUSINESSES_PER_PAGE) + 1 : 1;

    try {
      const result = await fetchMoreBusinessCardsByLocalityCombined({
        localityName: locality.name,
        pincode: locality.pincode,
        page: nextPage,
        sortBy,
        businessName: searchParams.get(BUSINESS_NAME_PARAM) || undefined,
      });

      if (result.error) {
        setSearchError(result.error);
        return;
      }

      if (result.data) {
        setBusinesses((prev) => [...prev, ...(result.data?.businesses || [])] as typeof prev);
        setHasMore(result.data.hasMore);
        setCurrentPage(nextPage);
      }
    } catch (error) {
      console.error("Error in loadMoreBusinesses:", error);
      setSearchError("An unexpected error occurred. Please try again.");
    }
  };

  return {
    isPending,
    handleBusinessSortChange,
    handleBusinessSearch,
    loadMoreBusinesses,
  };
}
