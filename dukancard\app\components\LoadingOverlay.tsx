"use client";

import { Loader2, X } from "lucide-react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { cn } from "@/lib/utils";

interface LoadingOverlayProps {
  isOpen: boolean;
  message?: string;
  description?: string;
}

export default function LoadingOverlay({
  isOpen,
  message = "Processing your request...",
  description = "Please wait while we process your subscription",
}: LoadingOverlayProps) {
  if (!isOpen) return null;

  return (
    <DialogPrimitive.Root open={isOpen} modal>
      <DialogPrimitive.Portal>
        {/* Custom overlay that covers the entire viewport */}
        <DialogPrimitive.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />

        {/* Custom content with accessible title and non-functional close button */}
        <DialogPrimitive.Content
          className={cn(
            "fixed top-[50%] left-[50%] z-50 w-full max-w-md translate-x-[-50%] translate-y-[-50%]",
            "p-6 shadow-lg rounded-lg border border-neutral-200 dark:border-neutral-800",
            "bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm",
            "data-[state=open]:animate-in data-[state=closed]:animate-out",
            "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
            "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
            "duration-200"
          )}
        >
          {/* Accessible title for screen readers */}
          <DialogPrimitive.Title className="sr-only">
            Loading
          </DialogPrimitive.Title>

          {/* Accessible description for screen readers */}
          <DialogPrimitive.Description className="sr-only">
            Please wait while we process your request
          </DialogPrimitive.Description>

          {/* Non-functional close button */}
          <div className="absolute top-4 right-4 opacity-70">
            <button
              className="rounded-sm ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
              onClick={(e) => e.preventDefault()}
              aria-hidden="true"
              tabIndex={-1}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>

          <div className="relative flex flex-col items-center text-center">
            <div className="relative">
              <div className="absolute -inset-4 rounded-full bg-primary/10 animate-pulse blur-xl opacity-75" />
              <Loader2 className="h-12 w-12 animate-spin text-primary relative z-10" />
            </div>
            <h3 className="mt-6 text-lg font-medium text-foreground">
              {message}
            </h3>
            <p className="mt-2 text-sm text-muted-foreground">{description}</p>
          </div>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </DialogPrimitive.Root>
  );
}
