"use client";

import { motion } from "framer-motion";

interface AnimatedDividerProps {
  className?: string;
  width?: string;
  color?: "gold" | "purple" | "blue" | "neutral";
}

export default function AnimatedDivider({
  className = "",
  width = "w-24",
  color = "gold",
}: AnimatedDividerProps) {
  const getColorClass = () => {
    switch (color) {
      case "gold":
        return "bg-[var(--brand-gold)]";
      case "purple":
        return "bg-purple-500";
      case "blue":
        return "bg-blue-500";
      case "neutral":
        return "bg-neutral-300 dark:bg-neutral-700";
      default:
        return "bg-[var(--brand-gold)]";
    }
  };

  return (
    <div className={`flex justify-center my-6 ${className}`}>
      <motion.div
        className={`h-0.5 ${width} ${getColorClass()} rounded-full opacity-50`}
        initial={{ width: 0, opacity: 0 }}
        animate={{ width: "100%", opacity: 0.5 }}
        transition={{ duration: 0.8 }}
      />
    </div>
  );
}
