"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, Minus, HelpCircle } from "lucide-react";
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { PricingPlan } from "@/lib/PricingPlans";

interface FeatureComparisonTableProps {
  plans: PricingPlan[];
}

// Feature categories for grouping
const featureCategories = [
  { id: "core", name: "Core Features" },
  { id: "analytics", name: "Analytics & Insights" },
  { id: "customization", name: "Customization" },
  { id: "management", name: "Management" },
  { id: "support", name: "Support" },
];

// Feature definitions with categories and tooltips
const featureDefinitions = [
  // Core Features
  {
    id: "digital-card",
    name: "Digital Business Card",
    category: "core",
    tooltip: "Your professional digital business card with contact details and social links."
  },
  {
    id: "product-listings",
    name: "Product Listings",
    category: "core",
    tooltip: "Showcase your products or services with details and images."
  },
  {
    id: "ratings-reviews",
    name: "Ratings & Reviews",
    category: "core",
    tooltip: "Collect and display customer ratings and reviews."
  },
  {
    id: "customer-subscriptions",
    name: "Customer Subscriptions",
    category: "core",
    tooltip: "Allow visitors to subscribe to your business for updates."
  },
  {
    id: "like-feature",
    name: "Like Feature",
    category: "core",
    tooltip: "Let users like your storefront to show their appreciation."
  },

  // Analytics & Insights
  {
    id: "basic-analytics",
    name: "Basic Analytics (Views/Clicks)",
    category: "analytics",
    tooltip: "Track storefront views and contact button clicks."
  },
  {
    id: "enhanced-analytics",
    name: "Enhanced Analytics (Product Views)",
    category: "analytics",
    tooltip: "Track individual product views in addition to basic metrics."
  },
  {
    id: "advanced-analytics",
    name: "Advanced Analytics Dashboard",
    category: "analytics",
    tooltip: "Access detailed reports on rating trends and offer performance."
  },

  // Customization
  {
    id: "theme-customization",
    name: "Theme Customization",
    category: "customization",
    tooltip: "Personalize your storefront with custom theme colors and backgrounds."
  },

  // Management

  // Additional Features
  {
    id: "ad-free",
    name: "Ad-Free Storefront Option",
    category: "customization",
    tooltip: "Remove Dukancard branding and ads from your public storefront."
  },
  {
    id: "ecommerce",
    name: "E-commerce Orders",
    category: "management",
    tooltip: "Enable customers to add products to a cart and place orders."
  },
  {
    id: "white-label",
    name: "White-Label Option",
    category: "customization",
    tooltip: "Fully remove Dukancard branding for a seamless brand experience."
  },

  // Support
  {
    id: "support",
    name: "Support",
    category: "support",
    tooltip: "Customer support options available for your plan."
  },
];

export default function FeatureComparisonTable({ plans }: FeatureComparisonTableProps) {
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);

  const toggleCategory = (categoryId: string) => {
    if (expandedCategory === categoryId) {
      setExpandedCategory(null);
    } else {
      setExpandedCategory(categoryId);
    }
  };

  // Helper function to extract feature value from plan
  const getFeatureValue = (featureName: string, plan: PricingPlan) => {
    // Special case for team members and support which have specific values
    if (featureName === "Team Members") {
      if (plan.id === "basic" || plan.id === "growth") return "1 User";
      if (plan.id === "pro") return "Up to 2";
      if (plan.id === "enterprise") return "Up to 10";
      return <Minus className="w-5 h-5 text-muted-foreground mx-auto" />;
    }

    if (featureName === "Support") {
      if (plan.id === "basic" || plan.id === "growth") return "Email";
      if (plan.id === "pro") return "Priority Email/Chat";
      if (plan.id === "enterprise") return "Dedicated Manager";
      return <Minus className="w-5 h-5 text-muted-foreground mx-auto" />;
    }

    if (featureName === "Product Listings") {
      if (plan.id === "basic") return "15 Products";
      if (plan.id === "growth") return "50 Products";
      if (plan.id === "pro" || plan.id === "enterprise") return "Unlimited";
      return <Minus className="w-5 h-5 text-muted-foreground mx-auto" />;
    }

    // For other features, check if they exist in the plan's features array
    const featureExists = plan.features.some(f =>
      f.includes(featureName) && !f.includes("❌")
    );

    if (featureExists) {
      return <CheckCircle className="w-5 h-5 text-[var(--brand-gold)] mx-auto" />;
    } else {
      return <Minus className="w-5 h-5 text-muted-foreground mx-auto" />;
    }
  };

  return (
    <div className="w-full overflow-x-auto">
      <table className="w-full border-collapse">
        <thead className="sticky top-0 z-10">
          <tr className="border-b border-border bg-muted/80 dark:bg-muted/80 backdrop-blur-sm">
            <th className="p-4 text-left text-foreground font-semibold">
              Feature
            </th>
            {plans.map((plan) => (
              <th
                key={plan.id}
                className="p-4 text-center text-foreground font-semibold"
              >
                <div className={cn(
                  plan.featured ? "text-[var(--brand-gold)]" : "",
                  "flex flex-col items-center"
                )}>
                  <span>{plan.name.replace(" Plan", "")}</span>
                  {plan.mostPopular && (
                    <span className="text-xs mt-1 bg-[var(--brand-gold)]/20 text-[var(--brand-gold)] px-2 py-0.5 rounded-full">
                      Most Popular
                    </span>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {featureCategories.map((category) => {
            const categoryFeatures = featureDefinitions.filter(
              (f) => f.category === category.id
            );

            const isExpanded = expandedCategory === category.id;

            return (
              <React.Fragment key={category.id}>
                {/* Category header row */}
                <tr
                  className="border-b border-border bg-muted/30 dark:bg-muted/10 cursor-pointer hover:bg-muted/50"
                  onClick={() => toggleCategory(category.id)}
                >
                  <td
                    colSpan={plans.length + 1}
                    className="p-3 text-left font-medium text-foreground"
                  >
                    <div className="flex items-center">
                      <motion.div
                        animate={{ rotate: isExpanded ? 90 : 0 }}
                        transition={{ duration: 0.2 }}
                        className="mr-2"
                      >
                        ▶
                      </motion.div>
                      {category.name}
                    </div>
                  </td>
                </tr>

                {/* Feature rows for this category */}
                {isExpanded && categoryFeatures.map((feature) => (
                  <motion.tr
                    key={feature.id}
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="border-b border-border hover:bg-muted/20"
                  >
                    <td className="p-4 text-left text-foreground">
                      <div className="flex items-center">
                        {feature.name}
                        {feature.tooltip && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="w-4 h-4 ml-2 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent className="bg-popover border-border text-popover-foreground max-w-xs">
                                <p>{feature.tooltip}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </td>
                    {plans.map((plan) => (
                      <td key={`${plan.id}-${feature.id}`} className="p-4 text-center">
                        {getFeatureValue(feature.name, plan)}
                      </td>
                    ))}
                  </motion.tr>
                ))}
              </React.Fragment>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}
