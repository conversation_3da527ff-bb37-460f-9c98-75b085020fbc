"use client";

import EnhancedCardActions from "@/app/components/shared/EnhancedCardActions";

interface EnhancedPublicCardActionsProps {
  businessSlug: string;
  businessName: string;
  ownerName?: string;
  businessAddress?: string;
  themeColor?: string;
}

export default function EnhancedPublicCardActions({
  businessSlug,
  businessName,
  ownerName = "",
  businessAddress = "",
  themeColor = "#F59E0B",
}: EnhancedPublicCardActionsProps) {
  return (
    <EnhancedCardActions
      businessSlug={businessSlug}
      businessName={businessName}
      ownerName={ownerName}
      businessAddress={businessAddress}
      themeColor={themeColor}
    />
  );
}
