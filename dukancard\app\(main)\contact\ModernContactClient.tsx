"use client";

import { useState, useRef, useEffect } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { siteConfig } from "@/lib/site-config";
import SectionDivider from "../components/landing/SectionDivider";

// Import our components
import ContactHeroSection from "./components/ContactHeroSection";
import ContactInfoSection from "./components/ContactInfoSection";
import ContactMapSection from "./components/ContactMapSection";
import ContactCTASection from "./components/ContactCTASection";
import ContactAnimatedBackground from "./components/animations/ContactAnimatedBackground";

export default function ModernContactClient() {
  const [isLoaded, setIsLoaded] = useState(false);
  const pageRef = useRef<HTMLDivElement>(null);

  // Scroll-based animations
  const { scrollYProgress } = useScroll({
    target: pageRef,
    offset: ["start start", "end start"],
  });

  const backgroundOpacity = useTransform(
    scrollYProgress,
    [0, 0.2, 0.8, 1],
    [1, 0.8, 0.4, 0.2]
  );

  // Animation variants for sections
  const sectionFadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2,
      },
    },
  };

  // Animation variants for items within sections
  const itemFadeIn = (delay = 0) => ({
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: delay * 0.1,
      },
    },
  });

  // Handle initial animation on page load
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div
      ref={pageRef}
      className="min-h-screen bg-white dark:bg-black text-black dark:text-white overflow-hidden"
    >
      {/* Animated background with adjustable opacity based on scroll */}
      <motion.div
        className="fixed inset-0 z-0 pointer-events-none"
        style={{ opacity: backgroundOpacity }}
      >
        <ContactAnimatedBackground />
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoaded ? 1 : 0 }}
        transition={{ duration: 0.5 }}
        className="w-full pt-8 sm:pt-10 md:pt-12 mt-2 sm:mt-3 md:mt-4"
      >
        {/* Hero Section */}
        <ContactHeroSection />

        {/* Section Divider */}
        <SectionDivider variant="gold" />

        {/* Contact Information Section */}
        <ContactInfoSection
          contactInfo={siteConfig.contact}
          sectionFadeIn={sectionFadeIn}
          itemFadeIn={itemFadeIn}
        />

        {/* Section Divider */}
        <SectionDivider variant="gold" />

        {/* Map Section */}
        <ContactMapSection
          address={siteConfig.contact.address}
          sectionFadeIn={sectionFadeIn}
          itemFadeIn={itemFadeIn}
        />

        {/* Section Divider */}
        <SectionDivider variant="blue" />

        {/* CTA Section */}
        <ContactCTASection
          sectionFadeIn={sectionFadeIn}
          itemFadeIn={itemFadeIn}
        />
      </motion.div>
    </div>
  );
}
