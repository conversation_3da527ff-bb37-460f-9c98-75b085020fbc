import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { EmailOTPForm } from '@/app/(main)/login/components/EmailOTPForm';

describe('EmailOTPForm', () => {
  const onEmailSubmit = jest.fn();
  const onOTPSubmit = jest.fn();
  const onResendOTP = jest.fn();
  const onBackToEmail = jest.fn();

  it('renders email form when step is email', () => {
    render(
      <EmailOTPForm
        step="email"
        email=""
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
  });

  it('renders OTP form when step is otp', () => {
    render(
      <EmailOTPForm
        step="otp"
        email="<EMAIL>"
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    expect(screen.getByText(/enter verification code/i)).toBeInTheDocument();
  });

  it('calls onEmailSubmit with correct values', async () => {
    render(
      <EmailOTPForm
        step="email"
        email=""
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: /continue/i }));
    await waitFor(() => {
      expect(onEmailSubmit).toHaveBeenCalledWith({ email: '<EMAIL>' }, expect.anything());
    });
  });
});