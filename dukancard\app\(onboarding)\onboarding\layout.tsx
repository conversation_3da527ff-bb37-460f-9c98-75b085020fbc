import React from "react";
import { Metada<PERSON> } from "next";

// Minimal layout for the onboarding process, excluding global header/footer.

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Onboarding", // Uses template: "Onboarding - Dukancard"
    description: "Complete your Dukancard setup.",
    // Prevent indexing of onboarding pages
    robots: "noindex, nofollow",
  };
}

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {children}
    </>
  );
}
