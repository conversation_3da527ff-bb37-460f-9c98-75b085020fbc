import { handleMultipleImageUpload } from '@/app/(dashboard)/dashboard/business/products/actions/imageHandlers';
import { createAdminClient } from '@/utils/supabase/admin';

// Mock dependencies
jest.mock('@/utils/supabase/admin', () => ({
  createAdminClient: jest.fn(),
}));

const mockAdminSupabase = createAdminClient as jest.Mock;

describe('Image Handlers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleMultipleImageUpload', () => {
    it('should return an error for invalid userId', async () => {
      // Act
      const result = await handleMultipleImageUpload('', 'prod-123', []);

      // Assert
      expect(result.error).toContain('Invalid userId');
    });

    it('should upload images successfully', async () => {
      // Arrange
      const mockUpload = jest.fn().mockResolvedValue({ data: { path: 'test-path' }, error: null });
      const mockGetPublicUrl = jest.fn().mockReturnValue({ data: { publicUrl: 'http://example.com/image.png' } });
      const mockFrom = jest.fn().mockReturnValue({
        upload: mockUpload,
        getPublicUrl: mockGetPublicUrl,
      });

      mockAdminSupabase.mockReturnValue({
        storage: {
          from: mockFrom,
        },
      });

      // Create a proper File mock with arrayBuffer method
      const imageFile = new File(['test content'], 'test.png', { type: 'image/png' });
      // Add the arrayBuffer method that the function expects
      imageFile.arrayBuffer = jest.fn().mockResolvedValue(new ArrayBuffer(8));

      // Act
      const result = await handleMultipleImageUpload('user-123', 'prod-123', [imageFile]);

      // Assert
      expect(result.urls).toHaveLength(1);
      expect(result.urls[0]).toBe('http://example.com/image.png');
      expect(mockUpload).toHaveBeenCalled();
      expect(mockGetPublicUrl).toHaveBeenCalled();
    });
  });
});