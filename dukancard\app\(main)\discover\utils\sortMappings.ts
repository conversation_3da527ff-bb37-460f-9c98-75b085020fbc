import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { BusinessSortOption, ProductSortOption } from "../context/types";

/**
 * Gets the sorting column from a sort option
 */
export function getSortingColumn(sortBy: string): string {
  // Extract the column name from the sort option (e.g., "created_desc" -> "created_at")
  const parts = sortBy.split('_');
  if (parts.length < 2) return 'created_at';

  // Special cases
  if (parts[0] === 'likes') return 'total_likes';
  if (parts[0] === 'subscriptions') return 'total_subscriptions';
  if (parts[0] === 'rating') return 'average_rating';
  if (parts[0] === 'newest') return 'created_at'; // Handle 'newest' as a special case

  // Standard cases
  if (parts[0] === 'created') return 'created_at';
  if (parts[0] === 'name') return 'business_name';
  if (parts[0] === 'price') return 'price';

  return parts[0];
}

/**
 * Gets the sorting direction from a sort option
 */
export function getSortingDirection(sortBy: string): boolean {
  // Extract the direction from the sort option (e.g., "created_desc" -> false for descending)
  return !sortBy.endsWith('_desc');
}

/**
 * Maps a product sort option from the UI to the backend API sort parameter
 */
export function mapProductSortToBackend(
  sortOption: ProductSortOption
): BusinessSortBy | "price_asc" | "price_desc" {
  switch (sortOption) {
    case "newest":
      return "created_desc";
    case "name_asc":
      return "name_asc";
    case "name_desc":
      return "name_desc";
    case "price_low":
      return "price_asc"; // Map to price_asc for proper sorting
    case "price_high":
      return "price_desc"; // Map to price_desc for proper sorting
    default:
      return "created_desc";
  }
}

/**
 * Maps a business sort option from the UI to the backend API sort parameter
 */
export function mapBusinessSortToBackend(
  sortOption: BusinessSortOption
): BusinessSortBy {
  switch (sortOption) {
    case "newest":
      return "created_desc";
    case "name_asc":
      return "name_asc";
    case "name_desc":
      return "name_desc";
    case "most_liked":
      return "likes_desc";
    case "most_subscribed":
      return "subscriptions_desc";
    case "highest_rated":
      return "rating_desc";
    default:
      return "created_desc";
  }
}

/**
 * Maps a backend API sort parameter to a product sort option for the UI
 */
export function mapBackendToProductSort(
  backendSort: string
): ProductSortOption {
  switch (backendSort) {
    case "created_desc":
      return "newest";
    case "name_asc":
      return "name_asc";
    case "name_desc":
      return "name_desc";
    case "price_asc":
      return "price_low";
    case "price_desc":
      return "price_high";
    default:
      return "newest";
  }
}

/**
 * Maps a backend API sort parameter to a business sort option for the UI
 */
export function mapBackendToBusinessSort(
  backendSort: BusinessSortBy
): BusinessSortOption {
  switch (backendSort) {
    case "created_desc":
      return "newest";
    case "name_asc":
      return "name_asc";
    case "name_desc":
      return "name_desc";
    case "likes_desc":
      return "most_liked";
    case "subscriptions_desc":
      return "most_subscribed";
    case "rating_desc":
      return "highest_rated";
    default:
      return "newest";
  }
}
