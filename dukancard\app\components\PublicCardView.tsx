"use client";

import BusinessCardPreview from "@/app/(dashboard)/dashboard/business/card/components/BusinessCardPreview";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { UserPlanStatus } from "@/app/(dashboard)/dashboard/business/card/CardEditorClient"; // Import type

interface PublicCardViewProps {
  // Now receives the final data (potentially masked by the server component)
  cardData: BusinessCardData;
  // Still needs the plan to determine branding/logo display in preview
  currentUserPlan: UserPlanStatus;
  // Interaction props
  isAuthenticated?: boolean;
  totalLikes?: number;
  totalSubscriptions?: number;
  averageRating?: number;
  isSubscribed?: boolean;
  hasLiked?: boolean;
  isLoadingInteraction?: boolean;
}

// This component is now purely for presentation
export default function PublicCardView({
  cardData,
  currentUserPlan,
  isAuthenticated = false,
  totalLikes = 0,
  totalSubscriptions = 0,
  averageRating = 0,
  isSubscribed = false,
  hasLiked = false,
  isLoadingInteraction = false,
}: PublicCardViewProps) {
  return (
    // The parent page (app/[slug]/page.tsx) handles centering and background
    <BusinessCardPreview
      data={cardData} // Pass the received data directly
      userPlan={
        currentUserPlan === "trial" ? "basic" : currentUserPlan ?? undefined
      }
      isAuthenticated={isAuthenticated}
      totalLikes={totalLikes}
      totalSubscriptions={totalSubscriptions}
      averageRating={averageRating}
      isSubscribed={isSubscribed}
      hasLiked={hasLiked}
      isLoadingInteraction={isLoadingInteraction}
    />
  );
}
