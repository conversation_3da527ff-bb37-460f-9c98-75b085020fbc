"use client";

import { useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchLocalityCombined } from "../actions/combinedActions";
import { fetchMoreProductsByLocalityCombined } from "../actions/productActions";
import { LOCALITY_PRODUCTS_PER_PAGE } from "../constants/paginationConstants";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import {
  PRODUCT_NAME_PARAM
} from "@/app/(main)/discover/constants/urlParamConstants";
import {
  LocalitySearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
  SerializableLocality,
} from "./types";
import { mapProductSortToBackend } from "@/app/(main)/discover/utils/sortMappings";

export function useProductContextFunctions(
  locality: SerializableLocality,
  viewType: ViewType,
  setIsSearching: React.Dispatch<React.SetStateAction<boolean>>,
  setSearchResult: React.Dispatch<React.SetStateAction<LocalitySearchResult | null>>,
  setIsAuthenticated: React.Dispatch<React.SetStateAction<boolean>>,
  setProducts: React.Dispatch<React.SetStateAction<NearbyProduct[]>>,
  setHasMore: React.Dispatch<React.SetStateAction<boolean>>,
  setTotalCount: React.Dispatch<React.SetStateAction<number>>,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>,
  setProductSortBy: React.Dispatch<React.SetStateAction<ProductSortOption>>,
  setProductFilterBy: React.Dispatch<React.SetStateAction<ProductFilterOption>>,
  setSearchError: React.Dispatch<React.SetStateAction<string | null>>,
  products: NearbyProduct[],
  sortBy: BusinessSortBy,
  productSortBy: ProductSortOption,
  productFilterBy: ProductFilterOption
) {
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  // Handle product sort change
  const handleProductSortChange = (newSortBy: ProductSortOption) => {
    setProductSortBy(newSortBy);
    startTransition(async () => {
      setIsSearching(true);
      setSearchError(null);

      try {
        const result = await searchLocalityCombined({
          localityName: locality.name,
          pincode: locality.pincode,
          viewType: "products",
          sortBy: mapProductSortToBackend(newSortBy),
          productType: productFilterBy === "all" ? null : productFilterBy,
          productName: searchParams.get(PRODUCT_NAME_PARAM) || undefined,
        });

        if (result.error) {
          setSearchError(result.error);
          return;
        }

        if (result.data) {
          setSearchResult(result.data);
          setProducts(result.data.products || []);
          setIsAuthenticated(result.data.isAuthenticated);
          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(1);
        }
      } catch (error) {
        console.error("Error in handleProductSortChange:", error);
        setSearchError("An unexpected error occurred. Please try again.");
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Handle product filter change
  const handleProductFilterChange = (newFilterBy: ProductFilterOption) => {
    setProductFilterBy(newFilterBy);
    startTransition(async () => {
      setIsSearching(true);
      setSearchError(null);

      try {
        const result = await searchLocalityCombined({
          localityName: locality.name,
          pincode: locality.pincode,
          viewType: "products",
          sortBy: mapProductSortToBackend(productSortBy),
          productType: newFilterBy === "all" ? null : newFilterBy,
          productName: searchParams.get(PRODUCT_NAME_PARAM) || undefined,
        });

        if (result.error) {
          setSearchError(result.error);
          return;
        }

        if (result.data) {
          setSearchResult(result.data);
          setProducts(result.data.products || []);
          setIsAuthenticated(result.data.isAuthenticated);
          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(1);
        }
      } catch (error) {
        console.error("Error in handleProductFilterChange:", error);
        setSearchError("An unexpected error occurred. Please try again.");
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Handle product search
  const handleProductSearch = async (productName: string) => {
    setIsSearching(true);
    setSearchError(null);

    try {
      const result = await searchLocalityCombined({
        localityName: locality.name,
        pincode: locality.pincode,
        viewType: "products",
        sortBy: mapProductSortToBackend(productSortBy),
        productType: productFilterBy === "all" ? null : productFilterBy,
        productName: productName || undefined,
      });

      if (result.error) {
        setSearchError(result.error);
        return;
      }

      if (result.data) {
        setSearchResult(result.data);
        setProducts(result.data.products || []);
        setIsAuthenticated(result.data.isAuthenticated);
        setHasMore(result.data.hasMore);
        setTotalCount(result.data.totalCount);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error("Error in handleProductSearch:", error);
      setSearchError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  // Load more products
  const loadMoreProducts = async () => {
    if (viewType !== "products") return;

    const nextPage = products.length > 0 ? Math.ceil(products.length / LOCALITY_PRODUCTS_PER_PAGE) + 1 : 1;

    try {
      const result = await fetchMoreProductsByLocalityCombined({
        localityName: locality.name,
        pincode: locality.pincode,
        page: nextPage,
        sortBy: mapProductSortToBackend(productSortBy),
        productType: productFilterBy === "all" ? null : productFilterBy,
        productName: searchParams.get(PRODUCT_NAME_PARAM) || undefined,
      });

      if (result.error) {
        setSearchError(result.error);
        return;
      }

      if (result.data) {
        setProducts((prev) => [...prev, ...(result.data?.products || [])]);
        setHasMore(result.data.hasMore);
        setCurrentPage(nextPage);
      }
    } catch (error) {
      console.error("Error in loadMoreProducts:", error);
      setSearchError("An unexpected error occurred. Please try again.");
    }
  };

  return {
    isPending,
    handleProductSortChange,
    handleProductSearch,
    handleProductFilterChange,
    loadMoreProducts,
  };
}
