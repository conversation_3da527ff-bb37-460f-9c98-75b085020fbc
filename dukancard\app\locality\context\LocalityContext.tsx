"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import {
  BUSINESS_SORT_PARAM,
  VIEW_TYPE_PARAM,
  PRODUCT_SORT_PARAM,
  PRODUCT_TYPE_PARAM,
} from "@/app/(main)/discover/constants/urlParamConstants";
import {
  LocalityContextType,
  LocalitySearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
  SerializableLocality,
} from "./types";
import { useBusinessContextFunctions } from "./businessContextFunctions";
import { useProductContextFunctions } from "./productContextFunctions";
import { useCommonContextFunctions } from "./commonContextFunctions";

// Create the context
const LocalityContext = createContext<LocalityContextType | undefined>(
  undefined
);

// Hook to use the locality context
export function useLocalityContext() {
  const context = useContext(LocalityContext);
  if (context === undefined) {
    throw new Error("useLocalityContext must be used within a LocalityProvider");
  }
  return context;
}

// Provider component
export function LocalityProvider({
  children,
  locality,
  initialBusinesses,
}: {
  children: React.ReactNode;
  locality: SerializableLocality;
  initialBusinesses: BusinessProfilePublicData[] | BusinessCardData[];
}) {
  const searchParams = useSearchParams();

  // Get initial view type from URL or default to "cards"
  const initialViewType = (searchParams.get(VIEW_TYPE_PARAM) as ViewType) || "cards";

  // Get initial sort by from URL or default to "created_desc"
  const initialSortBy = (searchParams.get(BUSINESS_SORT_PARAM) as BusinessSortBy) || "created_desc";

  // Get initial product sort by from URL or default to "newest"
  const initialProductSortBy = (searchParams.get(PRODUCT_SORT_PARAM) as ProductSortOption) || "newest";

  // Get initial product filter by from URL or default to "all"
  const initialProductFilterBy = (searchParams.get(PRODUCT_TYPE_PARAM) as ProductFilterOption) || "all";

  // State
  const [viewType, setViewType] = useState<ViewType>(initialViewType);
  const [sortBy, setSortBy] = useState<BusinessSortBy>(initialSortBy);
  const [productSortBy, setProductSortBy] = useState<ProductSortOption>(initialProductSortBy);
  const [productFilterBy, setProductFilterBy] = useState<ProductFilterOption>(initialProductFilterBy);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [searchResult, setSearchResult] = useState<LocalitySearchResult | null>(null);
  const [businesses, setBusinesses] = useState<BusinessProfilePublicData[] | BusinessCardData[]>(initialBusinesses);
  const [products, setProducts] = useState<NearbyProduct[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [totalCount, setTotalCount] = useState(initialBusinesses?.length || 0);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Get business context functions
  const { handleBusinessSortChange, handleBusinessSearch, loadMoreBusinesses } =
    useBusinessContextFunctions(
      locality,
      viewType,
      setIsSearching,
      setSearchResult,
      setIsAuthenticated,
      setBusinesses,
      setHasMore,
      setTotalCount,
      setCurrentPage,
      setSortBy,
      setSearchError,
      businesses,
      sortBy
    );

  // Get product context functions
  const {
    handleProductSortChange,
    handleProductSearch,
    handleProductFilterChange,
    loadMoreProducts,
  } = useProductContextFunctions(
    locality,
    viewType,
    setIsSearching,
    setSearchResult,
    setIsAuthenticated,
    setProducts,
    setHasMore,
    setTotalCount,
    setCurrentPage,
    setProductSortBy,
    setProductFilterBy,
    setSearchError,
    products,
    sortBy,
    productSortBy,
    productFilterBy
  );

  // Get common context functions
  const {
    isPending,
    handleViewChange,
    performSearch,
  } = useCommonContextFunctions(
    locality,
    viewType,
    setViewType,
    setIsSearching,
    setSearchResult,
    setIsAuthenticated,
    setBusinesses,
    setProducts,
    setHasMore,
    setTotalCount,
    setCurrentPage,
    setSearchError,
    sortBy,
    productSortBy,
    productFilterBy
  );

  // Load more items based on current view type
  const loadMore = async () => {
    if (isLoadingMore) return;
    setIsLoadingMore(true);

    try {
      if (viewType === "cards") {
        await loadMoreBusinesses();
      } else {
        await loadMoreProducts();
      }
    } catch (error) {
      console.error("Error in loadMore:", error);
      setSearchError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Initial search for products if view type is "products"
  useEffect(() => {
    if (viewType === "products" && products.length === 0 && !isSearching) {
      performSearch();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Create the context value
  const contextValue: LocalityContextType = {
    locality,
    viewType,
    sortBy,
    isSearching,
    isPending,
    isLoadingMore,
    searchError,
    productFilterBy,
    productSortBy,
    searchResult,
    businesses,
    products,
    currentPage,
    hasMore,
    totalCount,
    isAuthenticated,
    performSearch,
    handleViewChange,
    handleBusinessSortChange,
    handleBusinessSearch,
    handleProductSearch,
    handleProductSortChange,
    handleProductFilterChange,
    loadMore,
  };

  return (
    <LocalityContext.Provider value={contextValue}>
      {children}
    </LocalityContext.Provider>
  );
}
