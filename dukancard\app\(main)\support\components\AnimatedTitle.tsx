"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useRef } from "react";
import { useInView } from "framer-motion";

interface AnimatedTitleProps {
  title: string;
  highlightWords?: string[];
  className?: string;
  subtitle?: string;
  align?: "left" | "center" | "right";
  size?: "small" | "medium" | "large";
}

export default function AnimatedTitle({
  title,
  highlightWords = [],
  className,
  subtitle,
  align = "center",
  size = "large",
}: AnimatedTitleProps) {
  const titleRef = useRef<HTMLHeadingElement>(null);
  const isInView = useInView(titleRef, { once: true, amount: 0.3 });
  
  // Split the title into words
  const words = title.split(" ");

  // Determine text alignment class
  const alignClass = {
    left: "text-left",
    center: "text-center",
    right: "text-right",
  }[align];

  // Determine text size class
  const sizeClass = {
    small: "text-2xl md:text-3xl lg:text-4xl",
    medium: "text-3xl md:text-4xl lg:text-5xl",
    large: "text-4xl md:text-5xl lg:text-6xl",
  }[size];

  return (
    <div className={cn(alignClass, className)}>
      <h2 
        ref={titleRef}
        className={cn(
          sizeClass,
          "font-bold text-foreground tracking-tight leading-tight"
        )}
      >
        {words.map((word, i) => {
          const isHighlighted = highlightWords.some(hw => 
            word.toLowerCase().includes(hw.toLowerCase())
          );
          const delay = 0.1 + (i * 0.05);

          return (
            <span key={i} className="inline-block mr-[0.3em]">
              {isHighlighted ? (
                <motion.span
                  className="inline-block relative text-[var(--brand-gold)]"
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.5, delay }}
                >
                  {word}

                  {/* Animated underline */}
                  <motion.span
                    className="absolute bottom-0 left-0 h-1 bg-[var(--brand-gold)] rounded-full"
                    initial={{ width: 0 }}
                    animate={isInView ? { width: "100%" } : {}}
                    transition={{ duration: 0.5, delay: 0.5 + (i * 0.05) }}
                  />

                  {/* Glow effect */}
                  <motion.span
                    className="absolute inset-0 rounded-lg -z-10 bg-[var(--brand-gold)]/20"
                    animate={{ opacity: [0.2, 0.3, 0.2] }}
                    transition={{ duration: 3, repeat: Infinity, repeatType: "reverse" }}
                  />
                </motion.span>
              ) : (
                <motion.span
                  className="inline-block"
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.5, delay }}
                >
                  {word}
                </motion.span>
              )}
            </span>
          );
        })}
      </h2>

      {subtitle && (
        <motion.p
          className="text-lg md:text-xl text-muted-foreground mt-4 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 10 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5, delay: words.length * 0.05 + 0.2 }}
        >
          {subtitle}
          
          {/* Subtle highlight animation */}
          <motion.span
            className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 dark:via-white/5 to-transparent"
            initial={{ x: "-100%" }}
            animate={isInView ? { x: "100%" } : {}}
            transition={{ duration: 1.5, delay: words.length * 0.05 + 0.5, ease: "easeInOut" }}
          />
        </motion.p>
      )}
    </div>
  );
}
