"use client";

import { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  Shield,
  Database,
  UserCheck,
  Lock,
  Globe,
  Cookie,
  Clock,
  AlertCircle,
  Mail
} from "lucide-react";
import { Card } from "@/components/ui/card";
import PolicyHeroSection from "../components/policy/PolicyHeroSection";
import PolicySection from "../components/policy/PolicySection";
import PolicyNavigation from "../components/policy/PolicyNavigation";
import PolicyCTASection from "../components/policy/PolicyCTASection";
import SectionDivider from "../components/landing/SectionDivider";
import { siteConfig } from "@/lib/site-config";
import Link from "next/link";

// Navigation items for the policy
const navItems = [
  { id: "introduction", title: "Introduction" },
  { id: "data-collection", title: "Data We Collect" },
  { id: "data-usage", title: "How We Use Your Data" },
  { id: "data-sharing", title: "Data Sharing" },
  { id: "data-security", title: "Data Security" },
  { id: "cookies", title: "Cookies" },
  { id: "data-retention", title: "Data Retention" },
  { id: "policy-changes", title: "Policy Changes" },
  { id: "contact", title: "Contact Us" },
];

// Related links
const relatedLinks = [
  { title: "Terms of Service", href: "/terms" },
  { title: "Cookie Policy", href: "/cookies" },
  { title: "Refund Policy", href: "/refund" },
];

export default function ModernPrivacyPolicyClient() {
  const pageRef = useRef<HTMLDivElement>(null);

  // Removed scroll-based fade effect

  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div
      ref={pageRef}
      className="min-h-screen bg-white dark:bg-black"
    >
      <div className="relative">
        {/* Hero Section */}
        <PolicyHeroSection
          title="Privacy Policy"
          lastUpdated="May 19, 2025"
          variant="gold"
        />

        <div className="container mx-auto px-4 max-w-4xl pb-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left column: Navigation (sticky on desktop) */}
            <div className="w-full lg:w-1/4 order-1">
              <div className="lg:sticky lg:top-24 self-start">
                <PolicyNavigation items={navItems} />
              </div>
            </div>

            {/* Right column: Content */}
            <div className="w-full lg:w-3/4 order-2">
              <Card className="p-6 md:p-8 border border-border shadow-sm mb-8">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="prose prose-neutral dark:prose-invert max-w-none"
                >
                  <p className="text-lg">
                    Welcome to Dukancard. We respect your privacy and are committed to
                    protecting your personal data. This privacy policy will inform you
                    about how we look after your personal data when you visit our website
                    and tell you about your privacy rights and how the law protects you.
                  </p>
                </motion.div>
              </Card>

              {/* Introduction Section */}
              <PolicySection
                id="introduction"
                title="1. Introduction"
                icon={<Shield className="h-6 w-6" />}
                delay={0}
              >
                <p>
                  Welcome to Dukancard. We respect your privacy and are committed to
                  protecting your personal data. This privacy policy will inform you
                  about how we look after your personal data when you visit our website
                  and tell you about your privacy rights and how the law protects you.
                </p>
              </PolicySection>

              {/* Data Collection Section */}
              <PolicySection
                id="data-collection"
                title="2. Data We Collect"
                icon={<Database className="h-6 w-6" />}
                delay={1}
              >
                <p>
                  We may collect, use, store and transfer different kinds of personal
                  data about you which we have grouped together as follows:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>
                    <strong>Identity Data</strong> includes first name, last name,
                    username or similar identifier.
                  </li>
                  <li>
                    <strong>Contact Data</strong> includes email address, telephone
                    numbers, and physical address.
                  </li>
                  <li>
                    <strong>Technical Data</strong> includes internet protocol (IP)
                    address, browser type and version, time zone setting and location,
                    browser plug-in types and versions, operating system and platform,
                    and other technology on the devices you use to access this website.
                  </li>
                  <li>
                    <strong>Profile Data</strong> includes your username and password,
                    purchases or orders made by you, your interests, preferences,
                    feedback and survey responses.
                  </li>
                  <li>
                    <strong>Usage Data</strong> includes information about how you use
                    our website, products and services.
                  </li>
                </ul>
              </PolicySection>

              {/* Data Usage Section */}
              <PolicySection
                id="data-usage"
                title="3. How We Use Your Data"
                icon={<UserCheck className="h-6 w-6" />}
                delay={2}
              >
                <p>
                  We will only use your personal data when the law allows us to. Most
                  commonly, we will use your personal data in the following
                  circumstances:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>
                    Where we need to perform the contract we are about to enter into or
                    have entered into with you.
                  </li>
                  <li>
                    Where it is necessary for our legitimate interests (or those of a
                    third party) and your interests and fundamental rights do not
                    override those interests.
                  </li>
                  <li>Where we need to comply with a legal obligation.</li>
                  <li>
                    With your consent for specific purposes that we will inform you
                    about.
                  </li>
                </ul>
              </PolicySection>

              {/* Section Divider */}
              <SectionDivider variant="gold" className="my-12" />

              {/* Data Sharing Section */}
              <PolicySection
                id="data-sharing"
                title="4. Data Sharing"
                icon={<Globe className="h-6 w-6" />}
                delay={3}
              >
                <p>
                  We may share your personal data with the parties set out below for
                  the purposes outlined in this privacy policy:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>
                    Service providers who provide IT and system administration services.
                  </li>
                  <li>
                    Professional advisers including lawyers, bankers, auditors and
                    insurers.
                  </li>
                  <li>
                    Regulators and other authorities who require reporting of processing
                    activities in certain circumstances.
                  </li>
                  <li>
                    Third parties to whom we may choose to sell, transfer or merge parts
                    of our business or our assets.
                  </li>
                </ul>
                <p>
                  We require all third parties to respect the security of your personal
                  data and to treat it in accordance with the law. We do not allow our
                  third-party service providers to use your personal data for their own
                  purposes and only permit them to process your personal data for
                  specified purposes and in accordance with our instructions.
                </p>
              </PolicySection>

              {/* Data Security Section */}
              <PolicySection
                id="data-security"
                title="5. Data Security"
                icon={<Lock className="h-6 w-6" />}
                delay={4}
              >
                <p>
                  We have put in place appropriate security measures to prevent your
                  personal data from being accidentally lost, used or accessed in an
                  unauthorized way, altered or disclosed. In addition, we limit access
                  to your personal data to those employees, agents, contractors and
                  other third parties who have a business need to know.
                </p>
                <p>
                  We have put in place procedures to deal with any suspected personal
                  data breach and will notify you and any applicable regulator of a
                  breach where we are legally required to do so.
                </p>
              </PolicySection>

              {/* Cookies Section */}
              <PolicySection
                id="cookies"
                title="6. Cookies"
                icon={<Cookie className="h-6 w-6" />}
                delay={5}
              >
                <p>
                  We use cookies and similar tracking technologies to track the activity
                  on our Service and hold certain information. Cookies are files with a
                  small amount of data which may include an anonymous unique identifier.
                </p>
                <p>
                  You can instruct your browser to refuse all cookies or to indicate
                  when a cookie is being sent. However, if you do not accept cookies,
                  you may not be able to use some portions of our Service.
                </p>
                <p>
                  For more information about the cookies we use, please see our{" "}
                  <Link href="/cookies" className="text-[var(--brand-gold)] hover:underline">
                    Cookie Policy
                  </Link>
                  .
                </p>
              </PolicySection>

              {/* Data Retention Section */}
              <PolicySection
                id="data-retention"
                title="7. Data Retention"
                icon={<Clock className="h-6 w-6" />}
                delay={6}
              >
                <p>
                  We will only retain your personal data for as long as reasonably
                  necessary to fulfill the purposes we collected it for, including for
                  the purposes of satisfying any legal, regulatory, tax, accounting, or
                  reporting requirements.
                </p>
                <p>
                  To determine the appropriate retention period for personal data, we
                  consider the amount, nature, and sensitivity of the personal data,
                  the potential risk of harm from unauthorized use or disclosure of
                  your personal data, the purposes for which we process your personal
                  data and whether we can achieve those purposes through other means,
                  and the applicable legal, regulatory, tax, accounting, or other
                  requirements.
                </p>
              </PolicySection>

              {/* Policy Changes Section */}
              <PolicySection
                id="policy-changes"
                title="8. Changes to This Privacy Policy"
                icon={<AlertCircle className="h-6 w-6" />}
                delay={7}
              >
                <p>
                  We may update our Privacy Policy from time to time. We will notify you
                  of any changes by posting the new Privacy Policy on this page and
                  updating the &ldquo;Last Updated&rdquo; date at the top of this
                  Privacy Policy.
                </p>
                <p>
                  You are advised to review this Privacy Policy periodically for any
                  changes. Changes to this Privacy Policy are effective when they are
                  posted on this page.
                </p>
              </PolicySection>

              {/* Contact Us Section */}
              <PolicySection
                id="contact"
                title="9. Contact Us"
                icon={<Mail className="h-6 w-6" />}
                delay={8}
              >
                <p>
                  If you have any questions about this Privacy Policy, please contact
                  us:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>By email: {siteConfig.contact.email}</li>
                  <li>By phone: {siteConfig.contact.phone}</li>
                  <li>By mail: {siteConfig.contact.address.full}</li>
                </ul>
              </PolicySection>
            </div>
          </div>
        </div>

        {/* Section Divider */}
        <SectionDivider variant="blue" className="my-8" />

        {/* CTA Section */}
        <PolicyCTASection relatedLinks={relatedLinks} />
      </div>
    </div>
  );
}
