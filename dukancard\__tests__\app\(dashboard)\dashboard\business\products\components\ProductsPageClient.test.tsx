import React from 'react';
import { render, screen } from '@testing-library/react';
import ProductsPageClient from '@/app/(dashboard)/dashboard/business/products/components/ProductsPageClient';
import { ProductWithVariantInfo } from '@/types/products';

// Mock the inner ProductsClient component to isolate the test to ProductsPageClient
jest.mock('@/app/(dashboard)/dashboard/business/products/ProductsClient', () => {
  return function DummyProductsClient(props: any) {
    return (
      <div data-testid="products-client">
        <div data-testid="initial-data">{JSON.stringify(props.initialData)}</div>
        <div data-testid="initial-count">{props.initialCount}</div>
        <div data-testid="plan-limit">{props.planLimit}</div>
        <div data-testid="error">{props.error}</div>
      </div>
    );
  };
});

jest.mock('framer-motion', () => ({
    ...jest.requireActual('framer-motion'),
    motion: {
        ...jest.requireActual('framer-motion').motion,
        div: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    },
}));

const mockInitialData: ProductWithVariantInfo[] = [
  { id: '1', name: 'Product 1', variant_count: 0, available_variant_count: 0, product_type: 'physical', base_price: 10, is_available: true, business_id: 'bus_1', has_variants: false, created_at: new Date().toISOString(), image_url: null, images: [], discounted_price: null, description: null, featured_image_index: 0, slug: 'product-1', updated_at: new Date().toISOString() },
  { id: '2', name: 'Product 2', variant_count: 1, available_variant_count: 1, product_type: 'service', base_price: 20, is_available: false, business_id: 'bus_1', has_variants: true, created_at: new Date().toISOString(), image_url: null, images: [], discounted_price: null, description: null, featured_image_index: 0, slug: 'product-2', updated_at: new Date().toISOString() },
];

describe('ProductsPageClient', () => {
  it('should render ProductsClient and pass all props correctly', () => {
    render(
      <ProductsPageClient
        initialData={mockInitialData}
        initialCount={2}
        planLimit={10}
        error="Test Error"
      />
    );

    expect(screen.getByTestId('products-client')).toBeInTheDocument();
    expect(screen.getByTestId('initial-data')).toHaveTextContent(JSON.stringify(mockInitialData));
    expect(screen.getByTestId('initial-count')).toHaveTextContent('2');
    expect(screen.getByTestId('plan-limit')).toHaveTextContent('10');
    expect(screen.getByTestId('error')).toHaveTextContent('Test Error');
  });

  it('should render correctly with empty initial data', () => {
    render(
      <ProductsPageClient
        initialData={[]}
        initialCount={0}
        planLimit={5}
      />
    );

    expect(screen.getByTestId('products-client')).toBeInTheDocument();
    expect(screen.getByTestId('initial-data')).toHaveTextContent('[]');
    expect(screen.getByTestId('initial-count')).toHaveTextContent('0');
    expect(screen.getByTestId('plan-limit')).toHaveTextContent('5');
    expect(screen.getByTestId('error')).toBeEmptyDOMElement();
  });

  it('should render correctly without an error prop', () => {
    render(
      <ProductsPageClient
        initialData={mockInitialData}
        initialCount={2}
        planLimit={10}
      />
    );

    expect(screen.getByTestId('error')).toBeEmptyDOMElement();
  });
});