import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getSubscription } from "@/lib/razorpay/services/subscription";

/**
 * GET /api/subscriptions/:id
 *
 * Fetches a specific subscription from Razorpay by ID and includes additional information from the database
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "id": "sub_00000000000001",
 *     "entity": "subscription",
 *     "plan_id": "plan_00000000000001",
 *     "customer_id": "cust_D00000000000001",
 *     "status": "active",
 *     "current_start": 1577355871,
 *     "current_end": 1582655400,
 *     "ended_at": null,
 *     "quantity": 1,
 *     "notes": {
 *       "notes_key_1": "Tea, Earl Grey, Hot",
 *       "notes_key_2": "Tea, <PERSON>… decaf."
 *     },
 *     "charge_at": 1577385991,
 *     "offer_id": "offer_JHD834hjbxzhd38d",
 *     "start_at": 1577385991,
 *     "end_at": 1603737000,
 *     "auth_attempts": 0,
 *     "total_count": 6,
 *     "paid_count": 1,
 *     "customer_notify": true,
 *     "created_at": 1577356081,
 *     "expire_by": 1577485991,
 *     "short_url": "https://rzp.io/i/z3b1R61A9",
 *     "has_scheduled_changes": false,
 *     "change_scheduled_at": null,
 *     "remaining_count": 5
 *   }
 * }
 * ```
 */
export async function GET(
  _: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the user has access to this subscription
    // This is important for security to prevent users from accessing other users' subscriptions
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (subscriptionError) {
      // If no subscription is found with this ID, return a 404
      if (subscriptionError.code === "PGRST116") {
        return NextResponse.json(
          { success: false, error: "Subscription not found" },
          { status: 404 }
        );
      }

      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription" },
        { status: 500 }
      );
    }

    // Check if the user is authorized to access this subscription
    // Allow access if the subscription belongs to the user
    const isOwner = subscription.business_profile_id === user.id;

    // For now, we don't have admin roles, so we only check if the user is the owner
    if (!isOwner) {
      return NextResponse.json(
        { success: false, error: "Unauthorized to access this subscription" },
        { status: 403 }
      );
    }

    // Fetch the subscription from Razorpay
    const result = await getSubscription(subscriptionId);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return the subscription with additional database information
    return NextResponse.json(
      {
        success: true,
        data: {
          ...result.data,
          db_subscription: {
            id: subscription.id,
            business_profile_id: subscription.business_profile_id,
            plan_id: subscription.plan_id,
            plan_cycle: subscription.plan_cycle,
            subscription_status: subscription.subscription_status,
            subscription_start_date: subscription.subscription_start_date,
            subscription_expiry_time: subscription.subscription_expiry_time,
            last_payment_date: subscription.last_payment_date,
            last_payment_method: subscription.last_payment_method,
            cancellation_requested_at: subscription.cancellation_requested_at,
            cancellation_reason: subscription.cancellation_reason,
            subscription_paused_at: subscription.subscription_paused_at,
            created_at: subscription.created_at,
            updated_at: subscription.updated_at
          }
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error fetching subscription:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
