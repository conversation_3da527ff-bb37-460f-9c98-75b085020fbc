"use client";

import { motion } from "framer-motion";
import { ReactNode } from "react";

interface AnimatedBadgeProps {
  children: ReactNode;
  className?: string;
  color?: "gold" | "purple" | "blue" | "green";
}

export default function AnimatedBadge({
  children,
  className = "",
  color = "gold",
}: AnimatedBadgeProps) {
  const getColorClasses = () => {
    switch (color) {
      case "gold":
        return "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]";
      case "purple":
        return "bg-purple-500/10 text-purple-500";
      case "blue":
        return "bg-blue-500/10 text-blue-500";
      case "green":
        return "bg-green-500/10 text-green-500";
      default:
        return "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]";
    }
  };

  return (
    <motion.div
      className={`inline-flex items-center gap-2 px-3 py-1 rounded-full ${getColorClasses()} text-sm font-medium ${className}`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {children}
    </motion.div>
  );
}
