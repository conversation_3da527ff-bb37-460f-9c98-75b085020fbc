import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { SubscriptionFlowTester } from "@/lib/subscription/SubscriptionFlowTester";

/**
 * GET /api/test/subscription-flow
 * 
 * Test all subscription flow scenarios to ensure they work as expected.
 * This endpoint runs comprehensive tests for:
 * 1. Trial user authorizes payment for post-trial
 * 2. Post-trial user subscribes (upfront payment)
 * 3. Active user switches plans (card payment)
 * 4. Active user switches plans (UPI/E-Mandate)
 * 5. Payment method detection
 */
export async function GET(request: NextRequest) {
  try {
    // Check if this is a development environment
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (!isDevelopment) {
      return NextResponse.json(
        { success: false, error: "Test endpoints only available in development" },
        { status: 403 }
      );
    }

    // Get bypass parameter for internal testing
    const { searchParams } = new URL(request.url);
    const bypassAuth = searchParams.get('bypass') === 'internal-testing';

    if (!bypassAuth) {
      // Verify authentication for external requests
      const supabase = await createClient();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        return NextResponse.json(
          { success: false, error: "Authentication required" },
          { status: 401 }
        );
      }
    }

    // Get test type and business ID from query parameters
    const testType = searchParams.get('type') || 'all';
    const businessId = searchParams.get('businessId');

    // Validate business ID if provided
    if (businessId) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(businessId)) {
        return NextResponse.json(
          { success: false, error: "Invalid business ID format. Must be a valid UUID." },
          { status: 400 }
        );
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { success: false, error: "Business ID is required" },
        { status: 400 }
      );
    }

    const tester = new SubscriptionFlowTester(businessId);

    let results;
    
    switch (testType) {
      case 'trial-auth':
        results = [await tester.testTrialUserAuthorizesPayment()];
        break;
      case 'post-trial':
        results = [await tester.testPostTrialUserSubscribes()];
        break;
      case 'switch-card':
        results = [await tester.testActiveUserSwitchesPlansCard()];
        break;
      case 'switch-upi':
        results = [await tester.testActiveUserSwitchesPlansUPI()];
        break;
      case 'webhook-idempotency':
        results = [await tester.testWebhookIdempotency()];
        break;
      case 'all':
      default:
        const report = await tester.generateTestReport();

        return NextResponse.json({
          success: true,
          data: report
        });
    }

    return NextResponse.json({
      success: true,
      data: {
        totalTests: results.length,
        passedTests: results.filter(r => r.success).length,
        failedTests: results.filter(r => !r.success).length,
        results
      }
    });

  } catch (error) {
    console.error("[TEST_API] Error running subscription flow tests:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}


