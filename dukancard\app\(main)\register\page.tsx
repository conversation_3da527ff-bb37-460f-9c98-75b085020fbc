import type { Metada<PERSON> } from "next";
import { RegisterForm } from "./RegisterForm";
import { Suspense } from "react";
import AuthPageBackground from "../components/auth/AuthPageBackground";

export async function generateMetadata(): Promise<Metadata> {
  const title = "Register";
  const description =
    "Create your digital business card with <PERSON><PERSON><PERSON> and establish your online presence in minutes.";

  return {
    title, // Uses template: "Register - Dukancard"
    description,
    robots: "noindex, follow", // Prevent indexing
  };
}

export default function RegisterPage() {
  return (
    // Use semantic background and add top padding
    <div className="min-h-screen flex items-center justify-center pt-16 relative overflow-hidden">
      {/* Add animated background */}
      <AuthPageBackground />

      <Suspense
        fallback={
          <div className="flex flex-col justify-center items-center min-h-screen gap-2 relative z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[var(--brand-gold)]"></div>
            <p className="text-muted-foreground">
              Loading registration form...
            </p>
          </div>
        }
      >
        <RegisterForm />
      </Suspense>
    </div>
  );
}
