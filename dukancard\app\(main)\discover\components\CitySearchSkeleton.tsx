"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

export default function CitySearchSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="absolute z-10 mt-1 w-full bg-white dark:bg-neutral-900 rounded-md border border-neutral-200 dark:border-neutral-800 shadow-lg p-1"
    >
      <div className="py-1 px-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center py-1.5 px-2">
            <Skeleton className="h-4 w-4 mr-2 rounded-full" />
            <Skeleton className="h-4 w-full max-w-[180px]" />
          </div>
        ))}
      </div>
    </motion.div>
  );
}
