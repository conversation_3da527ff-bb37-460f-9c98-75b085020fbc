import { middleware } from '@/middleware';
import { NextRequest, NextResponse } from 'next/server';
import { updateSession } from '@/utils/supabase/middleware';
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

// Mock external dependencies
jest.mock('@/utils/supabase/middleware', () => ({
  updateSession: jest.fn((req) => NextResponse.next()),
}));

jest.mock('@upstash/redis', () => ({
  Redis: jest.fn(() => ({
    // Mock Redis methods used by Ratelimit
    get: jest.fn(),
    set: jest.fn(),
    incr: jest.fn(),
    expire: jest.fn(),
  })),
}));

jest.mock('@upstash/ratelimit', () => ({
  Ratelimit: jest.fn().mockImplementation(() => ({
    limit: jest.fn(() => Promise.resolve({ success: true, limit: 10, remaining: 9, reset: Date.now() / 1000 + 10 })), // Default to success
  })),
}));

// Mock console.error and console.warn to prevent them from cluttering test output
const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});

describe('middleware', () => {
  let mockRequest: NextRequest;

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock implementations for each test
    (updateSession as jest.Mock).mockImplementation((req) => NextResponse.next());
    (Ratelimit as jest.Mock).mockClear();
    // Ensure Ratelimit.prototype.limit is a mock function before calling mockClear
    if (Ratelimit.prototype.limit) {
      (Ratelimit.prototype.limit as jest.Mock).mockClear();
    }

    // Default mock request
    mockRequest = new NextRequest(new Request('https://example.com/some-path'), {
      headers: {
        'x-forwarded-for': '***********',
      },
    });
    // Ensure nextUrl.pathname is defined for updateSession mock
    Object.defineProperty(mockRequest.nextUrl, 'pathname', {
      get: () => new URL(mockRequest.url).pathname,
    });

    // Mock process.env for rate limiting configuration
    process.env.UPSTASH_REDIS_REST_URL = 'http://mock-redis-url';
    process.env.UPSTASH_REDIS_REST_TOKEN = 'mock-redis-token';
    process.env.RATE_LIMIT_MAX_REQUESTS = '10';
    process.env.RATE_LIMIT_WINDOW_SECONDS = '10';
    process.env.NODE_ENV = 'development'; // Default to development
    process.env.PLAYWRIGHT_TESTING = 'false';
  });

  afterAll(() => {
    consoleErrorSpy.mockRestore();
    consoleWarnSpy.mockRestore();
  });

  describe('Test Environment Bypass', () => {
    it('should call handleTestEnvironment and return its response if NODE_ENV is test', async () => {
      process.env.NODE_ENV = 'test';
      const mockTestResponse = NextResponse.json({ message: 'Test environment handled' });
      // Mock handleTestEnvironment directly within the test scope
      const handleTestEnvironmentSpy = jest.spyOn(require('@/middleware'), 'handleTestEnvironment').mockResolvedValue(mockTestResponse);

      const response = await middleware(mockRequest);

      expect(handleTestEnvironmentSpy).toHaveBeenCalledWith(mockRequest);
      expect(response).toEqual(mockTestResponse);
      expect(updateSession).not.toHaveBeenCalled();
      handleTestEnvironmentSpy.mockRestore();
    });

    it('should call handleTestEnvironment if PLAYWRIGHT_TESTING is true', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      const mockTestResponse = NextResponse.json({ message: 'Test environment handled' });
      const handleTestEnvironmentSpy = jest.spyOn(require('@/middleware'), 'handleTestEnvironment').mockResolvedValue(mockTestResponse);

      const response = await middleware(mockRequest);

      expect(handleTestEnvironmentSpy).toHaveBeenCalledWith(mockRequest);
      expect(response).toEqual(mockTestResponse);
      expect(updateSession).not.toHaveBeenCalled();
      handleTestEnvironmentSpy.mockRestore();
    });

    it('should call handleTestEnvironment if x-playwright-testing header is true', async () => {
      mockRequest = new NextRequest(new Request('https://example.com/some-path'), {
        headers: {
          'x-playwright-testing': 'true',
        },
      });
      const mockTestResponse = NextResponse.json({ message: 'Test environment handled' });
      const handleTestEnvironmentSpy = jest.spyOn(require('@/middleware'), 'handleTestEnvironment').mockResolvedValue(mockTestResponse);

      const response = await middleware(mockRequest);

      expect(handleTestEnvironmentSpy).toHaveBeenCalledWith(mockRequest);
      expect(response).toEqual(mockTestResponse);
      expect(updateSession).not.toHaveBeenCalled();
      handleTestEnvironmentSpy.mockRestore();
    });
  });

  describe('Domain and HTTPS Redirect Logic', () => {
    it('should redirect www to non-www in production', async () => {
      process.env.NODE_ENV = 'production';
      mockRequest = new NextRequest(new Request('https://www.example.com/some-path'));

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(301);
      expect(response?.headers.get('location')).toBe('https://example.com/some-path');
    });

    it('should redirect http to https in production', async () => {
      process.env.NODE_ENV = 'production';
      mockRequest = new NextRequest(new Request('http://example.com/some-path'));

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(301);
      expect(response?.headers.get('location')).toBe('https://example.com/some-path');
    });

    it('should redirect both www and http to non-www https in production', async () => {
      process.env.NODE_ENV = 'production';
      mockRequest = new NextRequest(new Request('http://www.example.com/some-path'));

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(301);
      expect(response?.headers.get('location')).toBe('https://example.com/some-path');
    });

    it('should not redirect in development environment', async () => {
      process.env.NODE_ENV = 'development';
      mockRequest = new NextRequest(new Request('http://www.localhost:3000/some-path'));

      const response = await middleware(mockRequest);

      expect(response?.status).not.toBe(301);
      expect(updateSession).toHaveBeenCalledWith(mockRequest);
    });

    it('should not redirect for development domains in production', async () => {
      process.env.NODE_ENV = 'production';
      mockRequest = new NextRequest(new Request('https://localhost:3000/some-path'));

      const response = await middleware(mockRequest);

      expect(response?.status).not.toBe(301);
      expect(updateSession).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('Rate Limiting Logic', () => {
    it('should apply rate limiting to API routes', async () => {
      mockRequest = new NextRequest(new Request('https://example.com/api/data'), {
        headers: {
          'x-forwarded-for': '***********',
        },
      });
      (Ratelimit.prototype.limit as jest.Mock).mockResolvedValue({ success: true, limit: 10, remaining: 9, reset: Date.now() / 1000 + 10 });

      await middleware(mockRequest);

      expect(Ratelimit.prototype.limit).toHaveBeenCalledWith('***********');
    });

    it('should return 429 if rate limit is exceeded', async () => {
      mockRequest = new NextRequest(new Request('https://example.com/api/data'), {
        headers: {
          'x-forwarded-for': '***********',
        },
      });
      (Ratelimit.prototype.limit as jest.Mock).mockResolvedValue({ success: false, limit: 10, remaining: 0, reset: Date.now() / 1000 + 10 });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(429);
      expect(response?.headers.get('X-RateLimit-Limit')).toBe('10');
      expect(response?.headers.get('X-RateLimit-Remaining')).toBe('0');
    });

    it('should not apply rate limiting to non-API routes', async () => {
      mockRequest = new NextRequest(new Request('https://example.com/some-page'));

      await middleware(mockRequest);

      expect(Ratelimit.prototype.limit).not.toHaveBeenCalled();
    });

    it('should not apply rate limiting to API webhooks', async () => {
      mockRequest = new NextRequest(new Request('https://example.com/api/webhooks/stripe'));

      await middleware(mockRequest);

      expect(Ratelimit.prototype.limit).not.toHaveBeenCalled();
    });

    it('should warn and skip rate limiting if Redis is not configured', async () => {
      process.env.UPSTASH_REDIS_REST_URL = '';
      process.env.UPSTASH_REDIS_REST_TOKEN = '';

      mockRequest = new NextRequest(new Request('https://example.com/api/data'));

      await middleware(mockRequest);

      expect(consoleWarnSpy).toHaveBeenCalledWith('Rate limiting skipped: Redis not configured');
      expect(Ratelimit.prototype.limit).not.toHaveBeenCalled();
    });

    it('should allow request to proceed if rate limiting throws an error', async () => {
      mockRequest = new NextRequest(new Request('https://example.com/api/data'));
      (Ratelimit.prototype.limit as jest.Mock).mockRejectedValue(new Error('Redis connection error'));

      const response = await middleware(mockRequest);

      expect(consoleErrorSpy).toHaveBeenCalledWith('Rate limiting error:', expect.any(Error));
      expect(response?.status).not.toBe(429);
      expect(updateSession).toHaveBeenCalled(); // Should still proceed to updateSession
    });
  });

  describe('Supabase Session Update', () => {
    it('should call updateSession at the end of the middleware chain', async () => {
      const mockUpdateSessionResponse = NextResponse.json({ message: 'Session updated' });
      (updateSession as jest.Mock).mockResolvedValue(mockUpdateSessionResponse);

      const response = await middleware(mockRequest);

      expect(updateSession).toHaveBeenCalledWith(mockRequest);
      expect(response).toEqual(mockUpdateSessionResponse);
    });
  });

  describe('handleTestEnvironment', () => {
    // This function is internal to middleware.ts and is not exported.
    // To test it, we need to import the middleware.ts file and then access the function.
    // However, since it's not exported, we'll have to test its effects through the middleware function itself.
    // The tests for this are already covered in the "Test Environment Bypass" section above.
    // If handleTestEnvironment were exported, we would test it directly here.

    // For the purpose of this test, we'll simulate the behavior of handleTestEnvironment
    // by setting the appropriate headers and environment variables.

    it('should redirect unauthenticated user from protected route to /login', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      mockRequest = new NextRequest(new Request('https://example.com/dashboard'), {
        headers: {
          'x-test-auth-state': 'unauthenticated',
        },
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(307); // Temporary redirect
      expect(response?.headers.get('location')).toBe('https://example.com/login?next=/dashboard');
    });

    it('should allow authenticated user to access protected route', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      mockRequest = new NextRequest(new Request('https://example.com/dashboard'), {
        headers: {
          'x-test-auth-state': 'authenticated',
          'x-test-has-profile': 'true',
          'x-test-user-type': 'customer',
        },
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(200); // OK (NextResponse.next() default)
      expect(response?.headers.get('location')).toBeNull();
    });

    it('should redirect new user to /choose-role', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      mockRequest = new NextRequest(new Request('https://example.com/some-protected-page'), {
        headers: {
          'x-test-auth-state': 'authenticated',
          'x-test-has-profile': 'false',
        },
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(307);
      expect(response?.headers.get('location')).toBe('https://example.com/choose-role');
    });

    it('should redirect business user without slug to /onboarding', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      mockRequest = new NextRequest(new Request('https://example.com/dashboard/business'), {
        headers: {
          'x-test-auth-state': 'authenticated',
          'x-test-has-profile': 'true',
          'x-test-user-type': 'business',
          'x-test-business-slug': '',
        },
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(307);
      expect(response?.headers.get('location')).toBe('https://example.com/onboarding');
    });

    it('should redirect logged-in user from /login to dashboard', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      mockRequest = new NextRequest(new Request('https://example.com/login'), {
        headers: {
          'x-test-auth-state': 'authenticated',
          'x-test-has-profile': 'true',
          'x-test-user-type': 'customer',
        },
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(307);
      expect(response?.headers.get('location')).toBe('https://example.com/dashboard/customer');
    });

    it('should allow user who just logged out to access /login', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      mockRequest = new NextRequest(new Request('https://example.com/login?logged_out=true'), {
        headers: {
          'x-test-auth-state': 'authenticated',
          'x-test-has-profile': 'true',
          'x-test-user-type': 'customer',
        },
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(200);
      expect(response?.headers.get('location')).toBeNull();
    });

    it('should redirect customer from business dashboard', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      mockRequest = new NextRequest(new Request('https://example.com/dashboard/business'), {
        headers: {
          'x-test-auth-state': 'authenticated',
          'x-test-has-profile': 'true',
          'x-test-user-type': 'customer',
        },
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(307);
      expect(response?.headers.get('location')).toBe('https://example.com/dashboard/customer');
    });

    it('should redirect business user from customer dashboard', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      mockRequest = new NextRequest(new Request('https://example.com/dashboard/customer'), {
        headers: {
          'x-test-auth-state': 'authenticated',
          'x-test-has-profile': 'true',
          'x-test-user-type': 'business',
          'x-test-business-slug': 'some-slug',
        },
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(307);
      expect(response?.headers.get('location')).toBe('https://example.com/dashboard/business');
    });

    it('should redirect free tier business user from analytics', async () => {
      process.env.PLAYWRIGHT_TESTING = 'true';
      mockRequest = new NextRequest(new Request('https://example.com/dashboard/business/analytics'), {
        headers: {
          'x-test-auth-state': 'authenticated',
          'x-test-has-profile': 'true',
          'x-test-user-type': 'business',
          'x-test-business-slug': 'some-slug',
          'x-test-plan-id': 'free',
        },
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(307);
      expect(response?.headers.get('location')).toBe('https://example.com/dashboard/business/plan?upgrade=analytics');
    });
  });
});