"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import {
  ArrowRight,
  Check,
  Loader2,
  ShieldCheck,
  Store,
  User,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { registerUser } from "./actions"; // Import the server action
import { IndianMobileSchema, PasswordComplexitySchema } from "@/lib/schemas/authSchemas";

// Mobile-only registration form schema
const formSchema = z
  .object({
    mobile: IndianMobileSchema,
    name: z.string().min(2, { message: "Name must be at least 2 characters" }),
    password: PasswordComplexitySchema,
    confirmPassword: z.string(),
    acceptTerms: z.boolean().refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export function RegisterForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [isSocialLoading, setIsSocialLoading] = useState<"google" | null>(null);
  const [redirectSlug, setRedirectSlug] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);

  // Get the redirect and message parameters from the URL
  useEffect(() => {
    const redirect = searchParams.get('redirect');
    if (redirect) {
      setRedirectSlug(redirect);
    }

    const messageParam = searchParams.get('message');
    if (messageParam) {
      setMessage(messageParam);
    }
  }, [searchParams]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      mobile: "",
      name: "",
      password: "",
      confirmPassword: "",
      acceptTerms: false,
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    form.clearErrors(); // Clear previous errors

    try {
      // Validate terms acceptance client-side as well
      if (!values.acceptTerms) {
        form.setError("acceptTerms", {
          type: "manual",
          message: "You must accept the terms and conditions",
        });
        setIsLoading(false);
        return;
      }

      const result = await registerUser(values); // Call the server action

      if (result.success) {
        // Show success message for mobile registration
        toast.success("Registration successful!", {
          description: result.message || "You can now log in with your mobile number.",
        });
        // If we have a redirect slug, pass it to the login page
        if (redirectSlug) {
          router.push(`/login?redirect=${redirectSlug}`);
        } else {
          router.push("/login"); // Default redirect on success
        }
      } else {
        // Handle errors returned from the server action
        if (result.error) {
          // Check for specific error messages and provide more user-friendly responses
          if (result.error.includes("already exists")) {
            toast.error("Mobile number already registered", {
              description: "Please log in instead or use a different mobile number.",
            });
          } else {
            toast.error("Registration Failed", {
              description: result.error,
            });
          }
        }
        // Optionally handle specific field errors if returned
        if (result.fieldErrors) {
          for (const [field, errors] of Object.entries(result.fieldErrors)) {
            if (errors && errors.length > 0) {
              form.setError(field as keyof z.infer<typeof formSchema>, {
                type: "server",
                message: errors.join(", "),
              });
            }
          }
        }
      }
    } catch (error: unknown) {
      // Catch unexpected client-side errors during the action call
      // Provide more specific error message if possible
      const errorMessage = error instanceof Error ? error.message : "Please try again later.";
      toast.error("Registration failed", {
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Social login with improved error handling and secure browser flow
  async function handleSocialLogin(provider: "google") {
    try {
      setIsSocialLoading(provider);
      // Need client for social login
      const { createClient } = await import("@/utils/supabase/client");
      const supabase = createClient();

      // Construct the callback URL with the redirect and message parameters if available
      // Add closeWindow=true to indicate this window should close after auth
      let callbackUrl = `${window.location.origin}/auth/callback?closeWindow=true`;

      // Add redirect parameter if available
      if (redirectSlug) {
        callbackUrl += `&redirect=${encodeURIComponent(redirectSlug)}`;
      } else {
        callbackUrl += `&next=/onboarding`;
      }

      // Add message parameter if available
      if (message) {
        callbackUrl += `&message=${encodeURIComponent(message)}`;
      }

      // Get the authorization URL but don't redirect automatically
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: callbackUrl,
          skipBrowserRedirect: true, // Don't redirect automatically
          queryParams: {
            // Add access_type=offline to request a refresh token
            access_type: "offline",
            // Prompt user to select account to avoid auto-selection issues
            prompt: "select_account"
          }
        },
      });

      if (error) {
        toast.error("Registration failed", {
          description: error.message,
        });
        setIsSocialLoading(null);
        return;
      }

      // If we have the URL, open it in a new tab
      if (data?.url) {
        // Open the authorization URL in a new tab
        window.open(data.url, "_blank");

        // Show a toast to guide the user
        toast.info("Google sign-in opened in a new tab", {
          description: "Please complete the sign-in process in the new tab.",
          duration: 5000,
        });

        // Reset loading state after a short delay
        setTimeout(() => {
          setIsSocialLoading(null);
        }, 1000);
      } else {
        toast.error("Failed to start Google sign-in", {
          description: "Please try again or use email registration.",
        });
        setIsSocialLoading(null);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred. Please try again.";
      toast.error("Registration failed", {
        description: errorMessage,
      });
      setIsSocialLoading(null);
    }
  }

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, delay: i * 0.1 },
    }),
  };

  return (
    <div className="container mx-auto px-3 sm:px-4 py-6 sm:py-8 md:py-12 lg:py-24 pt-20 sm:pt-24 md:pt-28">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-12">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-col justify-center"
        >
          <div className="text-center lg:text-left mb-6 sm:mb-8">
            {/* Use semantic text colors */}
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-foreground mb-3 sm:mb-4">
              Get Your Business <span className="text-[var(--brand-gold)]">Online for FREE</span>
            </h1>
            <p className="text-base sm:text-lg text-muted-foreground">
              Create your digital business card in seconds and start showcasing your products instantly.
            </p>
          </div>

          <div className="hidden lg:block">
            <div className="space-y-6">
              {[
                {
                  icon: <User className="w-6 h-6 text-[var(--brand-gold)]" />,
                  title: "Free Digital Business Card",
                  description:
                    "Create your professional online presence in just 30 seconds.",
                },
                {
                  icon: <Store className="w-6 h-6 text-[var(--brand-gold)]" />,
                  title: "Free Product Listings",
                  description:
                    "Showcase up to 5 products or services with your free account.",
                },
                {
                  icon: <ShieldCheck className="w-6 h-6 text-[var(--brand-gold)]" />,
                  title: "Secure & Reliable",
                  description:
                    "Enterprise-grade security with modern design and instant sharing.",
                },
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  custom={index}
                  initial="hidden"
                  animate="visible"
                  variants={fadeIn}
                  className="flex items-start gap-4"
                >
                   {/* Adjust icon background */}
                  <div className="p-2 bg-primary/10 dark:bg-[var(--brand-gold)]/10 rounded-lg">
                    {feature.icon}
                  </div>
                  <div>
                     {/* Use semantic text colors */}
                    <h3 className="text-lg font-semibold text-foreground">
                      {feature.title}
                    </h3>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
               // Use semantic card styles
              className="mt-12 p-6 bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/20 rounded-xl"
            >
              <div className="flex items-center gap-3 mb-4">
                 {/* Adjust icon background */}
                <div className="w-10 h-10 rounded-full bg-primary/10 dark:bg-[var(--brand-gold)]/10 flex items-center justify-center">
                  <Check className="w-5 h-5 text-primary dark:text-[var(--brand-gold)]" />
                </div>
                 {/* Use semantic text color */}
                <h3 className="text-lg font-semibold text-card-foreground">
                  <span className="text-[var(--brand-gold)]">FREE</span> + Premium Trial
                </h3>
              </div>
               {/* Use semantic text color */}
              <p className="text-muted-foreground">
                Start with our free tier instantly, plus get 1 month free trial of premium features when you&apos;re ready to upgrade. No credit card required.
              </p>
            </motion.div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
           {/* Use semantic card styles */}
          <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg dark:shadow-[var(--brand-gold)]/10">
             {/* Use semantic text color */}
            <h2 className="text-xl sm:text-2xl font-bold text-foreground mb-4 sm:mb-6 text-center">
              Create Your Account
            </h2>

            {message && (
              <div className={`mb-4 p-2 sm:p-3 rounded-lg ${
                message.toLowerCase().includes("error") || message.toLowerCase().includes("failed")
                  ? "bg-destructive/10 text-destructive"
                  : "bg-green-500/10 text-green-600 dark:text-green-400"
              }`}>
                <p className="text-xs sm:text-sm">{message}</p>
              </div>
            )}

            {/* Centered Google Button */}
            <div className="flex justify-center mb-5 sm:mb-6">
               {/* Adjust Google button style */}
              <Button
                variant="outline"
                className="cursor-pointer bg-background hover:bg-muted border-border dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-800 w-full py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-foreground text-sm sm:text-base"
                onClick={() => handleSocialLogin("google")}
                disabled={!!isSocialLoading}
              >
                {isSocialLoading === "google" ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    className="w-4 h-4 mr-2"
                  >
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                )}
                Register with Google
              </Button>
            </div>

            <div className="relative mb-5 sm:mb-6">
              <div className="absolute inset-0 flex items-center">
                 {/* Use semantic border */}
                <div className="w-full border-t border-border" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                 {/* Use semantic background/text */}
                <span className="bg-card px-2 text-muted-foreground">
                  Or continue with mobile
                </span>
              </div>
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4 sm:space-y-6"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                       {/* Use semantic label color */}
                      <FormLabel className="text-foreground text-sm sm:text-base">
                        Full Name
                      </FormLabel>
                      <FormControl>
                         {/* Use semantic input styles */}
                        <Input
                          placeholder="John Doe"
                          {...field}
                          className="bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* User Type Selection Removed */}
                <FormField
                  control={form.control}
                  name="mobile"
                  render={({ field }) => (
                    <FormItem>
                       {/* Use semantic label color */}
                      <FormLabel className="text-foreground text-sm sm:text-base">
                        Mobile Number
                      </FormLabel>
                      <FormControl>
                         {/* Use semantic input styles */}
                        <div className="relative">
                          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                            +91
                          </div>
                          <Input
                            placeholder="9876543210"
                            type="tel"
                            {...field}
                            onChange={(e) => {
                              let value = e.target.value;
                              // Remove any +91 prefix if user enters it
                              value = value.replace(/^\+91/, '');
                              // Only allow numeric input
                              value = value.replace(/\D/g, '');
                              // Limit to 10 digits for mobile numbers
                              if (value.length > 10) {
                                value = value.slice(0, 10);
                              }
                              field.onChange(value);
                            }}
                            onKeyDown={(e) => {
                              const isNumeric = /^[0-9]$/.test(e.key);
                              const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);
                              if (!isNumeric && !isControl) {
                                e.preventDefault();
                              }
                            }}
                            className="pl-12 bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"
                            maxLength={10}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                      <div className="text-xs text-muted-foreground mt-1">
                        Enter your 10-digit mobile number (starting with 6-9)
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                       {/* Use semantic label color */}
                      <FormLabel className="text-foreground text-sm sm:text-base">
                        Password
                      </FormLabel>
                      <FormControl>
                         {/* Use semantic input styles */}
                        <Input
                          placeholder="••••••••"
                          type="password"
                          {...field}
                          className="bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"
                        />
                      </FormControl>
                      <FormMessage />
                       {/* Use semantic text color */}
                      <div className="text-xs text-muted-foreground mt-1">
                        Must contain: 6+ chars, 1 symbol, 1 number, 1 uppercase
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                       {/* Use semantic label color */}
                      <FormLabel className="text-foreground text-sm sm:text-base">
                        Confirm Password
                      </FormLabel>
                      <FormControl>
                         {/* Use semantic input styles */}
                        <Input
                          placeholder="••••••••"
                          type="password"
                          {...field}
                          className="bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="acceptTerms"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-2 space-y-0 py-1 sm:py-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="cursor-pointer mt-0.5"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-tight flex-1 min-w-0">
                        <FormLabel className="text-xs sm:text-sm font-normal text-muted-foreground inline-block break-words">
                          I agree to the{" "}
                          <Link
                            href="/terms"
                            className="text-primary dark:text-[var(--brand-gold)] hover:underline inline-block"
                            target="_blank"
                          >
                            Terms of Service
                          </Link>{" "}
                          and{" "}
                          <Link
                            href="/privacy"
                            className="text-primary dark:text-[var(--brand-gold)] hover:underline inline-block"
                            target="_blank"
                          >
                            Privacy Policy
                          </Link>
                        </FormLabel>
                        <FormMessage />
                      </div>
                    </FormItem>
                  )}
                />

                 {/* Ensure button text contrast */}
                <Button
                  type="submit"
                  className="cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base"
                  disabled={isLoading || isSocialLoading !== null}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    <>
                      Get Started for FREE <ArrowRight className="w-5 h-5 ml-2" />
                    </>
                  )}
                </Button>

                 {/* Use semantic text color */}
                <p className="text-center text-muted-foreground text-xs sm:text-sm">
                  Already have an account?{" "}
                  <Link
                    href={redirectSlug
                      ? `/login?redirect=${redirectSlug}${message ? `&message=${encodeURIComponent(message)}` : ''}`
                      : "/login"}
                    className="text-primary dark:text-[var(--brand-gold)] hover:underline font-medium"
                  >
                    Sign in
                  </Link>
                </p>
              </form>
            </Form>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
