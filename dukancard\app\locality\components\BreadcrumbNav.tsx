"use client";

import Link from "next/link";
import { ChevronRight, Home } from "lucide-react";
import { useLocalityContext } from "../context/LocalityContext";

export default function BreadcrumbNav() {
  const { locality } = useLocalityContext();

  return (
    <nav className="container mx-auto px-4 py-4 flex items-center text-sm text-muted-foreground">
      <Link href="/?view=home" className="flex items-center hover:text-primary transition-colors">
        <Home className="h-4 w-4 mr-1" />
        <span>Home</span>
      </Link>
      <ChevronRight className="h-4 w-4 mx-1" />
      <Link href="/discover" className="hover:text-primary transition-colors">
        Discover
      </Link>
      <ChevronRight className="h-4 w-4 mx-1" />
      <span className="text-foreground font-medium">
        {locality.name}
      </span>
    </nav>
  );
}
