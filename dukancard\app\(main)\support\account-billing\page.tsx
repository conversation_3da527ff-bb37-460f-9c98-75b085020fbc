import { Metada<PERSON> } from "next";
import { siteConfig } from "@/lib/site-config";
import AccountBillingClient from "./AccountBillingClient";

export async function generateMetadata(): Promise<Metadata> {
  const title = `Account & Billing Support`;
  const description =
    "Get help with your Dukancard account, subscription plans, billing information, and payment methods. Learn how to manage your account settings and subscription.";
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || siteConfig.url;
  const pageUrl = `${siteUrl}/support/account-billing`;
  const ogImage = `${siteUrl}/opengraph-image.png`;

  return {
    title,
    description,
    keywords: [
      "Dukancard account",
      "Dukancard billing",
      "subscription management",
      "payment methods",
      "account settings",
      "billing support",
    ],
    alternates: {
      canonical: "/support/account-billing",
    },
    openGraph: {
      title,
      description,
      url: pageUrl,
      siteName: siteConfig.name,
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: `${siteConfig.name} Account & Billing Support`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [ogImage],
    },
    // Add WebPage Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: siteConfig.name,
          url: siteUrl,
        },
      }),
    },
  };
}

export default function AccountBillingPage() {
  return <AccountBillingClient />;
}
