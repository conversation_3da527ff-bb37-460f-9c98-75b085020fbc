"use client";

import React from "react";

interface AuthMessageProps {
  isAuthenticated: boolean;
}

export default function AuthMessage({ isAuthenticated }: AuthMessageProps) {
  return isAuthenticated ? (
    <div className="bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800/30 text-green-700 dark:text-green-400 text-xs sm:text-sm p-2.5 sm:p-3 rounded-md mb-4 sm:mb-6 w-full overflow-hidden">
      You&apos;re logged in and can leave a review. Your feedback helps others discover great businesses!
    </div>
  ) : (
    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800/30 text-blue-700 dark:text-blue-400 text-xs sm:text-sm p-2.5 sm:p-3 rounded-md mb-4 sm:mb-6 w-full overflow-hidden">
      <strong>Note:</strong> You need to be logged in to leave a review. Your feedback helps others discover great businesses!
    </div>
  );
}
