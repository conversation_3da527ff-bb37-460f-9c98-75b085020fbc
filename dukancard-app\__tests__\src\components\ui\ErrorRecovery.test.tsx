import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ErrorRecovery } from '@/src/components/ui/ErrorRecovery';
import { createAppError } from '@/src/utils/errorHandling';
import { useTheme } from '@/src/hooks/useTheme';

// Mock hooks
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
      secondary: '#f0f0f0',
      error: '#EF4444',
      textPrimary: '#000000',
      textSecondary: '#666666',
      textMuted: '#999999',
      border: '#cccccc',
      background: '#ffffff',
      shadow: '#000',
    },
    borderRadius: { lg: 12, md: 8 },
    spacing: { lg: 24, md: 16, sm: 8, xs: 4 },
    typography: {
      fontSize: { xl: 24, lg: 20, base: 16, sm: 14, xs: 12 },
      fontWeight: { bold: '700', medium: '500' },
      lineHeight: { relaxed: 1.6 },
    },
  }),
}));

const mockOnRetry = jest.fn();
const mockOnDismiss = jest.fn();

const networkError = createAppError(
  'network',
  'Connection Error',
  'Could not connect to the server.'
);

const renderComponent = (props: Partial<React.ComponentProps<typeof ErrorRecovery>> = {}) => {
  return render(
    <ErrorRecovery
      error={networkError}
      onRetry={mockOnRetry}
      onDismiss={mockOnDismiss}
      {...props}
    />
  );
};

describe('<ErrorRecovery />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with a network error', () => {
    const { getByText, toJSON } = renderComponent({ error: networkError });
    expect(getByText('Connection Error')).toBeTruthy();
    expect(getByText('Could not connect to the server.')).toBeTruthy();
    expect(getByText(/Check your internet connection/)).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it('calls onRetry when the retry button is pressed', () => {
    const { getByText } = renderComponent({ error: networkError });
    fireEvent.press(getByText('Try Again'));
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it('calls onDismiss when the dismiss button is pressed', () => {
    const { getByText } = renderComponent({ error: networkError });
    fireEvent.press(getByText('Dismiss'));
    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });

  it('disables the retry button when max retries are reached', () => {
    const { getByText, queryByText } = renderComponent({ retryCount: 3, maxRetries: 3 });
    expect(queryByText('Try Again')).toBeNull();
    expect(getByText(/Maximum retry attempts reached/)).toBeTruthy();
  });

  it('shows retry count information', () => {
    const { getByText } = renderComponent({ retryCount: 1, maxRetries: 3 });
    expect(getByText('Attempt 1 of 3')).toBeTruthy();
  });

  it('shows a loading indicator when retrying', () => {
    const { getByText, queryByText } = renderComponent({ isRetrying: true });
    expect(queryByText('Try Again')).toBeNull();
    expect(getByText('Retrying...')).toBeTruthy();
  });

  it('renders in compact mode', () => {
    const { queryByText, toJSON } = renderComponent({ compact: true });
    // Guidance text should be hidden in compact mode
    expect(queryByText(/Check your internet connection/)).toBeNull();
    expect(toJSON()).toMatchSnapshot();
  });

  it('hides the dismiss button when showDismiss is false', () => {
    const { queryByText } = renderComponent({ showDismiss: false });
    expect(queryByText('Dismiss')).toBeNull();
  });
});