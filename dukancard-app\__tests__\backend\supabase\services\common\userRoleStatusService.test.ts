import { supabase } from '@/lib/supabase';
import {
  getUserRoleStatus,
  needsRoleSelection,
  needsOnboarding,
  getUserRole,
  isBusinessOnboardingCompleted,
  isCustomerProfileComplete,
  getOnboardingStatus,
  getNextAction,
} from '@/backend/supabase/services/common/userRoleStatusService';

// Mock the Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          maybeSingle: jest.fn(),
        })),
      })),
    })),
  },
}));

const mockGetUser = supabase.auth.getUser as jest.Mock;
const mockFrom = supabase.from as jest.Mock;

describe('userRoleStatusService', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserRoleStatus', () => {
    it('should return needsRoleSelection when no profiles exist', async () => {
      mockGetUser.mockResolvedValue({ data: { user: { id: '123' } }, error: null });
      mockFrom.mockImplementation((table: string) => {
        if (table === 'customer_profiles') {
          return {
            select: () => ({
              eq: () => ({
                maybeSingle: () => Promise.resolve({ data: null, error: null }),
              }),
            }),
          };
        }
        if (table === 'business_profiles') {
          return {
            select: () => ({
              eq: () => ({
                maybeSingle: () => Promise.resolve({ data: null, error: null }),
              }),
            }),
          };
        }
        return {
            select: () => ({
              eq: () => ({
                maybeSingle: () => Promise.resolve({ data: null, error: null }),
              }),
            }),
          };
      });

      const result = await getUserRoleStatus();
      expect(result.success).toBe(true);
      expect(result.data?.needsRoleSelection).toBe(true);
      expect(result.data?.role).toBe(null);
    });

    it('should return correct status for a complete customer profile', async () => {
        mockGetUser.mockResolvedValue({ data: { user: { id: '123' } }, error: null });
        const customerProfile = {
            id: '123',
            name: 'John Doe',
            pincode: '123456',
            city: 'Test City',
            state: 'Test State',
            locality: 'Test Locality',
            latitude: 1.0,
            longitude: 1.0,
        };
        mockFrom.mockImplementation((table: string) => {
            if (table === 'customer_profiles') {
                return {
                    select: () => ({
                        eq: () => ({
                            maybeSingle: () => Promise.resolve({ data: customerProfile, error: null }),
                        }),
                    }),
                };
            }
            if (table === 'business_profiles') {
                return {
                    select: () => ({
                        eq: () => ({
                            maybeSingle: () => Promise.resolve({ data: null, error: null }),
                        }),
                    }),
                };
            }
            return {
                select: () => ({
                  eq: () => ({
                    maybeSingle: () => Promise.resolve({ data: null, error: null }),
                  }),
                }),
              };
        });

        const result = await getUserRoleStatus();
        expect(result.success).toBe(true);
        expect(result.data?.hasCustomerProfile).toBe(true);
        expect(result.data?.customerProfileComplete).toBe(true);
        expect(result.data?.role).toBe('customer');
        expect(result.data?.needsOnboarding).toBe(false);
    });

    it('should return correct status for an incomplete business profile', async () => {
        mockGetUser.mockResolvedValue({ data: { user: { id: '123' } }, error: null });
        const businessProfile = { id: '123', business_name: 'Test Inc.' };
        mockFrom.mockImplementation((table: string) => {
            if (table === 'customer_profiles') {
                return {
                    select: () => ({
                        eq: () => ({
                            maybeSingle: () => Promise.resolve({ data: null, error: null }),
                        }),
                    }),
                };
            }
            if (table === 'business_profiles') {
                return {
                    select: () => ({
                        eq: () => ({
                            maybeSingle: () => Promise.resolve({ data: businessProfile, error: null }),
                        }),
                    }),
                };
            }
            return {
                select: () => ({
                  eq: () => ({
                    maybeSingle: () => Promise.resolve({ data: null, error: null }),
                  }),
                }),
              };
        });

        const result = await getUserRoleStatus();
        expect(result.success).toBe(true);
        expect(result.data?.hasBusinessProfile).toBe(true);
        expect(result.data?.businessOnboardingCompleted).toBe(false);
        expect(result.data?.role).toBe('business');
        expect(result.data?.needsOnboarding).toBe(true);
    });
  });

  describe('helper functions', () => {
    it('needsRoleSelection should call getUserRoleStatus and return correct value', async () => {
        mockGetUser.mockResolvedValue({ data: { user: { id: '123' } }, error: null });
        mockFrom.mockImplementation((table: string) => {
            if (table === 'customer_profiles') {
                return {
                    select: () => ({
                        eq: () => ({
                            maybeSingle: () => Promise.resolve({ data: null, error: null }),
                        }),
                    }),
                };
            }
            if (table === 'business_profiles') {
                return {
                    select: () => ({
                        eq: () => ({
                            maybeSingle: () => Promise.resolve({ data: null, error: null }),
                        }),
                    }),
                };
            }
            return {
                select: () => ({
                  eq: () => ({
                    maybeSingle: () => Promise.resolve({ data: null, error: null }),
                  }),
                }),
              };
        });
        const result = await needsRoleSelection();
        expect(result.success).toBe(true);
        expect(result.data).toBe(true);
    });

    it('getUserRole should return the correct role', async () => {
        mockGetUser.mockResolvedValue({ data: { user: { id: '123' } }, error: null });
        const businessProfile = { id: '123', business_name: 'Test Inc.' };
        mockFrom.mockImplementation((table: string) => {
            if (table === 'customer_profiles') {
                return {
                    select: () => ({
                        eq: () => ({
                            maybeSingle: () => Promise.resolve({ data: null, error: null }),
                        }),
                    }),
                };
            }
            if (table === 'business_profiles') {
                return {
                    select: () => ({
                        eq: () => ({
                            maybeSingle: () => Promise.resolve({ data: businessProfile, error: null }),
                        }),
                    }),
                };
            }
            return {
                select: () => ({
                  eq: () => ({
                    maybeSingle: () => Promise.resolve({ data: null, error: null }),
                  }),
                }),
              };
        });
        const result = await getUserRole();
        expect(result.success).toBe(true);
        expect(result.data).toBe('business');
    });
  });
});