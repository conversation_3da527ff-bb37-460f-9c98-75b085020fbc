"use client";

import { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  Info,
  HelpCircle,
  Settings,
  Shield,
  Globe,
  Calendar,
  Mail
} from "lucide-react";
import { Card } from "@/components/ui/card";
import PolicyHeroSection from "../components/policy/PolicyHeroSection";
import PolicySection from "../components/policy/PolicySection";
import PolicyNavigation from "../components/policy/PolicyNavigation";
import PolicyCTASection from "../components/policy/PolicyCTASection";
import SectionDivider from "../components/landing/SectionDivider";
import { siteConfig } from "@/lib/site-config";

// Navigation items for the policy
const navItems = [
  { id: "introduction", title: "Introduction" },
  { id: "what-are-cookies", title: "What Are Cookies?" },
  { id: "why-we-use-cookies", title: "Why We Use Cookies" },
  { id: "types-of-cookies", title: "Types of Cookies" },
  { id: "control-cookies", title: "How to Control Cookies" },
  { id: "third-party", title: "Third-Party Cookies" },
  { id: "policy-updates", title: "Policy Updates" },
  { id: "contact", title: "Contact Us" },
];

// Related links
const relatedLinks = [
  { title: "Privacy Policy", href: "/privacy" },
  { title: "Terms of Service", href: "/terms" },
  { title: "Refund Policy", href: "/refund" },
];

export default function ModernCookiePolicyClient() {
  const pageRef = useRef<HTMLDivElement>(null);

  // Removed scroll-based fade effect

  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div
      ref={pageRef}
      className="min-h-screen bg-white dark:bg-black"
    >
      <div className="relative">
        {/* Hero Section */}
        <PolicyHeroSection
          title="Cookie Policy"
          lastUpdated="May 19, 2025"
          variant="gold"
        />

        <div className="container mx-auto px-4 max-w-4xl pb-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left column: Navigation (sticky on desktop) */}
            <div className="w-full lg:w-1/4 order-1">
              <div className="lg:sticky lg:top-24 self-start">
                <PolicyNavigation items={navItems} />
              </div>
            </div>

            {/* Right column: Content */}
            <div className="w-full lg:w-3/4 order-2">
              <Card className="p-6 md:p-8 border border-border shadow-sm mb-8">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="prose prose-neutral dark:prose-invert max-w-none"
                >
                  <p className="text-lg">
                    This Cookie Policy explains how Dukancard uses cookies and similar technologies to recognize you when you visit our website. It explains what these technologies are and why we use them, as well as your rights to control our use of them.
                  </p>
                </motion.div>
              </Card>

              {/* Introduction Section */}
              <PolicySection
                id="introduction"
                title="1. Introduction"
                icon={<Info className="h-6 w-6" />}
                delay={0}
              >
                <p>
                  This Cookie Policy explains how Dukancard (&quot;we&quot;, &quot;us&quot;, or &quot;our&quot;) uses cookies and similar technologies to recognize you when you visit our website at {siteConfig.url} (&quot;Website&quot;). It explains what these technologies are and why we use them, as well as your rights to control our use of them.
                </p>
              </PolicySection>

              {/* What Are Cookies Section */}
              <PolicySection
                id="what-are-cookies"
                title="2. What Are Cookies?"
                icon={<Cookie className="h-6 w-6" />}
                delay={1}
              >
                <p>
                  Cookies are small data files that are placed on your computer or mobile device when you visit a website. Cookies are widely used by website owners in order to make their websites work, or to work more efficiently, as well as to provide reporting information.
                </p>
                <p>
                  Cookies set by the website owner (in this case, Dukancard) are called &quot;first-party cookies.&quot; Cookies set by parties other than the website owner are called &quot;third-party cookies.&quot; Third-party cookies enable third-party features or functionality to be provided on or through the website (e.g., advertising, interactive content, and analytics). The parties that set these third-party cookies can recognize your computer both when it visits the website in question and also when it visits certain other websites.
                </p>
              </PolicySection>

              {/* Why We Use Cookies Section */}
              <PolicySection
                id="why-we-use-cookies"
                title="3. Why Do We Use Cookies?"
                icon={<HelpCircle className="h-6 w-6" />}
                delay={2}
              >
                <p>
                  We use first-party and third-party cookies for several reasons. Some cookies are required for technical reasons in order for our Website to operate, and we refer to these as &quot;essential&quot; or &quot;strictly necessary&quot; cookies. Other cookies also enable us to track and target the interests of our users to enhance the experience on our Website. Third parties serve cookies through our Website for advertising, analytics, and other purposes. This is described in more detail below.
                </p>
              </PolicySection>

              {/* Section Divider */}
              <SectionDivider variant="gold" className="my-12" />

              {/* Types of Cookies Section */}
              <PolicySection
                id="types-of-cookies"
                title="4. Types of Cookies We Use"
                icon={<Settings className="h-6 w-6" />}
                delay={3}
              >
                <h3 className="text-xl font-semibold mt-6 mb-3">4.1 Essential Cookies</h3>
                <p>
                  These cookies are strictly necessary to provide you with services available through our Website and to use some of its features, such as access to secure areas. Because these cookies are strictly necessary to deliver the Website, you cannot refuse them without impacting how our Website functions.
                </p>
                <p>
                  Examples of essential cookies we use:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Session cookies to operate our service</li>
                  <li>Authentication cookies to remember your login state</li>
                  <li>Security cookies for fraud prevention and detection</li>
                </ul>

                <h3 className="text-xl font-semibold mt-6 mb-3">4.2 Performance and Functionality Cookies</h3>
                <p>
                  These cookies are used to enhance the performance and functionality of our Website but are non-essential to their use. However, without these cookies, certain functionality may become unavailable.
                </p>
                <p>
                  Examples of performance and functionality cookies we use:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Cookies to remember your preferences</li>
                  <li>Cookies to personalize content</li>
                  <li>Cookies to improve website performance</li>
                </ul>

                <h3 className="text-xl font-semibold mt-6 mb-3">4.3 Analytics and Customization Cookies</h3>
                <p>
                  These cookies collect information that is used either in aggregate form to help us understand how our Website is being used or how effective our marketing campaigns are, or to help us customize our Website for you.
                </p>
                <p>
                  Examples of analytics and customization cookies we use:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Google Analytics cookies to track user behavior</li>
                  <li>Cookies to measure the effectiveness of our content</li>
                  <li>Cookies to understand user journeys through our website</li>
                </ul>

                <h3 className="text-xl font-semibold mt-6 mb-3">4.4 Advertising Cookies</h3>
                <p>
                  These cookies are used to make advertising messages more relevant to you. They perform functions like preventing the same ad from continuously reappearing, ensuring that ads are properly displayed for advertisers, and in some cases selecting advertisements that are based on your interests.
                </p>
                <p>
                  Examples of advertising cookies we use:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Cookies to deliver targeted advertisements</li>
                  <li>Cookies to measure the effectiveness of advertising campaigns</li>
                  <li>Cookies to limit the number of times you see an advertisement</li>
                </ul>
              </PolicySection>

              {/* How to Control Cookies Section */}
              <PolicySection
                id="control-cookies"
                title="5. How Can You Control Cookies?"
                icon={<Shield className="h-6 w-6" />}
                delay={4}
              >
                <p>
                  You have the right to decide whether to accept or reject cookies. You can exercise your cookie preferences by clicking on the appropriate opt-out links provided in the cookie banner on our Website.
                </p>
                <p>
                  You can also set or amend your web browser controls to accept or refuse cookies. If you choose to reject cookies, you may still use our Website though your access to some functionality and areas of our Website may be restricted. As the means by which you can refuse cookies through your web browser controls vary from browser to browser, you should visit your browser&apos;s help menu for more information.
                </p>
                <p>
                  In addition, most advertising networks offer you a way to opt out of targeted advertising. If you would like to find out more information, please visit <a href="http://www.aboutads.info/choices/" className="text-[var(--brand-gold)] hover:underline">http://www.aboutads.info/choices/</a> or <a href="http://www.youronlinechoices.com" className="text-[var(--brand-gold)] hover:underline">http://www.youronlinechoices.com</a>.
                </p>
              </PolicySection>

              {/* Third-Party Cookies Section */}
              <PolicySection
                id="third-party"
                title="6. Third-Party Cookies"
                icon={<Globe className="h-6 w-6" />}
                delay={5}
              >
                <p>
                  In addition to our own cookies, we may also use various third-party cookies to report usage statistics of the Service, deliver advertisements on and through the Service, and so on.
                </p>
                <p>
                  The specific third-party cookies we use include:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>
                    <strong>Google Analytics:</strong> Used to analyze user behavior and track website performance.
                  </li>
                  <li>
                    <strong>Google Ads:</strong> Used for remarketing and conversion tracking.
                  </li>
                  <li>
                    <strong>Razorpay:</strong> Used for processing payments and managing subscriptions.
                  </li>
                  <li>
                    <strong>Supabase:</strong> Used for authentication and database services.
                  </li>
                </ul>
              </PolicySection>

              {/* Policy Updates Section */}
              <PolicySection
                id="policy-updates"
                title="7. How Often Will We Update This Cookie Policy?"
                icon={<Calendar className="h-6 w-6" />}
                delay={6}
              >
                <p>
                  We may update this Cookie Policy from time to time in order to reflect, for example, changes to the cookies we use or for other operational, legal, or regulatory reasons. Please therefore re-visit this Cookie Policy regularly to stay informed about our use of cookies and related technologies.
                </p>
                <p>
                  The date at the top of this Cookie Policy indicates when it was last updated.
                </p>
              </PolicySection>

              {/* Contact Us Section */}
              <PolicySection
                id="contact"
                title="8. Contact Us"
                icon={<Mail className="h-6 w-6" />}
                delay={7}
              >
                <p>
                  If you have any questions about our use of cookies or other technologies, please contact us:
                </p>
                <ul className="list-disc pl-6 mb-6">
                  <li>By email: {siteConfig.contact.email}</li>
                  <li>By phone: {siteConfig.contact.phone}</li>
                  <li>By mail: {siteConfig.contact.address.full}</li>
                </ul>
              </PolicySection>
            </div>
          </div>
        </div>

        {/* Section Divider */}
        <SectionDivider variant="blue" className="my-8" />

        {/* CTA Section */}
        <PolicyCTASection relatedLinks={relatedLinks} />
      </div>
    </div>
  );
}
