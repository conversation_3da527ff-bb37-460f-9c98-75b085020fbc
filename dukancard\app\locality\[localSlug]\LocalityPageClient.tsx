"use client";

import { useSearchParams } from "next/navigation";
import { LocalityProvider } from "../context/LocalityContext";
import { PINCODE_PARAM, CITY_PARAM, LOCALITY_PARAM } from "@/app/(main)/discover/constants/urlParamConstants";
import { SerializableLocality } from "../context/types";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";

// Import components
import ImprovedSearchSection from "../components/ImprovedSearchSection";
import ErrorSection from "../components/ErrorSection";
import ModernResultsSection from "../components/ModernResultsSection";
import LocationIndicator from "../components/LocationIndicator";
import BreadcrumbNav from "../components/BreadcrumbNav";

interface LocalityPageClientProps {
  locality: SerializableLocality;
  initialBusinesses: BusinessProfilePublicData[];
  totalCount: number;
}

export default function LocalityPageClient({
  locality,
  initialBusinesses
}: LocalityPageClientProps) {
  const searchParams = useSearchParams();

  // Get initial values from URL
  const initialPincode = searchParams.get(PINCODE_PARAM);
  const initialCity = searchParams.get(CITY_PARAM);
  const initialLocality = searchParams.get(LOCALITY_PARAM);

  return (
    <LocalityProvider
      locality={locality}
      initialBusinesses={initialBusinesses}
    >
      <div className="relative min-h-screen overflow-hidden bg-white dark:bg-black">
        {/* Breadcrumb navigation */}
        <BreadcrumbNav />

        {/* Improved search section - full width, one line for desktop/tablet */}
        <ImprovedSearchSection
          initialValues={{
            pincode: initialPincode,
            city: initialCity,
            locality: initialLocality,
          }}
        />

        {/* Error Display */}
        <div className="container mx-auto px-4 my-2">
          <ErrorSection />
        </div>

        {/* Location Indicator */}
        <LocationIndicator />

        {/* Results Section */}
        <ModernResultsSection />
      </div>
    </LocalityProvider>
  );
}
