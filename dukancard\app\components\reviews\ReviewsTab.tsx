"use client";

import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  ReviewWithUser,
  ReviewSortBy,
  fetchBusinessReviews,
} from "@/lib/actions/reviews";
import { submitReview, deleteReview } from "@/lib/actions/interactions";
import AuthMessage from "./AuthMessage";
import ReviewsSummary from "./ReviewsSummary";
import ReviewForm from "./ReviewForm";
import ReviewsList from "./ReviewsList";
import ReviewSignInPrompt from "./ReviewSignInPrompt";

interface ReviewsTabProps {
  businessProfileId: string;
  isAuthenticated: boolean;
  currentUserId?: string | null;
  averageRating: number;
  totalReviews: number;
}

export default function ReviewsTab({
  businessProfileId,
  isAuthenticated,
  currentUserId,
  averageRating,
  totalReviews,
}: ReviewsTabProps) {
  // State for reviews list
  const [reviews, setReviews] = useState<ReviewWithUser[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [sortBy, setSortBy] = useState<ReviewSortBy>("newest");
  const [isLoadingReviews, setIsLoadingReviews] = useState<boolean>(true);

  // State for review form
  const [ratingValue, setRatingValue] = useState<number>(0);
  const [reviewText, setReviewText] = useState<string>("");
  const [isSubmittingReview, setIsSubmittingReview] = useState<boolean>(false);
  const [userHasReviewed, setUserHasReviewed] = useState<boolean>(false);
  // This state is used when setting the user's review ID
  const [_userReviewId, setUserReviewId] = useState<string | null>(null);

  // Constants
  const REVIEWS_PER_PAGE = 5;

  // Load reviews on mount and when page or sort changes
  useEffect(() => {
    loadReviews();
  }, [currentPage, sortBy, businessProfileId]); // eslint-disable-line react-hooks/exhaustive-deps

  // Function to load reviews
  const loadReviews = async () => {
    if (!businessProfileId) return;

    setIsLoadingReviews(true);
    try {
      const result = await fetchBusinessReviews(
        businessProfileId,
        currentPage,
        REVIEWS_PER_PAGE,
        sortBy
      );

      if (result.error) {
        toast.error(`Error loading reviews: ${result.error}`);
      } else {
        setReviews(result.data);
        setTotalCount(result.totalCount);

        // Check if current user has already reviewed
        if (isAuthenticated && currentUserId) {
          const userReview = result.data.find(
            (review) => review.user_id === currentUserId
          );
          if (userReview) {
            setUserHasReviewed(true);
            setUserReviewId(userReview.id);
            setRatingValue(userReview.rating);
            setReviewText(userReview.review_text || "");
          }
        }
      }
    } catch (error) {
      console.error("Error loading reviews:", error);
      toast.error("Failed to load reviews. Please try again later.");
    } finally {
      setIsLoadingReviews(false);
    }
  };

  // Handle review submission
  const handleReviewSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!businessProfileId || ratingValue === 0) {
      toast.warning("Please select a rating before submitting.");
      return;
    }

    setIsSubmittingReview(true);
    try {
      const result = await submitReview(
        businessProfileId,
        ratingValue,
        reviewText.trim() || null
      );

      if (result.success) {
        toast.success(
          userHasReviewed
            ? "Review updated successfully!"
            : "Review submitted successfully!"
        );
        setUserHasReviewed(true);

        // Reload reviews to show the new/updated review
        await loadReviews();
      } else {
        toast.error(
          `Failed to submit review: ${result.error || "Unknown error"}`
        );
      }
    } catch (error) {
      console.error("Error submitting review:", error);
      toast.error("Failed to submit review. Please try again later.");
    } finally {
      setIsSubmittingReview(false);
    }
  };

  // Handle review deletion
  const handleDeleteReview = async () => {
    if (!businessProfileId) return;

    const confirmation = confirm(
      "Are you sure you want to delete your review?"
    );
    if (!confirmation) return;

    setIsSubmittingReview(true);
    try {
      const result = await deleteReview(businessProfileId);

      if (result.success) {
        toast.success("Review deleted successfully.");
        setUserHasReviewed(false);
        setUserReviewId(null);
        setRatingValue(0);
        setReviewText("");

        // Reload reviews
        await loadReviews();
      } else {
        toast.error(
          `Failed to delete review: ${result.error || "Unknown error"}`
        );
      }
    } catch (error) {
      console.error("Error deleting review:", error);
      toast.error("Failed to delete review. Please try again later.");
    } finally {
      setIsSubmittingReview(false);
    }
  };

  // Calculate total pages for pagination
  const totalPages = Math.ceil(totalCount / REVIEWS_PER_PAGE);

  return (
    <div className="space-y-6">
      {/* Authentication Status Message */}
      <AuthMessage isAuthenticated={isAuthenticated} />

      {/* Reviews Summary */}
      <ReviewsSummary
        averageRating={averageRating}
        totalReviews={totalReviews}
        _sortBy={sortBy}
        onSortChange={setSortBy}
      />

      {/* Reviews List */}
      <ReviewsList
        reviews={reviews}
        totalReviews={totalCount}
        totalPages={totalPages}
        currentPage={currentPage}
        isLoadingReviews={isLoadingReviews}
        isAuthenticated={isAuthenticated}
        currentUserId={currentUserId}
        onPageChange={setCurrentPage}
        onDeleteReview={handleDeleteReview}
      />

      {/* Write a Review Section */}
      {isAuthenticated && currentUserId !== businessProfileId && (
        <ReviewForm
          userHasReviewed={userHasReviewed}
          ratingValue={ratingValue}
          reviewText={reviewText}
          isSubmittingReview={isSubmittingReview}
          onRatingChange={setRatingValue}
          onReviewTextChange={setReviewText}
          onSubmit={handleReviewSubmit}
          onDelete={handleDeleteReview}
        />
      )}

      {/* Message for business owners */}
      {isAuthenticated && currentUserId === businessProfileId && (
        <div className="relative p-4 sm:p-6 md:p-8 border rounded-xl bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-800/90 dark:to-neutral-900/90 shadow-lg hover:shadow-xl transition-all duration-300 border-neutral-200 dark:border-neutral-700/80 mb-6 sm:mb-10 overflow-hidden">
          <div className="flex flex-col items-center justify-center text-center p-4">
            <div className="text-amber-500 dark:text-amber-400 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-200 mb-2">
              Business Owner Notice
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-1">
              You cannot review your own business card.
            </p>
            <p className="text-sm text-neutral-500 dark:text-neutral-500">
              Business owners cannot leave reviews for their own businesses.
            </p>
          </div>
        </div>
      )}

      {/* Sign in prompt for non-authenticated users */}
      {!isAuthenticated && (
        <ReviewSignInPrompt pathname={window.location.pathname} />
      )}
    </div>
  );
}
