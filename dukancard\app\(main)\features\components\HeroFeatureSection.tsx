"use client";

import { useState, useEffect, useRef } from "react";
import { motion, useInView, useScroll, useTransform } from "framer-motion";
import { ArrowRight, Star, Sparkles, CreditCard, Store, BarChart3, Share2 } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function HeroFeatureSection() {
  const [isClient, setIsClient] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"],
  });

  // Parallax effect values
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  // Set client-side rendering flag
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Feature icons for the floating animation
  const featureIcons = [
    <CreditCard key="card" className="text-[var(--brand-gold)]" />,
    <Store key="store" className="text-blue-500" />,
    <BarChart3 key="chart" className="text-purple-500" />,
    <Share2 key="share" className="text-green-500" />,
    <Sparkles key="sparkles" className="text-amber-500" />,
  ];

  return (
    <section
      ref={sectionRef}
      className="relative w-full pt-24 pb-16 md:pt-32 md:pb-24 overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        {/* Main centered glow */}
        <motion.div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[400px] bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full blur-[120px]"
          animate={{
            opacity: [0.4, 0.6, 0.4],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />

        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 bg-[url('/decorative/subtle-pattern.svg')] bg-repeat opacity-5 dark:opacity-10"></div>

        {/* Floating feature icons */}
        {isClient && isInView && (
          <>
            {featureIcons.map((icon, index) => (
              <motion.div
                key={`icon-${index}`}
                className="absolute opacity-20 dark:opacity-30"
                style={{
                  left: `${15 + (index * 20)}%`,
                  top: `${20 + (index % 3) * 20}%`,
                  scale: 1.5 + (index % 3) * 0.5,
                }}
                animate={{
                  y: [0, -15, 0, 15, 0],
                  x: [0, 10, 0, -10, 0],
                  rotate: [0, 5, 0, -5, 0],
                }}
                transition={{
                  duration: 10 + index * 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                {icon}
              </motion.div>
            ))}
          </>
        )}
      </div>

      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-16 items-center">
          {/* Left Column: Text Content */}
          <motion.div
            className="w-full lg:w-1/2 text-center lg:text-left"
            style={{ opacity: isClient ? opacity : 1, y: isClient ? y : 0 }}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 tracking-tight">
                Powerful Features for Your{" "}
                <span className="text-[var(--brand-gold)] relative">
                  Digital Business
                  <motion.div
                    className="absolute -bottom-1 left-0 h-1 bg-[var(--brand-gold)]/30 rounded-full"
                    initial={{ width: 0 }}
                    animate={isInView ? { width: "100%" } : {}}
                    transition={{ duration: 1, delay: 0.5 }}
                  />
                </span>
              </h1>
            </motion.div>

            <motion.p
              className="text-lg md:text-xl text-muted-foreground mb-8 max-w-xl mx-auto lg:mx-0"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.7, delay: 0.2, ease: "easeOut" }}
            >
              Discover how Dukancard&apos;s comprehensive suite of tools can help your business thrive in the digital world.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.7, delay: 0.3, ease: "easeOut" }}
            >
              <Link href="/login">
                <Button className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900 px-8 py-6 rounded-full font-medium text-lg relative overflow-hidden group">
                  <span className="flex items-center">
                    Get Started Free
                    <motion.div
                      className="ml-2"
                      animate={{ x: [0, 5, 0] }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        repeatType: "loop",
                        ease: "easeInOut",
                      }}
                    >
                      <ArrowRight className="w-5 h-5" />
                    </motion.div>
                  </span>
                </Button>
              </Link>
            </motion.div>
          </motion.div>

          {/* Right Column: Feature Icons */}
          <motion.div
            className="w-full lg:w-1/2 mt-12 lg:mt-0"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.7, delay: 0.4, ease: "easeOut" }}
          >
            <div className="relative w-full max-w-lg mx-auto p-8 border border-neutral-200 dark:border-neutral-800 rounded-xl bg-neutral-50 dark:bg-neutral-900">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                {featureIcons.map((icon, index) => (
                  <motion.div
                    key={`grid-icon-${index}`}
                    className="flex flex-col items-center justify-center p-4 bg-white dark:bg-black rounded-lg border border-neutral-200 dark:border-neutral-800"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.5, delay: 0.5 + (index * 0.1) }}
                    whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
                  >
                    <div className="text-2xl mb-2">
                      {icon}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Decorative elements */}
              <motion.div
                className="absolute -top-6 -right-6 text-[var(--brand-gold)] opacity-70"
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                  scale: { duration: 3, repeat: Infinity, repeatType: "reverse" }
                }}
              >
                <Star size={24} />
              </motion.div>

              <motion.div
                className="absolute -bottom-6 -left-6 text-blue-500 opacity-70"
                animate={{
                  rotate: [0, -360],
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                  scale: { duration: 3, repeat: Infinity, repeatType: "reverse", delay: 1 }
                }}
              >
                <Star size={24} />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
