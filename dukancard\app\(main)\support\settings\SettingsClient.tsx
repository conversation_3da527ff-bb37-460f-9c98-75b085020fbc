"use client";

import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  User,
  <PERSON>,
  Shield,
  CreditCard,
  AlertCircle,
} from "lucide-react";
import EnhancedSupportSubPage from "../components/EnhancedSupportSubPage";

// Define FAQ data specific to Settings
const settingsFaqs = [
  {
    id: 1,
    question: "How do I change my email address?",
    answer: "To change your email address, go to Account > Settings in your dashboard. You'll find the email update section at the top of the page. Enter your new email address and click 'Update Email'. You'll need to verify the new email address before the change takes effect."
  },
  {
    id: 2,
    question: "Can I change my username or business slug?",
    answer: "Yes, you can change your business slug by going to Business Management > Manage Card in your dashboard. Find the 'Business Slug' field, enter your new preferred slug, and click 'Save'. Note that changing your slug will change your public URL, so update any shared links accordingly."
  },
  {
    id: 3,
    question: "How do I update my password?",
    answer: "To update your password, go to Account > Settings in your dashboard. Scroll down to the 'Password' section. Enter your current password and your new password, then click 'Update Password'. Choose a strong, unique password for better security."
  },
  {
    id: 4,
    question: "How do I manage notification preferences?",
    answer: "Currently, notification preferences are managed through your email service. You can mark emails from Dukancard as important or filter them based on your preferences. We're working on adding a dedicated notification settings section in a future update."
  },
  {
    id: 5,
    question: "Can I delete my account?",
    answer: "Yes, you can delete your account by going to Account > Settings in your dashboard. Scroll to the bottom of the page to find the 'Delete Account' section. This action is permanent and will remove all your data from our system, including your digital card, products, and analytics. Consider downloading any important data before proceeding."
  },
  {
    id: 6,
    question: "How do I change my language preferences?",
    answer: "Currently, Dukancard is available in English only. We're working on adding support for additional languages in future updates. When available, language preferences will be found in the Settings > Preferences section."
  },
  {
    id: 7,
    question: "Can I customize the appearance of my dashboard?",
    answer: "You can toggle between light and dark mode by clicking on your profile picture in the top right corner of the dashboard and selecting your preferred theme. More customization options for the dashboard will be available in future updates."
  },
  {
    id: 8,
    question: "How do I manage my privacy settings?",
    answer: "Go to Settings > Privacy to manage your privacy settings. You can control what information is visible on your public card and how your data is used. We recommend reviewing these settings periodically to ensure they align with your preferences."
  },
  {
    id: 9,
    question: "Can I link my social media accounts to my Dukancard account?",
    answer: "Currently, you can add your social media links to your digital card for display purposes, but direct account linking for login or cross-posting is not available. You can add these links in the Business Management > Manage Card section of your dashboard."
  },
  {
    id: 10,
    question: "How do I update my business information?",
    answer: "To update your business information, go to Business Management > Manage Card in your dashboard. Here you can modify your business name, address, contact information, and other details. All changes will be immediately reflected on your public digital card."
  },
];

// Define guide sections
const guideSections = [
  {
    id: "account-settings",
    title: "Account Settings",
    icon: <User className="w-6 h-6 text-[var(--brand-gold)]" />,
    content: [
      {
        title: "Managing Your Profile Information",
        steps: [
          "Log in to your Dukancard account and navigate to Account > Settings.",
          "Review your current profile information including your email address.",
          "To update your email, enter the new email address in the 'Email Update' section.",
          "Click 'Update Email' to apply the changes.",
          "You'll need to verify the new email address before the change takes effect."
        ],
        image: "/support/profile-settings.jpg",
        imageAlt: "Profile settings page",
        tip: "Keep your contact information up to date to ensure you receive important account notifications and can recover your account if needed."
      },
      {
        title: "Changing Your Password",
        steps: [
          "Go to Account > Settings in your dashboard.",
          "Scroll down to the 'Password' section.",
          "Enter your current password for verification.",
          "Create a new strong password and enter it in the 'New Password' field.",
          "Click 'Update Password' to save your new password."
        ],
        image: "/support/password-change.jpg",
        imageAlt: "Password change interface",
        tip: "Use a strong, unique password that includes a mix of uppercase and lowercase letters, numbers, and special characters. Consider using a password manager to keep track of your passwords securely."
      },
      {
        title: "Updating Your Business Slug",
        steps: [
          "Navigate to Business Management > Manage Card in your dashboard.",
          "Find the 'Business Slug' field in the business information section.",
          "Enter your new preferred slug (this will change your public URL).",
          "Ensure the slug is simple, memorable, and related to your business.",
          "Click 'Save' to update your slug and public URL (dukancard.in/your-slug)."
        ],
        image: "/support/slug-settings.jpg",
        imageAlt: "Business slug settings",
        tip: "After changing your slug, update any shared links or QR codes to reflect your new URL. Your old URL will no longer work, so inform your contacts about the change."
      }
    ]
  },
  {
    id: "notification-preferences",
    title: "Notification Preferences",
    icon: <Bell className="w-6 h-6 text-[var(--brand-gold)]" />,
    content: [
      {
        title: "Managing Email Notifications",
        steps: [
          "Go to Settings > Notifications in your dashboard.",
          "Review the list of available notification types.",
          "Toggle the switch next to each notification type to enable or disable it.",
          "Email notifications can include card views, contact button clicks, and system updates.",
          "Click 'Save Preferences' to apply your changes."
        ],
        image: "/support/email-notifications.jpg",
        imageAlt: "Email notification settings",
        tip: "Enable notifications for important business activities like contact button clicks, but consider disabling less critical notifications to avoid email overload."
      },
      {
        title: "Setting Up Digest Preferences",
        steps: [
          "In the Notifications section, find the 'Digest Emails' option.",
          "Choose your preferred frequency for digest emails (daily, weekly, or monthly).",
          "Select which metrics and updates you want to include in your digest.",
          "Digests can include summaries of card views, engagement metrics, and system updates.",
          "Click 'Save Preferences' to apply your digest settings."
        ],
        image: "/support/digest-settings.jpg",
        imageAlt: "Digest email settings",
        tip: "Weekly digests offer a good balance, providing regular updates without overwhelming your inbox. They're great for tracking trends over time."
      },
      {
        title: "Managing System Notifications",
        steps: [
          "Navigate to the 'System Notifications' section in Settings > Notifications.",
          "Review the types of system notifications available.",
          "Toggle notifications for account security, feature updates, and maintenance alerts.",
          "Choose your preferred notification channels for urgent alerts.",
          "Click 'Save Preferences' to apply your settings."
        ],
        image: "/support/system-notifications.jpg",
        imageAlt: "System notification settings",
        tip: "Always keep security notifications enabled to stay informed about important account security events or potential issues."
      }
    ]
  },
  {
    id: "privacy-security",
    title: "Privacy & Security",
    icon: <Shield className="w-6 h-6 text-[var(--brand-gold)]" />,
    content: [
      {
        title: "Managing Privacy Settings",
        steps: [
          "Go to Settings > Privacy in your dashboard.",
          "Review the current visibility settings for your profile information.",
          "Choose which elements of your business information are visible on your public card.",
          "Adjust settings for analytics tracking and data usage.",
          "Click 'Save Privacy Settings' to apply your changes."
        ],
        image: "/support/privacy-settings.jpg",
        imageAlt: "Privacy settings page",
        tip: "Balance privacy with business needs. While protecting sensitive information is important, ensuring customers can easily contact you is essential for business growth."
      },
      {
        title: "Enhancing Account Security",
        steps: [
          "Navigate to Settings > Account > Security.",
          "Review your current security settings and recent account activity.",
          "Enable additional security features like login notifications.",
          "Consider changing your password regularly (every 3-6 months).",
          "Review the devices and locations that have accessed your account."
        ],
        image: "/support/security-settings.jpg",
        imageAlt: "Security settings page",
        tip: "If you notice any suspicious activity in your account access history, immediately change your password and contact our support team."
      },
      {
        title: "Managing Data and Exports",
        steps: [
          "Go to Settings > Privacy > Data Management.",
          "Review what data is stored in your account.",
          "Use the 'Export Data' option to download a copy of your account data.",
          "Learn about data retention policies and how your information is protected.",
          "Find options for deleting specific data or your entire account if needed."
        ],
        image: "/support/data-management.jpg",
        imageAlt: "Data management settings",
        tip: "Periodically export your data for your records, especially before making significant changes to your account or business information."
      }
    ]
  }
];

export default function SettingsClient() {
  const quickHelp = [
    {
      title: "Account Settings Tips:",
      items: [
        { text: "Update your email in Account → Settings" },
        { text: "Change business slug in Business Management → Manage Card" },
        { text: "Use a strong, unique password for better security" },
      ]
    },
    {
      title: "Security Tips:",
      items: [
        { text: "Change your password regularly (every 3 months)" },
        { text: "Don't share your login credentials with others" },
        { text: "Log out when using shared or public devices" },
      ]
    }
  ];

  const navigationButtons = [
    { label: "Account Settings", href: "#account-settings" },
    { label: "Notifications", href: "#notification-preferences" },
    { label: "Privacy & Security", href: "#privacy-security" },
    { label: "FAQs", href: "#faqs" },
  ];

  const relatedResources = [
    {
      title: "Account & Billing",
      description: "Manage your account, subscription, and billing information",
      icon: <CreditCard className="w-6 h-6 text-[var(--brand-gold)]" />,
      href: "/support/account-billing"
    },
    {
      title: "Technical Issues",
      description: "Find solutions to common technical problems and troubleshooting",
      icon: <AlertCircle className="w-6 h-6 text-[var(--brand-gold)]" />,
      href: "/support/technical-issues"
    }
  ];

  return (
    <EnhancedSupportSubPage
      title="Settings & Preferences"
      description="Learn how to configure your account settings, manage notifications, and customize your Dukancard experience."
      icon={<Settings className="w-12 h-12 text-[var(--brand-gold)]" />}
      quickHelp={quickHelp}
      guideSections={guideSections}
      faqs={settingsFaqs}
      relatedResources={relatedResources}
      navigationButtons={navigationButtons}
    />
  );
}
