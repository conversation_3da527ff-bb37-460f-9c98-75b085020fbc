"use client";

import { useEffect, useState } from "react";
import { useInView } from "react-intersection-observer";
import { motion } from "framer-motion";

interface FuturisticScannerProps {
  className?: string;
}

export default function FuturisticScanner({ className = "" }: FuturisticScannerProps) {
  const [isScanning, setIsScanning] = useState(false);
  const { ref, inView } = useInView({
    threshold: 0.75,
    triggerOnce: false
  });

  // Start animation when in view
  useEffect(() => {
    if (inView) {
      setIsScanning(true);
    } else {
      setIsScanning(false);
    }
  }, [inView]);

  return (
    <div ref={ref} className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {/* Horizontal scanner line */}
      {isScanning && (
        <motion.div
          className="absolute h-0.5 w-full bg-[var(--brand-gold)]/70 left-0 top-0"
          initial={{ top: "0%" }}
          animate={{ top: "100%" }}
          transition={{
            duration: 3,
            ease: "linear",
            repeat: Infinity,
            repeatType: "loop"
          }}
        />
      )}

      {/* Vertical scanner line */}
      {isScanning && (
        <motion.div
          className="absolute w-0.5 h-full bg-[var(--brand-gold)]/70 left-0 top-0"
          initial={{ left: "0%" }}
          animate={{ left: "100%" }}
          transition={{
            duration: 3,
            ease: "linear",
            repeat: Infinity,
            repeatType: "loop"
          }}
        />
      )}

      {/* Corner highlights */}
      {isScanning && (
        <>
          <motion.div
            className="absolute w-8 h-8 border-t-2 border-l-2 border-[var(--brand-gold)] top-0 left-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
              duration: 0.5,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          <motion.div
            className="absolute w-8 h-8 border-t-2 border-r-2 border-[var(--brand-gold)] top-0 right-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
              duration: 0.5,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 0.2
            }}
          />

          <motion.div
            className="absolute w-8 h-8 border-b-2 border-l-2 border-[var(--brand-gold)] bottom-0 left-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
              duration: 0.5,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 0.4
            }}
          />

          <motion.div
            className="absolute w-8 h-8 border-b-2 border-r-2 border-[var(--brand-gold)] bottom-0 right-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
              duration: 0.5,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 0.6
            }}
          />
        </>
      )}

      {/* Digital scan effect overlay */}
      {isScanning && (
        <motion.div
          className="absolute inset-0 bg-[var(--brand-gold)]/5 backdrop-blur-[1px]"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.3 }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
      )}

      {/* Data points */}
      <div className="absolute inset-0">
        {isScanning && Array.from({ length: 10 }).map((_, i) => {
          const left = 10 + Math.random() * 80;
          const top = 10 + Math.random() * 80;
          const delay = Math.random() * 1;

          return (
            <motion.div
              key={`data-point-${i}`}
              className="absolute w-1.5 h-1.5 rounded-full bg-[var(--brand-gold)]"
              style={{
                left: `${left}%`,
                top: `${top}%`,
              }}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 0.8, scale: 1 }}
              transition={{
                duration: 0.5,
                delay: delay,
                repeat: Infinity,
                repeatType: "reverse",
                repeatDelay: 2
              }}
            />
          );
        })}
      </div>

      {/* Final "complete" flash */}
      {isScanning && (
        <motion.div
          className="absolute inset-0 bg-[var(--brand-gold)]/20"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            duration: 0.3,
            repeat: Infinity,
            repeatType: "reverse",
            repeatDelay: 5
          }}
        />
      )}
    </div>
  );
}
