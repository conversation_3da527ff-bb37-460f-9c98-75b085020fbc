// components/Footer.tsx
"use client";

import React from "react";
import Link from "next/link";
import { motion } from "framer-motion"; // Import motion
import { cn } from "@/lib/utils";
import { siteConfig } from "@/lib/site-config";

// Define types for our footer links
interface FooterLink {
  title: string;
  href: string;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

interface FooterProps {
  className?: string;
}

const Footer: React.FC<FooterProps> = ({ className }) => {
  // We don't need to use isMobile here since we're using CSS to hide on mobile
  // const isMobile = useIsMobile();
  // Define footer sections and links
  const footerSections: FooterSection[] = [
    {
      title: "Company",
      links: [
        { title: "About Us", href: "/about" },
        { title: "Contact Us", href: "/contact" },
      ],
    },
    {
      title: "Product",
      links: [
        { title: "Features", href: "/features" },
        { title: "Pricing", href: "/pricing" },
      ],
    },
    {
      title: "Resources",
      links: [
        { title: "Blog", href: "/blog" },
        { title: "Support", href: "/support" },
        { title: "Advertise", href: siteConfig.advertising.page },
      ],
    },
    {
      title: "Legal",
      links: [
        { title: "Privacy Policy", href: siteConfig.legal.privacyPolicy },
        { title: "Terms of Service", href: siteConfig.legal.termsOfService },
        { title: "Refund Policy", href: siteConfig.legal.refundPolicy },
        { title: "Cookie Policy", href: "/cookies" },
      ],
    },
  ];

  // Animation variants
  const footerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5, ease: "easeOut" } },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, delay: i * 0.05, ease: "easeOut" },
    }),
  };

  return (
    <motion.footer
      variants={footerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      // Use theme variables for background and border
      className={cn(
        "bg-gradient-to-b from-muted/50 to-background dark:from-background dark:to-black/30 border-t border-border pt-16 pb-20 md:pb-8 relative overflow-hidden",
        className
      )}
    >
      {/* Background Elements - Use theme colors */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute left-1/4 bottom-1/2 w-1/3 h-1/3 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl opacity-60"></div>
        <div className="absolute right-1/4 top-1/2 w-1/4 h-1/4 bg-blue-500/5 dark:bg-blue-500/10 rounded-full blur-3xl opacity-60"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        {/* Main Footer Content */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          transition={{ staggerChildren: 0.1 }}
          className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 md:gap-8 lg:gap-12 pb-12"
        >
          {/* Company Info */}
          <motion.div
            variants={itemVariants}
            custom={0}
            className="col-span-2 md:col-span-3 lg:col-span-2"
          >
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-foreground flex items-center">
                {" "}
                {/* Use foreground */}
                <span className="text-[var(--brand-gold)]">Dukan</span>card
              </h2>
              <p className="text-muted-foreground mt-4">
                {" "}
                {/* Use muted-foreground */}
                Elevate your business with our premium digital card solution.
                Connect with customers, showcase your offerings, and grow your
                brand.
              </p>
            </div>
            {/* Contact information removed as per request - available on Contact and About pages */}
          </motion.div>

          {/* Footer Link Sections */}
          {footerSections.map((section, index) => (
            <motion.div
              key={section.title} // Use title as key
              variants={itemVariants}
              custom={index + 1} // Stagger delay
              className="lg:col-span-1"
            >
              <h3 className="text-lg font-semibold text-foreground mb-4">
                {" "}
                {/* Use foreground */}
                {section.title}
              </h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.href}>
                    {" "}
                    {/* Use href as key */}
                    <Link
                      href={link.href}
                      className="text-muted-foreground hover:text-[var(--brand-gold)] transition-colors duration-200 text-sm" // Added text-sm
                    >
                      {link.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </motion.div>

        {/* Divider */}
        <div className="border-t border-border py-6">
          {" "}
          {/* Use border */}
          <div className="flex flex-col md:flex-row justify-center items-center">
            <p className="text-muted-foreground text-sm mb-4 md:mb-0 text-center">
              {" "}
              {/* Use muted-foreground */}© {new Date().getFullYear()}{" "}
              {siteConfig.name}. All rights reserved.
            </p>
            {/* Optional: Add social links here if desired */}
          </div>
        </div>
      </div>
    </motion.footer>
  );
};

export default Footer;
