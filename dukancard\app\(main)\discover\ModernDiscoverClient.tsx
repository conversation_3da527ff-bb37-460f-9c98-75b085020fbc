"use client";

import { useSearchParams } from "next/navigation";
import { DiscoverProvider } from "./context/DiscoverContext";
import { PINCODE_PARAM, CITY_PARAM, LOCALITY_PARAM } from "./constants/urlParamConstants";

// Import components
import ImprovedSearchSection from "./components/ImprovedSearchSection";
import CategoryCarousel from "./components/CategoryCarousel";
import ErrorSection from "./components/ErrorSection";
import ModernResultsSection from "./components/ModernResultsSection";

export default function ModernDiscoverClient() {
  const searchParams = useSearchParams();

  const initialPincode = searchParams.get(PINCODE_PARAM) || "";
  const initialCity = searchParams.get(CITY_PARAM) || "";
  const initialLocality = searchParams.get(LOCALITY_PARAM) || "";

  return (
    <DiscoverProvider>
      <div className="relative min-h-screen overflow-hidden bg-white dark:bg-black">
        {/* Improved search section - full width, one line for desktop/tablet */}
        <ImprovedSearchSection
          initialValues={{
            pincode: initialPincode,
            city: initialCity,
            locality: initialLocality,
          }}
        />

        {/* Category carousel */}
        <CategoryCarousel />

        {/* Error Display */}
        <div className="container mx-auto px-4 my-4">
          <ErrorSection />
        </div>

        {/* Results Section */}
        <ModernResultsSection />
      </div>
    </DiscoverProvider>
  );
}
