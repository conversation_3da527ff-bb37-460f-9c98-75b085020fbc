import { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { Loader2 } from "lucide-react";
import ModernTermsOfServiceClient from "./ModernTermsOfServiceClient";

export const metadata: Metadata = {
  title: "Terms of Service",
  description: "Read Dukancard's Terms of Service. Understand the rules, guidelines, and legal terms that govern the use of our digital business card platform.",
};

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense
        fallback={
          <div className="min-h-screen flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-[var(--brand-gold)]" />
          </div>
        }
      >
        <ModernTermsOfServiceClient />
      </Suspense>
    </div>
  );
}
