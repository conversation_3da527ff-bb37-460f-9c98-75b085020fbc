"use client";

import React from "react";
import ReviewsTabComponent from "./reviews/ReviewsTab";

interface ReviewsTabProps {
  businessProfileId: string;
  isAuthenticated: boolean;
  currentUserId?: string | null;
  averageRating: number;
  totalReviews: number;
}

// This is a wrapper component that re-exports the new modular ReviewsTab
export default function ReviewsTab(props: ReviewsTabProps) {
  return <ReviewsTabComponent {...props} />;
}
