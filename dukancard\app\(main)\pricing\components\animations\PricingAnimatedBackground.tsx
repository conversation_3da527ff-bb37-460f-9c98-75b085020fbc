"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { CreditCard, DollarSign, Co<PERSON>, Sparkles } from "lucide-react";

export default function PricingAnimatedBackground() {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const isDark = resolvedTheme === "dark";

  // Generate random positions for elements
  const generateRandomElements = (count: number) => {
    return Array.from({ length: count }).map((_, _i) => {
      const size = Math.random() * 40 + 20;
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      const duration = Math.random() * 10 + 15;
      const delay = Math.random() * 5;
      const moveX = (Math.random() - 0.5) * 30;
      const moveY = (Math.random() - 0.5) * 30;
      const rotate = Math.random() * 360;
      const rotateDirection = Math.random() > 0.5 ? 360 : -360;

      return { size, posX, posY, duration, delay, moveX, moveY, rotate, rotateDirection };
    });
  };

  const coins = generateRandomElements(8);
  const rupeeSymbols = generateRandomElements(6);
  const iconElements = generateRandomElements(4);

  // Icons to use
  const icons = [
    <CreditCard key="card" className="text-[var(--brand-gold)]" />,
    <DollarSign key="dollar" className="text-[var(--brand-gold)]" />,
    <Coins key="coins" className="text-[var(--brand-gold)]" />,
    <Sparkles key="sparkles" className="text-[var(--brand-gold)]" />
  ];

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Main gradient background */}
      <div
        className="absolute inset-0 bg-gradient-to-b from-background via-background to-background"
        style={{
          backgroundImage: isDark
            ? "radial-gradient(circle at 50% 0%, rgba(var(--brand-gold-rgb), 0.15) 0%, transparent 70%)"
            : "radial-gradient(circle at 50% 0%, rgba(var(--brand-gold-rgb), 0.08) 0%, transparent 70%)"
        }}
      />

      {/* Floating elements */}
      <div className="absolute inset-0">
        {/* Coin-like elements */}
        {coins.map((coin, i) => (
          <motion.div
            key={`coin-${i}`}
            className="absolute rounded-full"
            style={{
              width: `${coin.size}px`,
              height: `${coin.size}px`,
              border: `2px solid ${isDark ? 'rgba(var(--brand-gold-rgb), 0.3)' : 'rgba(var(--brand-gold-rgb), 0.2)'}`,
              left: `${coin.posX}%`,
              top: `${coin.posY}%`,
              boxShadow: `0 0 10px ${isDark ? 'rgba(var(--brand-gold-rgb), 0.2)' : 'rgba(var(--brand-gold-rgb), 0.1)'}`,
            }}
            initial={{
              x: 0,
              y: 0,
              opacity: 0.5,
              rotate: coin.rotate
            }}
            animate={{
              x: coin.moveX,
              y: coin.moveY,
              opacity: 0.8,
              rotate: coin.rotate + coin.rotateDirection
            }}
            transition={{
              x: {
                duration: coin.duration,
                repeat: Infinity,
                repeatType: "reverse",
                delay: coin.delay
              },
              y: {
                duration: coin.duration * 1.2,
                repeat: Infinity,
                repeatType: "reverse",
                delay: coin.delay
              },
              opacity: {
                duration: coin.duration / 2,
                repeat: Infinity,
                repeatType: "reverse",
                delay: coin.delay
              },
              rotate: {
                duration: coin.duration * 2,
                repeat: Infinity,
                ease: "linear"
              }
            }}
          />
        ))}

        {/* Rupee symbols */}
        {rupeeSymbols.map((symbol, i) => (
          <motion.div
            key={`rupee-${i}`}
            className="absolute text-[var(--brand-gold)] font-bold"
            style={{
              fontSize: `${symbol.size}px`,
              left: `${symbol.posX}%`,
              top: `${symbol.posY}%`,
              opacity: isDark ? 0.3 : 0.2
            }}
            initial={{ y: 0, rotate: symbol.rotate, scale: 1 }}
            animate={{ 
              y: symbol.moveY / 2, 
              rotate: symbol.rotate + symbol.rotateDirection / 4,
              scale: [1, 1.1, 1]
            }}
            transition={{
              y: {
                duration: symbol.duration,
                repeat: Infinity,
                repeatType: "reverse"
              },
              rotate: {
                duration: symbol.duration * 1.5,
                repeat: Infinity,
                repeatType: "reverse"
              },
              scale: {
                duration: symbol.duration / 2,
                repeat: Infinity,
                repeatType: "reverse"
              }
            }}
          >
            ₹
          </motion.div>
        ))}

        {/* Icon elements */}
        {iconElements.map((element, i) => (
          <motion.div
            key={`icon-${i}`}
            className="absolute"
            style={{
              fontSize: `${element.size}px`,
              left: `${element.posX}%`,
              top: `${element.posY}%`,
              opacity: isDark ? 0.3 : 0.2
            }}
            initial={{ scale: 0.8, rotate: element.rotate }}
            animate={{ 
              scale: [0.8, 1, 0.8], 
              rotate: element.rotate + element.rotateDirection / 2,
              x: element.moveX / 3,
              y: element.moveY / 3
            }}
            transition={{
              scale: {
                duration: element.duration / 2,
                repeat: Infinity,
                repeatType: "reverse"
              },
              rotate: {
                duration: element.duration * 2,
                repeat: Infinity,
                ease: "linear"
              },
              x: {
                duration: element.duration,
                repeat: Infinity,
                repeatType: "reverse"
              },
              y: {
                duration: element.duration * 1.3,
                repeat: Infinity,
                repeatType: "reverse"
              }
            }}
          >
            {icons[i % icons.length]}
          </motion.div>
        ))}

        {/* Blurred circles */}
        <motion.div
          className="absolute w-96 h-96 rounded-full blur-3xl opacity-20 dark:opacity-10"
          style={{
            background: isDark
              ? "radial-gradient(circle, rgba(var(--brand-gold-rgb), 0.4) 0%, transparent 70%)"
              : "radial-gradient(circle, rgba(var(--brand-gold-rgb), 0.2) 0%, transparent 70%)",
            top: "10%",
            right: "5%",
          }}
          initial={{ scale: 1, opacity: isDark ? 0.1 : 0.2 }}
          animate={{ scale: 1.2, opacity: isDark ? 0.2 : 0.3 }}
          transition={{
            duration: 15,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />

        <motion.div
          className="absolute w-80 h-80 rounded-full blur-3xl opacity-10 dark:opacity-5"
          style={{
            background: isDark
              ? "radial-gradient(circle, rgba(var(--brand-gold-rgb), 0.3) 0%, transparent 70%)"
              : "radial-gradient(circle, rgba(var(--brand-gold-rgb), 0.15) 0%, transparent 70%)",
            bottom: "20%",
            left: "10%",
          }}
          initial={{ scale: 1, opacity: isDark ? 0.05 : 0.1 }}
          animate={{ scale: 1.3, opacity: isDark ? 0.15 : 0.2 }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse",
            delay: 5
          }}
        />
      </div>
    </div>
  );
}
