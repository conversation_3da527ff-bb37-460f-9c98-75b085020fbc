"use client";

import { motion } from "framer-motion";
import { sectionFadeIn, itemFadeIn } from "@/app/(main)/components/landing/animations";
import { Button } from "@/components/ui/button";
import { Phone, Mail, MapPin } from "lucide-react";
import { siteConfig } from "@/lib/site-config";
import SectionBackground from "@/app/(main)/components/SectionBackground";
import { useState } from "react";
import HubSpotForm from "./HubSpotForm";

export default function ContactSection() {
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const phoneNumber = siteConfig.advertising.phone;
  const email = siteConfig.advertising.email;

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(type);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (_err) {
      // Silently fail if clipboard copy doesn't work
    }
  };

  const handleCall = () => {
    window.location.href = `tel:${phoneNumber}`;
  };

  const handleEmail = () => {
    window.location.href = `mailto:${email}?subject=Advertising%20Inquiry`;
  };

  // Simple animation for icons
  const simpleAnimation = {
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  };

  return (
    <motion.section
      id="contact-section"
      variants={sectionFadeIn}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      className="py-16 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto relative"
    >
      {/* Enhanced background with SectionBackground component */}
      <div className="absolute inset-0 -z-10">
        <SectionBackground variant="blue" intensity="low" />
      </div>

      <motion.div
        variants={itemFadeIn}
        custom={0}
        className="text-center mb-12"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Get in{" "}
          <span className="text-[var(--brand-gold)] relative">
            Touch
            {/* Animated underline */}
            <motion.div
              className="absolute -bottom-1 left-0 h-1 bg-gradient-to-r from-[var(--brand-gold)]/30 via-[var(--brand-gold)] to-[var(--brand-gold)]/30 rounded-full"
              initial={{ width: 0, left: "50%" }}
              animate={{ width: "100%", left: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Contact our advertising team to discuss your requirements and get a customized quote for your business.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
        <motion.div
          variants={itemFadeIn}
          custom={1}
          className="bg-white/90 dark:bg-black/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 rounded-2xl p-8 shadow-lg"
        >
          <h3 className="text-2xl font-semibold mb-6 text-foreground">Contact Information</h3>

          <div className="space-y-6">
            {/* Phone */}
            <div className="flex items-start space-x-4">
              <motion.div
                animate={simpleAnimation}
                className="bg-[var(--brand-gold)]/10 p-3 rounded-full"
              >
                <Phone className="h-6 w-6 text-[var(--brand-gold)]" />
              </motion.div>
              <div>
                <h4 className="font-medium text-foreground">Phone</h4>
                <p className="text-muted-foreground mt-1">{phoneNumber}</p>
                <div className="flex space-x-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCall}
                    className="text-xs"
                  >
                    Call Now
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(phoneNumber, 'phone')}
                    className="text-xs"
                  >
                    {copiedText === 'phone' ? 'Copied!' : 'Copy'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Email */}
            <div className="flex items-start space-x-4">
              <motion.div
                animate={simpleAnimation}
                className="bg-[var(--brand-gold)]/10 p-3 rounded-full"
              >
                <Mail className="h-6 w-6 text-[var(--brand-gold)]" />
              </motion.div>
              <div>
                <h4 className="font-medium text-foreground">Email</h4>
                <p className="text-muted-foreground mt-1">{email}</p>
                <div className="flex space-x-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleEmail}
                    className="text-xs"
                  >
                    Send Email
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(email, 'email')}
                    className="text-xs"
                  >
                    {copiedText === 'email' ? 'Copied!' : 'Copy'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Location */}
            <div className="flex items-start space-x-4">
              <motion.div
                animate={simpleAnimation}
                className="bg-[var(--brand-gold)]/10 p-3 rounded-full"
              >
                <MapPin className="h-6 w-6 text-[var(--brand-gold)]" />
              </motion.div>
              <div>
                <h4 className="font-medium text-foreground">Location</h4>
                <p className="text-muted-foreground mt-1">{siteConfig.contact.address.full}</p>
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          variants={itemFadeIn}
          custom={2}
          className="text-center md:text-left"
        >
          <div className="bg-white/90 dark:bg-black/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 rounded-2xl p-8 shadow-lg">
            <h3 className="text-2xl font-semibold mb-6 text-foreground">How It Works</h3>

            <ol className="space-y-6 relative">
              {/* Connecting line */}
              <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-neutral-200 dark:bg-neutral-800"></div>

              {/* Step 1 */}
              <li className="relative pl-12">
                <div className="absolute left-0 flex items-center justify-center w-8 h-8 rounded-full bg-[var(--brand-gold)] text-black font-bold">1</div>
                <h4 className="font-medium text-foreground">Contact Us</h4>
                <p className="text-muted-foreground mt-1">Reach out via phone or email to discuss your advertising needs.</p>
              </li>

              {/* Step 2 */}
              <li className="relative pl-12">
                <div className="absolute left-0 flex items-center justify-center w-8 h-8 rounded-full bg-[var(--brand-gold)] text-black font-bold">2</div>
                <h4 className="font-medium text-foreground">Get a Quote</h4>
                <p className="text-muted-foreground mt-1">We&apos;ll provide a customized quote based on your target locations and duration.</p>
              </li>

              {/* Step 3 */}
              <li className="relative pl-12">
                <div className="absolute left-0 flex items-center justify-center w-8 h-8 rounded-full bg-[var(--brand-gold)] text-black font-bold">3</div>
                <h4 className="font-medium text-foreground">Submit Your Ad</h4>
                <p className="text-muted-foreground mt-1">Provide your ad creative or let us help you design one.</p>
              </li>

              {/* Step 4 */}
              <li className="relative pl-12">
                <div className="absolute left-0 flex items-center justify-center w-8 h-8 rounded-full bg-[var(--brand-gold)] text-black font-bold">4</div>
                <h4 className="font-medium text-foreground">Go Live</h4>
                <p className="text-muted-foreground mt-1">Your ad will be displayed to users in your target locations.</p>
              </li>
            </ol>
          </div>
        </motion.div>

        {/* HubSpot Form */}
        <HubSpotForm />
      </div>
    </motion.section>
  );
}
