"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { CreditCard, Store, BarChart3, Share2, Smartphone, Users } from "lucide-react";

export default function FeatureAnimation() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Feature icons for the animation
  const featureIcons = [
    { icon: <CreditCard className="text-[var(--brand-gold)]" />, label: "Digital Card" },
    { icon: <Store className="text-blue-500" />, label: "Products" },
    { icon: <BarChart3 className="text-purple-500" />, label: "Analytics" },
    { icon: <Share2 className="text-green-500" />, label: "Sharing" },
    { icon: <Smartphone className="text-amber-500" />, label: "Mobile" },
    { icon: <Users className="text-cyan-500" />, label: "Team" },
  ];

  if (!isClient) return null;

  return (
    <div className="relative h-16 md:h-20 mb-8 overflow-hidden">
      {/* Central line */}
      <motion.div
        className="absolute top-1/2 left-0 right-0 h-0.5 bg-neutral-200 dark:bg-neutral-800"
        initial={{ scaleX: 0 }}
        animate={{ scaleX: 1 }}
        transition={{ duration: 1, ease: "easeInOut" }}
      />

      {/* Feature icons */}
      <div className="absolute top-0 left-0 right-0 bottom-0 flex justify-between items-center">
        {featureIcons.map((item, index) => (
          <motion.div
            key={index}
            className="flex flex-col items-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <motion.div
              className="relative z-10 bg-white dark:bg-black p-2 rounded-full border border-neutral-200 dark:border-neutral-800"
              whileHover={{ scale: 1.1, y: -5 }}
              animate={{
                y: [0, -5, 0],
              }}
              transition={{
                y: {
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                  delay: index * 0.5,
                },
              }}
            >
              {item.icon}
              
              {/* Glow effect */}
              <motion.div
                className="absolute inset-0 rounded-full blur-sm -z-10"
                style={{
                  backgroundColor: `rgba(var(--${
                    item.icon.props.className.includes("text-[var(--brand-gold)]")
                      ? "brand-gold"
                      : item.icon.props.className.includes("text-blue-500")
                      ? "blue-500"
                      : item.icon.props.className.includes("text-purple-500")
                      ? "purple-500"
                      : item.icon.props.className.includes("text-green-500")
                      ? "green-500"
                      : item.icon.props.className.includes("text-amber-500")
                      ? "amber-500"
                      : "cyan-500"
                  }-rgb), 0.2)`,
                }}
                animate={{
                  opacity: [0.2, 0.4, 0.2],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                  delay: index * 0.3,
                }}
              />
            </motion.div>
            
            <motion.span
              className="text-xs mt-2 text-neutral-600 dark:text-neutral-400 hidden md:block"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
            >
              {item.label}
            </motion.span>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
