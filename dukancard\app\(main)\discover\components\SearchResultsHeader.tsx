"use client";

import { Package, Building2, RefreshCw } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";

interface SearchResultsHeaderProps {
  totalCount: number;
  viewType: "cards" | "products";
  searchParams: {
    businessName?: string | null;
    pincode?: string | null;
    locality?: string | null;
    city?: string | null;
    location?: {
      city: string;
      state: string;
    } | null;
  };
}

export default function SearchResultsHeader({
  totalCount,
  viewType,
  searchParams,
}: SearchResultsHeaderProps) {
  const { businessName, pincode, locality, city } = searchParams;

  // Determine if we have any search filters applied
  const hasFilters = businessName || pincode || locality || city;

  // Function to reset all filters and show all India businesses/products
  const handleResetFilters = () => {
    // Use window.location.href to force a full page reload
    window.location.href = "/discover";
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      className="mb-8 bg-white/90 dark:bg-neutral-800/90 rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50 shadow-lg p-6 text-center backdrop-blur-sm"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-3xl mx-auto">
        {/* Results Count and Reset Button */}
        <motion.div
          className="flex flex-col sm:flex-row items-center justify-between gap-4"
          variants={itemVariants}
        >
          <div className="inline-flex items-center px-4 py-1.5 rounded-full bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20">
            {viewType === "cards" ? (
              <Building2 className="h-4 w-4 mr-2 text-[var(--brand-gold)]" />
            ) : (
              <Package className="h-4 w-4 mr-2 text-[var(--brand-gold)]" />
            )}
            <span className="text-neutral-700 dark:text-neutral-300 font-medium">
              {totalCount > 0 ? (
                <span>
                  {totalCount}{" "}
                  {viewType === "cards" ? "businesses" : "products"}
                </span>
              ) : (
                <span>
                  No {viewType === "cards" ? "businesses" : "products"} found
                </span>
              )}
            </span>
          </div>

          {/* Reset Filters Button - Only show when filters are applied */}
          {hasFilters && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                onClick={handleResetFilters}
                variant="outline"
                size="sm"
                className="flex items-center gap-2 bg-white dark:bg-neutral-800 border-[var(--brand-gold)]/30 dark:border-[var(--brand-gold)]/30 hover:bg-[var(--brand-gold)]/10 dark:hover:bg-[var(--brand-gold)]/20 text-neutral-700 dark:text-neutral-300 shadow-sm transition-all duration-300"
              >
                <RefreshCw className="h-3.5 w-3.5 text-[var(--brand-gold)]" />
                <span>Reset Filters</span>
              </Button>
            </motion.div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
}
