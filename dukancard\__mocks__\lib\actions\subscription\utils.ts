// Mock for @/lib/actions/subscription/utils

export const getCurrentUser = jest.fn();
export const getBusinessProfile = jest.fn();
export const getUserSubscription = jest.fn();
export const getUserAndProfile = jest.fn();

// Default mock implementations
getCurrentUser.mockResolvedValue(null);
getBusinessProfile.mockResolvedValue(null);
getUserSubscription.mockResolvedValue(null);
getUserAndProfile.mockResolvedValue({
  error: "User not authenticated",
  user: null,
  profile: null,
  subscription: null,
});
