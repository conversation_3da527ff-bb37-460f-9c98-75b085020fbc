"use client";

import { useEffect, useRef, useState } from "react";
import { motion, useInView, Variants } from "framer-motion";
import { MapPin, Navigation, Clock, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { SiteConfig } from "@/lib/site-config";

interface ContactMapSectionProps {
  address: SiteConfig["contact"]["address"];
  sectionFadeIn: Variants;
  itemFadeIn: (_delay?: number) => Variants;
}

export default function ContactMapSection({
  address,
  sectionFadeIn,
  itemFadeIn,
}: ContactMapSectionProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(mapRef, { once: true, amount: 0.3 });
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleOpenMap = () => {
    window.open("https://maps.app.goo.gl/m6FfJHyYLC53HZiR7", "_blank");
  };

  // Map pin animation
  const mapPinAnimation = {
    initial: { y: -20, opacity: 0 },
    animate: {
      y: [0, -15, 0],
      opacity: 1,
      transition: {
        y: {
          duration: 2,
          repeat: Infinity,
          repeatType: "loop",
          ease: "easeInOut",
        },
        opacity: {
          duration: 0.5,
        }
      }
    }
  };

  // Directions animation
  const directionsAnimation = {
    rest: { scale: 1 },
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 }
    },
    tap: {
      scale: 0.98,
      transition: { duration: 0.1 }
    }
  };

  return (
    <motion.section
      id="contact-map-section"
      variants={sectionFadeIn}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      className="py-16 px-4 container mx-auto max-w-7xl"
    >
      <motion.div variants={itemFadeIn(0)} className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Find <span className="text-[var(--brand-gold)]">Us</span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Visit our office to meet the team and discuss your business needs in person.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Map Card - Takes 2/3 of the space on desktop */}
        <motion.div
          variants={itemFadeIn(1)}
          className="lg:col-span-2"
          ref={mapRef}
        >
          <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 md:p-8 h-full">
            <div className="w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden border border-border dark:border-[var(--brand-gold)]/20 relative">
              {/* Map placeholder with animation */}
              <div className="absolute inset-0 bg-muted dark:bg-gradient-to-br dark:from-neutral-800 dark:to-black flex items-center justify-center">
                {isClient && isInView && (
                  <>
                    {/* Map background with subtle grid */}
                    <div className="absolute inset-0 bg-[url('/map-grid.svg')] opacity-10 dark:opacity-20"></div>

                    {/* Animated location pin */}
                    <motion.div
                      initial="initial"
                      animate="animate"
                      variants={mapPinAnimation}
                      className="relative z-10"
                    >
                      <div className="relative">
                        <MapPin className="w-16 h-16 text-[var(--brand-gold)]" />

                        {/* Ripple effect */}
                        <motion.div
                          className="absolute -bottom-2 left-1/2 -translate-x-1/2 w-8 h-8 bg-[var(--brand-gold)]/30 rounded-full"
                          initial={{ scale: 0, opacity: 1 }}
                          animate={{
                            scale: 3,
                            opacity: 0,
                          }}
                          transition={{
                            repeat: Infinity,
                            duration: 2,
                            ease: "easeOut"
                          }}
                        />
                      </div>

                      {/* Location label */}
                      <div className="absolute top-full left-1/2 -translate-x-1/2 mt-2 bg-white dark:bg-black px-3 py-1 rounded-full shadow-md border border-neutral-200 dark:border-neutral-800">
                        <p className="text-sm font-medium whitespace-nowrap">
                          {address.city}, {address.state}
                        </p>
                      </div>
                    </motion.div>

                    {/* Open in Google Maps button */}
                    <motion.div
                      className="absolute bottom-4 right-4"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 1, duration: 0.5 }}
                    >
                      <Button
                        onClick={handleOpenMap}
                        variant="outline"
                        size="sm"
                        className="bg-white/90 dark:bg-black/70 backdrop-blur-sm border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group"
                      >
                        <ExternalLink className="mr-2 h-4 w-4 text-[var(--brand-gold)] group-hover:scale-110 transition-transform" />
                        Open in Google Maps
                      </Button>
                    </motion.div>
                  </>
                )}
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Directions Card - Takes 1/3 of the space on desktop */}
        <motion.div
          variants={itemFadeIn(2)}
          className="lg:col-span-1"
        >
          <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 md:p-8 h-full">
            <h3 className="text-xl font-semibold text-foreground mb-6 flex items-center">
              <Navigation className="mr-2 h-5 w-5 text-[var(--brand-gold)]" />
              Directions
            </h3>

            <div className="space-y-6">
              {/* Address */}
              <div className="flex items-start space-x-4">
                <div className="bg-[var(--brand-gold)]/10 p-2 rounded-full">
                  <MapPin className="h-5 w-5 text-[var(--brand-gold)]" />
                </div>
                <div>
                  <h4 className="font-medium text-foreground">Our Address</h4>
                  <address className="not-italic text-muted-foreground mt-1">
                    {address.street}<br />
                    {address.city}, {address.state} - {address.postalCode}<br />
                    {address.country}
                  </address>
                </div>
              </div>

              {/* Business Hours */}
              <div className="flex items-start space-x-4">
                <div className="bg-[var(--brand-gold)]/10 p-2 rounded-full">
                  <Clock className="h-5 w-5 text-[var(--brand-gold)]" />
                </div>
                <div>
                  <h4 className="font-medium text-foreground">Office Hours</h4>
                  <ul className="text-muted-foreground mt-1 space-y-1">
                    <li className="flex justify-between">
                      <span>Monday - Friday:</span>
                      <span>9:00 AM - 6:00 PM</span>
                    </li>
                    <li className="flex justify-between">
                      <span>Saturday - Sunday:</span>
                      <span>Closed</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Get Directions Button */}
              <motion.div
                variants={directionsAnimation}
                whileHover="hover"
                whileTap="tap"
                className="mt-6"
              >
                <Button
                  onClick={handleOpenMap}
                  className="w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium"
                >
                  <Navigation className="mr-2 h-4 w-4" />
                  Get Directions
                </Button>
              </motion.div>

              {/* Additional Info */}
              <div className="mt-4 pt-4 border-t border-neutral-200 dark:border-neutral-800">
                <p className="text-muted-foreground text-sm">
                  Parking is available in the building&apos;s underground parking lot. Please call us when you arrive, and we&apos;ll guide you to our office.
                </p>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </motion.section>
  );
}
