import {
  isValidIndianMobile,
  isValidEmail,
  isValidPincode,
  validateProfileName,
  validatePhone,
  validateEmail,
  validateMobile,
  validateAddress,
  cleanProfileData,
  cleanPhoneData,
  cleanEmailData,
  cleanMobileData,
  cleanAddressData,
} from '@/backend/supabase/utils/profileValidation';

describe('profileValidation', () => {
  describe('validation functions', () => {
    it('isValidIndianMobile', () => {
      expect(isValidIndianMobile('9876543210')).toBe(true);
      expect(isValidIndianMobile('5555555555')).toBe(false);
      expect(isValidIndianMobile('987654321')).toBe(false);
    });

    it('isValidEmail', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('invalid-email')).toBe(false);
    });

    it('isValidPincode', () => {
      expect(isValidPincode('123456')).toBe(true);
      expect(isValidPincode('12345')).toBe(false);
    });

    // ... tests for other validation functions ...
  });

  describe('data cleaning functions', () => {
    it('cleanProfileData', () => {
      expect(cleanProfileData({ name: '  Test User  ' })).toEqual({ name: 'Test User' });
      expect(cleanProfileData({ name: null })).toEqual({ name: null });
    });

    it('cleanPhoneData', () => {
      expect(cleanPhoneData({ phone: ' (************* ' })).toEqual({ phone: '9876543210' });
    });

    it('cleanEmailData', () => {
      expect(cleanEmailData({ email: '  <EMAIL>  ' })).toEqual({ email: '<EMAIL>' });
    });

    it('cleanMobileData', () => {
      expect(cleanMobileData({ mobile: ' +91 98765 43210 ' })).toEqual({ mobile: '919876543210' });
    });

    it('cleanAddressData', () => {
      const address = {
        address: '  123 Main St  ',
        pincode: '123456  ',
        city: '  Anytown  ',
        state: '  State  ',
        locality: '  Locality  ',
      };
      const cleaned = {
        address: '123 Main St',
        pincode: '123456',
        city: 'Anytown',
        state: 'State',
        locality: 'Locality',
      };
      expect(cleanAddressData(address)).toEqual(cleaned);
    });
  });
});