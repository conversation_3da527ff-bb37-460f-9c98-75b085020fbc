"use client";

import { useState, useRef, useEffect } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { useRouter } from "next/navigation";
import { pricingPlans, PricingPlan } from "@/lib/PricingPlans";
import {
  pricingPageFAQ,
  pricingPageCTA,
} from "@/lib/siteContent";

// Import our enhanced components
import PricingAnimatedBackground from "./components/animations/PricingAnimatedBackground";
import EnhancedPricingHero from "./components/EnhancedPricingHero";

import EnhancedPricingCards from "./components/EnhancedPricingCards";
import EnhancedPricingFAQ from "./components/EnhancedPricingFAQ";
import EnhancedPricingCTA from "./components/EnhancedPricingCTA";
import SectionDivider from "../components/landing/SectionDivider";
import FeatureComparisonTable from "../components/shared/FeatureComparisonTable";

// Animation variants
type AnimationVariant = {
  hidden: { opacity: number; y: number };
  visible: {
    opacity: number;
    y: number;
    transition: { duration: number; ease: string };
  } | ((_i: number) => {
    opacity: number;
    y: number;
    transition: { duration: number; delay: number; ease: string };
  });
};

const sectionFadeIn: AnimationVariant = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" },
  },
};

const itemFadeIn: AnimationVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { duration: 0.5, delay: i * 0.1, ease: "easeOut" },
  }),
};

export default function EnhancedPricingPageClient() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly");
  const [isLoaded, setIsLoaded] = useState(false);
  const router = useRouter();
  const plans = pricingPlans(billingCycle);
  const pageRef = useRef<HTMLDivElement>(null);

  // Scroll-based animations
  const { scrollYProgress } = useScroll({
    target: pageRef,
    offset: ["start start", "end start"],
  });

  const backgroundOpacity = useTransform(
    scrollYProgress,
    [0, 0.2, 0.8, 1],
    [1, 0.8, 0.4, 0.2]
  );

  // Handle initial animation on page load
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const handleGetStarted = () => {
    router.push(`/register`);
  };

  const handlePlanClick = (plan: PricingPlan) => {
    if (plan.id === "enterprise") {
      // For Enterprise, redirect to contact page
      router.push("/contact");
    } else if (plan.available) {
      handleGetStarted();
    }
  };

  return (
    <div
      ref={pageRef}
      className="min-h-screen bg-background overflow-hidden relative"
    >
      {/* Animated background with parallax effect */}
      <motion.div
        style={{ opacity: backgroundOpacity }}
        className="fixed inset-0 -z-10"
      >
        <PricingAnimatedBackground />
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoaded ? 1 : 0 }}
        transition={{ duration: 0.5 }}
        className="w-full pt-8 sm:pt-10 md:pt-12 mt-2 sm:mt-3 md:mt-4"
      >
        {/* Hero Section */}
        <EnhancedPricingHero />

        {/* Pricing Cards */}
        <EnhancedPricingCards
          plans={plans}
          onButtonClick={handlePlanClick}
          itemFadeIn={itemFadeIn}
          billingCycle={billingCycle}
          setBillingCycle={setBillingCycle}
        />

        {/* Section Divider */}
        <SectionDivider variant="gold" />

        {/* Features Comparison */}
        <motion.div
          variants={sectionFadeIn}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="py-16 px-4 md:px-6 lg:px-8"
        >
          <FeatureComparisonTable
            plans={plans}
            title="Feature Comparison"
            subtitle="Compare features across plans to find the perfect fit for your business needs."
          />
        </motion.div>

        {/* Section Divider */}
        <SectionDivider variant="purple" />

        {/* FAQ Section */}
        <EnhancedPricingFAQ
          faqItems={pricingPageFAQ}
          sectionFadeIn={sectionFadeIn}
          itemFadeIn={itemFadeIn}
        />

        {/* Section Divider */}
        <SectionDivider variant="blue" />

        {/* CTA Section */}
        <EnhancedPricingCTA
          title={pricingPageCTA.title}
          description={pricingPageCTA.description}
          buttons={pricingPageCTA.buttons}
          sectionFadeIn={sectionFadeIn}
          itemFadeIn={itemFadeIn}
        />
      </motion.div>
    </div>
  );
}
