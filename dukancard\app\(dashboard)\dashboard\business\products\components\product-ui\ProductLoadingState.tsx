"use client";

import { motion } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";

interface ProductLoadingStateProps {
  view: "table" | "grid";
  count?: number;
}

export default function ProductLoadingState({ view, count = 10 }: ProductLoadingStateProps) {
  if (view === "table") {
    return (
      <div className="rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden shadow-sm">
        {/* Table Header Skeleton */}
        <div className="border-b border-neutral-200/60 dark:border-neutral-800/60 bg-gradient-to-r from-neutral-50/80 to-neutral-100/40 dark:from-neutral-800/50 dark:to-neutral-900/30 p-4">
          <div className="grid grid-cols-8 gap-4">
            <Skeleton className="h-4 w-8" />
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-12" />
          </div>
        </div>

        {/* Table Rows Skeleton */}
        <div className="divide-y divide-neutral-100/60 dark:divide-neutral-800/60">
          {[...Array(count)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: i * 0.05 }}
              className="p-4"
            >
              <div className="grid grid-cols-8 gap-4 items-center">
                {/* Expand button */}
                <Skeleton className="h-8 w-8 rounded-lg" />

                {/* Image */}
                <Skeleton className="h-12 w-12 rounded-xl" />

                {/* Product details */}
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>

                {/* Category */}
                <Skeleton className="h-6 w-16 rounded-full" />

                {/* Variants */}
                <div className="space-y-1">
                  <Skeleton className="h-4 w-8" />
                  <Skeleton className="h-3 w-12" />
                </div>

                {/* Pricing */}
                <div className="space-y-1">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-3 w-12" />
                </div>

                {/* Status */}
                <Skeleton className="h-6 w-20 rounded-full" />

                {/* Actions */}
                <Skeleton className="h-8 w-8 rounded-lg" />
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {[...Array(count)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: i * 0.1 }}
          className="rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden shadow-sm"
        >
          {/* Image skeleton */}
          <Skeleton className="w-full aspect-square" />

          {/* Content skeleton */}
          <div className="p-6 space-y-4">
            {/* Product name & category */}
            <div className="space-y-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-5 w-20 rounded-full" />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>

            {/* Variant info */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-16 rounded-full" />
              <Skeleton className="h-3 w-2" />
              <Skeleton className="h-4 w-12" />
            </div>

            {/* Pricing */}
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-10" />
                <div className="text-right space-y-1">
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-4 w-16" />
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
