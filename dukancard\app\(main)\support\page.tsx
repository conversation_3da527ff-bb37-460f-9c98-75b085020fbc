import SupportPageClient from "./SupportPageClient";
import { Metadata } from "next";
import { siteConfig } from "@/lib/site-config";

export async function generateMetadata(): Promise<Metadata> {
  const title = `Support Center`
  const description =
    "Get help with Dukancard. Find answers to frequently asked questions and contact our support team for assistance with your digital business card.";
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || siteConfig.url;
  const pageUrl = `${siteUrl}/support`;
  const ogImage = `${siteUrl}/opengraph-image.png`;

  return {
    title,
    description,
    keywords: [
      "Dukancard support",
      "digital business card help",
      "Dukancard FAQ",
      "Dukancard customer service",
      "business card troubleshooting",
      "Dukancard contact",
    ],
    alternates: {
      canonical: "/support",
    },
    openGraph: {
      title,
      description,
      url: pageUrl,
      siteName: siteConfig.name,
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: `${siteConfig.name} Support Center`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [ogImage],
    },
    // Add WebPage Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: siteConfig.name,
          url: siteUrl,
        },
      }),
    },
  };
}

export default function SupportPage() {
  return <SupportPageClient />;
}
