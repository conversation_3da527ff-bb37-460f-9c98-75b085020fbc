"use client";

interface AuthMethodToggleProps {
  authMethod: 'email-otp' | 'mobile-password';
  step: 'email' | 'otp';
  onMethodChange: (_method: 'email-otp' | 'mobile-password') => void;
}

export function AuthMethodToggle({ authMethod, step, onMethodChange }: AuthMethodToggleProps) {
  // Only show toggle when on email step or mobile-password method
  if (!(authMethod === 'email-otp' && step === 'email') && authMethod !== 'mobile-password') {
    return null;
  }

  return (
    <div className="flex rounded-lg bg-muted p-1 mb-6">
      <button
        type="button"
        onClick={() => onMethodChange('email-otp')}
        className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
          authMethod === 'email-otp'
            ? 'bg-background text-foreground shadow-sm'
            : 'text-muted-foreground hover:text-foreground'
        }`}
      >
        Email OTP
      </button>
      <button
        type="button"
        onClick={() => onMethodChange('mobile-password')}
        className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
          authMethod === 'mobile-password'
            ? 'bg-background text-foreground shadow-sm'
            : 'text-muted-foreground hover:text-foreground'
        }`}
      >
        Mobile + Password
      </button>
    </div>
  );
}
