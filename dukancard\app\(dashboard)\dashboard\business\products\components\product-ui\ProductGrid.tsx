"use client";

import { motion } from "framer-motion";
import { Edit, Trash2, Package, MoreHorizontal, IndianRupee } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Image from "next/image";
import Link from "next/link";
import { viewTransitionVariants } from "../../types";
import { useProducts } from "../../context/ProductsContext";
import ProductEmptyState from "./ProductEmptyState";

export default function ProductGrid() {
  const {
    products,
    isLoading,
    isPending,
    deletingProductId,
    setDeletingProductId,
  } = useProducts();

  if (!isLoading && products.length === 0) {
    return <ProductEmptyState view="grid" />;
  }

  return (
    <motion.div
      key="grid-view"
      variants={viewTransitionVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
    >
      {products.map((product, index) => (
        <motion.div
          key={product.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: index * 0.1 }}
          className={`
            group relative rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60
            bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden
            hover:border-neutral-300/60 dark:hover:border-neutral-700/60
            hover:shadow-lg hover:shadow-neutral-200/20 dark:hover:shadow-neutral-900/20
            transition-all duration-300
            ${isPending && deletingProductId === product.id ? 'opacity-50 pointer-events-none' : ''}
          `}
          whileHover={{
            y: -4,
            transition: { duration: 0.2 }
          }}
        >
          {/* Product Image */}
          <div className="relative aspect-square overflow-hidden bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-800 dark:to-neutral-900">
            {product.image_url ? (
              <Image
                src={product.image_url}
                alt={product.name ?? "Product image"}
                className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
                fill
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Package className="w-16 h-16 text-neutral-300 dark:text-neutral-600" />
              </div>
            )}

            {/* Overlay gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            {/* Status badge */}
            <div className="absolute top-3 right-3">
              <Badge
                variant={product.is_available ? "default" : "secondary"}
                className={`
                  text-xs font-medium px-2 py-1 rounded-full border-0 shadow-sm
                  ${product.is_available
                    ? 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400'
                    : 'bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-400'
                  }
                `}
              >
                {product.is_available ? 'Available' : 'Unavailable'}
              </Badge>
            </div>

            {/* Variant count badge */}
            {product.variant_count > 0 && (
              <div className="absolute top-3 left-3">
                <Badge className="bg-primary/90 text-primary-foreground text-xs font-medium px-2 py-1 rounded-full border-0 shadow-sm">
                  {product.variant_count} variant{product.variant_count > 1 ? 's' : ''}
                </Badge>
              </div>
            )}

            {/* Actions overlay */}
            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="secondary"
                    size="sm"
                    disabled={isPending}
                    className="bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm border border-neutral-200/60 dark:border-neutral-700/60 shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="center"
                  className="w-48 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm shadow-xl"
                >
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/dashboard/business/products/edit/${product.id}`}
                      className="flex items-center gap-2 px-3 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors duration-200 cursor-pointer"
                    >
                      <Edit className="h-4 w-4" />
                      Edit Product
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <button
                      onClick={() => setDeletingProductId(product.id!)}
                      disabled={isPending || deletingProductId === product.id}
                      className="flex items-center gap-2 px-3 py-2 text-sm text-rose-600 dark:text-rose-400 hover:bg-rose-50 dark:hover:bg-rose-900/20 rounded-lg transition-colors duration-200 cursor-pointer w-full text-left disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete Product
                    </button>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Product Details */}
          <div className="p-6 space-y-4">
            {/* Product Name & Category */}
            <div className="space-y-2">
              <h3 className="font-semibold text-lg text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight">
                {product.name}
              </h3>
              <Badge
                variant="secondary"
                className="capitalize text-xs font-medium bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 border-0 px-2 py-1 rounded-full"
              >
                {product.product_type}
              </Badge>
            </div>

            {/* Description */}
            {product.description && (
              <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2 leading-relaxed">
                {product.description}
              </p>
            )}

            {/* Variant Info */}
            {product.variant_count > 0 && (
              <div className="flex items-center gap-2 text-xs">
                <Badge className="bg-primary/10 text-primary border-0 px-2 py-1 rounded-full">
                  {product.variant_count} variant{product.variant_count > 1 ? 's' : ''}
                </Badge>
                <span className="text-neutral-400">•</span>
                <span className="text-emerald-600 dark:text-emerald-400 font-medium">
                  {product.available_variant_count} active
                </span>
              </div>
            )}

            {/* Pricing */}
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">Price</span>
                <div className="text-right">
                  <div className="font-bold text-lg text-neutral-900 dark:text-neutral-100 flex items-center">
                    <IndianRupee className="h-4 w-4 mr-1" />
                    {product.base_price?.toLocaleString("en-IN") ?? "—"}
                  </div>
                  {product.discounted_price && (
                    <div className="text-sm text-emerald-600 dark:text-emerald-400 font-medium flex items-center justify-end">
                      <IndianRupee className="h-3 w-3 mr-1" />
                      {product.discounted_price.toLocaleString("en-IN")}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      ))}
    </motion.div>
  );
}
