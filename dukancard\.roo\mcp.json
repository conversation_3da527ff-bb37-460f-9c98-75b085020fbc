{"mcpServers": {"supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "alwaysAllow": ["list_projects", "list_tables", "execute_sql", "list_functions", "list_edge_functions", "get_advisors"]}, "github.com/upstash/context7-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": [], "alwaysAllow": ["get-library-docs", "resolve-library-id"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}, "n8n-mcp": {"command": "npx", "args": ["n8n-mcp"], "env": {"MCP_MODE": "stdio", "LOG_LEVEL": "error", "DISABLE_CONSOLE_OUTPUT": "true"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "kite": {"command": "npx", "args": ["mcp-remote", "https://mcp.kite.trade/sse"]}}}