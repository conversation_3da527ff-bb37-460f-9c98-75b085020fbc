import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Button, ButtonProps } from '@/src/components/ui/Button';
import { Text } from 'react-native';

// Mock the useColorScheme hook
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

const mockOnPress = jest.fn();

const defaultProps: ButtonProps = {
  title: 'Test Button',
  onPress: mockOnPress,
};

const renderComponent = (props: Partial<ButtonProps> = {}) => {
  return render(<Button {...defaultProps} {...props} />);
};

describe('<Button />', () => {
  beforeEach(() => {
    mockOnPress.mockClear();
  });

  it('renders correctly with default props', () => {
    const { getByText, toJSON } = renderComponent();
    expect(getByText('Test Button')).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it('calls onPress when pressed', () => {
    const { getByText } = renderComponent();
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('does not call onPress when disabled', () => {
    const { getByText } = renderComponent({ disabled: true });
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('shows a loading indicator when loading', () => {
    const { getByTestId, queryByText } = render(
      <Button {...defaultProps} loading={true} />
    );
    // queryByText is used because the title might not be rendered when loading
    expect(queryByText('Test Button')).toBeTruthy(); 
  });

  it('does not call onPress when loading', () => {
    const { getByText } = renderComponent({ loading: true });
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('renders with a custom icon', () => {
    const Icon = () => <Text testID="icon">Icon</Text>;
    const { getByTestId } = renderComponent({ icon: <Icon /> });
    expect(getByTestId('icon')).toBeTruthy();
  });

  it('does not render the icon when loading', () => {
    const Icon = () => <Text testID="icon">Icon</Text>;
    const { queryByTestId } = renderComponent({ icon: <Icon />, loading: true });
    expect(queryByTestId('icon')).toBeNull();
  });

  describe('variants and sizes', () => {
    it('renders primary variant correctly', () => {
      const { toJSON } = renderComponent({ variant: 'primary' });
      expect(toJSON()).toMatchSnapshot();
    });

    it('renders secondary variant correctly', () => {
      const { toJSON } = renderComponent({ variant: 'secondary' });
      expect(toJSON()).toMatchSnapshot();
    });

    it('renders outline variant correctly', () => {
      const { toJSON } = renderComponent({ variant: 'outline' });
      expect(toJSON()).toMatchSnapshot();
    });

    it('renders small size correctly', () => {
      const { toJSON } = renderComponent({ size: 'small' });
      expect(toJSON()).toMatchSnapshot();
    });

    it('renders medium size correctly', () => {
      const { toJSON } = renderComponent({ size: 'medium' });
      expect(toJSON()).toMatchSnapshot();
    });

    it('renders large size correctly', () => {
      const { toJSON } = renderComponent({ size: 'large' });
      expect(toJSON()).toMatchSnapshot();
    });
  });
});