"use client";

import { useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchDiscoverCombined } from "../actions/combinedActions";
import { fetchMoreProductsCombined } from "../actions/productActions";
import { DISCOVER_PRODUCTS_PER_PAGE } from "../constants/paginationConstants";
import { NearbyProduct } from "../actions/types";
import {
  PRODUCT_NAME_PARAM,
  PRODUCT_SORT_PARAM,
  PRODUCT_TYPE_PARAM,
  PINCODE_PARAM,
  LOCALITY_PARAM,
} from "../constants/urlParamConstants";
import {
  DiscoverSearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
} from "./types";
import { mapProductSortToBackend } from "../utils/sortMappings";

// Product context functions
export function useProductContextFunctions(
  viewType: ViewType,
  setIsSearching: (_value: boolean) => void,
  setSearchResult: (_value: DiscoverSearchResult | null) => void,
  setIsAuthenticated: (_value: boolean) => void,
  setProducts: (_value: NearbyProduct[]) => void,
  setHasMore: (_value: boolean) => void,
  setTotalCount: (_value: number) => void,
  setCurrentPage: (_value: number) => void,
  setProductSortBy: (_value: ProductSortOption) => void,
  setProductFilterBy: (_value: ProductFilterOption) => void,
  setSearchError: (_value: string | null) => void,
  products: NearbyProduct[],
  sortBy: BusinessSortBy,
  productSortBy: ProductSortOption,
  productFilterBy: ProductFilterOption
) {
  const searchParams = useSearchParams();
  const [, startSearchTransition] = useTransition();

  // Handle product sort change
  const handleProductSortChange = (sortOption: ProductSortOption) => {
    if (sortOption !== productSortBy) {
      setProductSortBy(sortOption);
      setIsSearching(true);

      // Update URL to include the sort option
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        // Set the new parameter and remove the old one
        url.searchParams.set(PRODUCT_SORT_PARAM, sortOption);
        url.searchParams.delete("sortBy");
        window.history.replaceState({}, "", url.toString());
      }

      // Map the product sort option to a backend sort value
      const mappedSortBy = mapProductSortToBackend(sortOption);

      // If we already have search results, perform the search again with the new sort
      if (viewType === "products") {
        const productName = searchParams.get(PRODUCT_NAME_PARAM) || null;
        const pincode = searchParams.get(PINCODE_PARAM) || null;
        let locality = searchParams.get(LOCALITY_PARAM) || null;

        // Handle "_any" locality value
        if (locality === "_any") {
          locality = "";
        }

        startSearchTransition(async () => {
          const result = await searchDiscoverCombined({
            businessName: null,
            productName,
            pincode,
            locality,
            viewType,
            page: 1,
            limit: DISCOVER_PRODUCTS_PER_PAGE,
            productSort: mappedSortBy,
            productType: productFilterBy === "all" ? null : productFilterBy,
          });

          if (result.data) {
            setSearchResult(result.data);
            setIsAuthenticated(result.data.isAuthenticated);

            if (viewType === "products" && result.data.products) {
              setProducts(result.data.products);
            }

            setHasMore(result.data.hasMore);
            setTotalCount(result.data.totalCount);
            setCurrentPage(1);
          } else {
            setSearchError(result.error || "Failed to fetch results.");
          }
          setIsSearching(false);
        });
      }
    }
  };

  // Handle product search
  const handleProductSearch = (term: string) => {
    // Prevent unnecessary API calls
    if (viewType !== "products") {
      return;
    }

    setIsSearching(true);

    // Add a flag to prevent duplicate calls
    const currentSearchTerm = term;

    // Update URL to include the search term
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href);
      if (currentSearchTerm) {
        url.searchParams.set(PRODUCT_NAME_PARAM, currentSearchTerm);
      } else {
        url.searchParams.delete(PRODUCT_NAME_PARAM);
      }
      window.history.replaceState({}, "", url.toString());
    }

    // If search term is empty, reset to initial state
    if (!currentSearchTerm) {
      // If we have pincode/locality, search with those parameters
      const pincode = searchParams.get(PINCODE_PARAM) || null;
      let locality = searchParams.get(LOCALITY_PARAM) || null;

      // Handle "_any" locality value
      if (locality === "_any") {
        locality = "";
      }

      startSearchTransition(async () => {
        // Add a small delay to prevent race conditions
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Map the product sort option to a backend sort value
        const mappedSortBy = mapProductSortToBackend(productSortBy);

        const result = await searchDiscoverCombined({
          businessName: null,
          productName: null,
          pincode,
          locality,
          viewType,
          page: 1,
          limit: DISCOVER_PRODUCTS_PER_PAGE,
          productSort: mappedSortBy,
          productType: productFilterBy === "all" ? null : productFilterBy,
        });

        if (result.data) {
          setSearchResult(result.data);
          setIsAuthenticated(result.data.isAuthenticated);

          if (viewType === "products" && result.data.products) {
            setProducts(result.data.products);
          }

          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(1);
        } else {
          setSearchError(result.error || "Failed to fetch results.");
        }
        setIsSearching(false);
      });
      return;
    }

    // If search term is provided, search with it
    const pincode = searchParams.get(PINCODE_PARAM) || null;
    let locality = searchParams.get(LOCALITY_PARAM) || null;

    // Handle "_any" locality value
    if (locality === "_any") {
      locality = "";
    }

    // Reset state before new search
    setSearchError(null);
    setProducts([]);
    setCurrentPage(1);
    setHasMore(false);
    setTotalCount(0);

    startSearchTransition(async () => {
      // Add a small delay to prevent race conditions
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Map the product sort option to a backend sort value
      const mappedSortBy = mapProductSortToBackend(productSortBy);

      const result = await searchDiscoverCombined({
        businessName: null,
        productName: currentSearchTerm,
        pincode,
        locality,
        viewType,
        page: 1,
        limit: DISCOVER_PRODUCTS_PER_PAGE,
        productSort: mappedSortBy,
        productType: productFilterBy === "all" ? null : productFilterBy,
      });

      if (result.data) {
        setSearchResult(result.data);
        setIsAuthenticated(result.data.isAuthenticated);

        if (viewType === "products" && result.data.products) {
          setProducts(result.data.products);
        }

        setHasMore(result.data.hasMore);
        setTotalCount(result.data.totalCount);
        setCurrentPage(1);
      } else {
        setSearchError(result.error || "Failed to fetch results.");
      }
      setIsSearching(false);
    });
  };

  // Handle product filter change
  const handleProductFilterChange = (filter: ProductFilterOption) => {
    if (filter !== productFilterBy) {
      setProductFilterBy(filter);
      setIsSearching(true);

      // Update URL to include the filter option
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        url.searchParams.set(PRODUCT_TYPE_PARAM, filter);
        window.history.replaceState({}, "", url.toString());
      }

      // If we already have search results, perform the search again with the new filter
      if (viewType === "products") {
        const productName = searchParams.get(PRODUCT_NAME_PARAM) || null;
        const pincode = searchParams.get(PINCODE_PARAM) || null;
        let locality = searchParams.get(LOCALITY_PARAM) || null;

        // Handle "_any" locality value
        if (locality === "_any") {
          locality = "";
        }

        startSearchTransition(async () => {
          // Map the product sort option to a backend sort value
          const mappedSortBy = mapProductSortToBackend(productSortBy);

          const result = await searchDiscoverCombined({
            businessName: null,
            productName,
            pincode,
            locality,
            viewType,
            page: 1,
            limit: DISCOVER_PRODUCTS_PER_PAGE,
            productSort: mappedSortBy,
            productType: filter === "all" ? null : filter,
          });

          if (result.data) {
            setSearchResult(result.data);
            setIsAuthenticated(result.data.isAuthenticated);

            if (viewType === "products" && result.data.products) {
              setProducts(result.data.products);
            }

            setHasMore(result.data.hasMore);
            setTotalCount(result.data.totalCount);
            setCurrentPage(1);
          } else {
            setSearchError(result.error || "Failed to fetch results.");
          }
          setIsSearching(false);
        });
      }
    }
  };

  // Load more products
  const loadMoreProducts = async (
    nextPage: number,
    isLoadingMore: boolean,
    setIsLoadingMore: (_value: boolean) => void
  ) => {
    if (isLoadingMore) {
      return;
    }

    const productName = searchParams.get(PRODUCT_NAME_PARAM) || null;
    const pincode = searchParams.get(PINCODE_PARAM) || null;
    let locality = searchParams.get(LOCALITY_PARAM) || null;

    // Handle "_any" locality value
    if (locality === "_any") {
      locality = "";
    }

    setIsLoadingMore(true);

    try {
      // Add a small delay to prevent race conditions
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Map the product sort option to a backend sort value
      const mappedSortBy = mapProductSortToBackend(productSortBy);

      const result = await fetchMoreProductsCombined({
        businessName: null,
        productName,
        pincode,
        locality,
        page: nextPage,
        limit: DISCOVER_PRODUCTS_PER_PAGE,
        productSort: mappedSortBy,
        productType: productFilterBy === "all" ? null : productFilterBy,
      });

      if (result.data && result.data.products) {
        if (result.data.products.length > 0) {
          const newProducts = result.data.products;

          // Filter out any products that already exist in the current list to avoid duplicates
          const existingIds = new Set(products.map((p) => p.id));
          const uniqueNewProducts = newProducts.filter(
            (product) => !existingIds.has(product.id)
          );

          if (uniqueNewProducts.length > 0) {
            // Combine previous products with new ones
            const updatedProducts = [...products, ...uniqueNewProducts];
            setProducts(updatedProducts);
            // Use the hasMore flag from the server
            setHasMore(result.data.hasMore);
            setCurrentPage(nextPage);
            return true;
          } else {
            // If all new products were duplicates, stop loading more
            console.warn(
              "All new products were duplicates, stopping infinite scroll"
            );
            setHasMore(false);
            return false;
          }
        } else {
          setHasMore(false);
          return false;
        }
      } else {
        setHasMore(false);
        return false;
      }
    } catch (error) {
      console.error("Error loading more products:", error);
      setHasMore(false);
      return false;
    } finally {
      setIsLoadingMore(false);
    }
  };

  return {
    handleProductSortChange,
    handleProductSearch,
    handleProductFilterChange,
    loadMoreProducts,
  };
}
