"use server";

import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
// Removed unused headers import

export async function signOutUser() {
  const supabase = await createClient();

  try {
    const { error: _error } = await supabase.auth.signOut();
    // Note: Sign out errors are typically not critical for user experience
    // The user will be redirected to login regardless

    // Explicitly clear auth cookies to ensure logout
    const cookieStore = await import("next/headers").then((m) => m.cookies());
    const cookiesToClear = ["sb-access-token", "sb-refresh-token"];

    for (const cookieName of cookiesToClear) {
      try {
        cookieStore.set(cookieName, "", {
          expires: new Date(0),
          maxAge: -1,
        });
      } catch {
        // Cookie clearing errors are not critical for sign out
        // Continue with the sign out process
      }
    }
  } catch {
    // Even if sign out fails, redirect to login for security
    // User will be treated as logged out
  }

  // Redirect to login with a flag to prevent middleware redirect loop
  return redirect("/login?logged_out=true");
}
