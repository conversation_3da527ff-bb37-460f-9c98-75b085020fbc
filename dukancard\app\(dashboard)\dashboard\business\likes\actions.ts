import { createClient } from '@/utils/supabase/server';
import { createAdminClient } from '@/utils/supabase/admin';

// Define interfaces for the expected data structure
interface BusinessProfileDataForLike {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface CustomerProfileDataForLike {
  id: string;
  name: string | null;
  email: string | null;
  avatar_url: string | null;
}

export interface BusinessLikeReceived {
  id: string;
  user_id: string;
  customer_profiles?: CustomerProfileDataForLike | null;
  business_profiles?: BusinessProfileDataForLike | null;
  profile_type: 'customer' | 'business';
}

export interface BusinessMyLike {
  id: string;
  business_profiles: BusinessProfileDataForLike | null;
}

interface LikesReceivedResult {
  items: BusinessLikeReceived[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

interface MyLikesResult {
  items: BusinessMyLike[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Fetch likes received by a business (customers/businesses who liked this business)
 */
export async function fetchBusinessLikesReceived(
  businessId: string,
  page: number = 1,
  limit: number = 10
): Promise<LikesReceivedResult> {
  const supabase = await createClient();
  const supabaseAdmin = createAdminClient();

  try {
    // Get total count first
    const { count: totalCount, error: countError } = await supabase
      .from('likes')
      .select('id', { count: 'exact', head: true })
      .eq('business_profile_id', businessId);

    if (countError) {
      throw new Error("Failed to get total count");
    }

    if (!totalCount || totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page
      };
    }

    // Get likes with pagination (database-level pagination)
    const from = (page - 1) * limit;
    const { data: likes, error: likesError } = await supabase
      .from('likes')
      .select('id, user_id')
      .eq('business_profile_id', businessId)
      .range(from, from + limit - 1);

    if (likesError) {
      throw new Error("Failed to fetch likes");
    }

    if (!likes || likes.length === 0) {
      return {
        items: [],
        totalCount,
        hasMore: false,
        currentPage: page
      };
    }

    // Get user IDs for the paginated results only
    const userIds = likes.map((like: { user_id: string }) => like.user_id);

    // Fetch customer and business profiles for paginated results only
    const [customerProfiles, businessProfiles] = await Promise.all([
      supabaseAdmin
        .from('customer_profiles')
        .select('id, name, email, avatar_url')
        .in('id', userIds),
      supabaseAdmin
        .from('business_profiles')
        .select('id, business_name, business_slug, logo_url, city, state, pincode, address_line')
        .in('id', userIds)
    ]);

    // Create maps for easy lookup
    const customerProfilesMap = new Map(
      customerProfiles.data?.map(profile => [profile.id, profile]) || []
    );
    const businessProfilesMap = new Map(
      businessProfiles.data?.map(profile => [profile.id, profile]) || []
    );

    // Combine likes with their corresponding profiles
    const processedLikes = likes
      .map((like: { id: string; user_id: string }) => {
        const customerProfile = customerProfilesMap.get(like.user_id);
        const businessProfile = businessProfilesMap.get(like.user_id);

        if (customerProfile) {
          return {
            id: like.id,
            user_id: like.user_id,
            customer_profiles: customerProfile,
            profile_type: 'customer' as const
          };
        } else if (businessProfile) {
          return {
            id: like.id,
            user_id: like.user_id,
            business_profiles: businessProfile,
            profile_type: 'business' as const
          };
        }
        return null;
      })
      .filter((item: BusinessLikeReceived | null): item is BusinessLikeReceived => item !== null);

    const hasMore = totalCount > from + limit;

    return {
      items: processedLikes,
      totalCount,
      hasMore,
      currentPage: page
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Fetch businesses that this business has liked
 */
export async function fetchBusinessMyLikes(
  businessId: string,
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ""
): Promise<MyLikesResult> {
  const _supabase = await createClient();
  const supabaseAdmin = createAdminClient();

  try {
    // Build the query with proper joins and filtering
    let query = supabaseAdmin
      .from('likes')
      .select(`
        id,
        business_profiles!inner (
          id,
          business_name,
          business_slug,
          logo_url,
          city,
          state,
          pincode,
          address_line
        )
      `)
      .eq('user_id', businessId);

    // Apply search filter if provided
    if (searchTerm) {
      query = query.ilike('business_profiles.business_name', `%${searchTerm}%`);
    }

    // Get total count for pagination with proper join for search
    let countQuery = supabaseAdmin
      .from('likes')
      .select(`
        id,
        business_profiles!inner (
          id,
          business_name
        )
      `, { count: 'exact', head: true })
      .eq('user_id', businessId);

    // Apply search filter to count query if provided
    if (searchTerm) {
      countQuery = countQuery.ilike('business_profiles.business_name', `%${searchTerm}%`);
    }

    const { count: totalCount, error: countError } = await countQuery;

    if (countError) {
      throw new Error("Failed to get total count");
    }

    // If no likes, return empty result
    if (!totalCount || totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page
      };
    }

    // Apply pagination to the query
    const from = (page - 1) * limit;
    query = query.range(from, from + limit - 1);

    const { data: likesWithProfiles, error: likesError } = await query;

    if (likesError) {
      throw new Error("Failed to fetch likes");
    }

    // Transform the data to match BusinessMyLike interface
    const transformedItems: BusinessMyLike[] = (likesWithProfiles || []).map(item => ({
      id: item.id,
      business_profiles: Array.isArray(item.business_profiles)
        ? item.business_profiles[0] || null
        : item.business_profiles
    }));

    const hasMore = totalCount > from + limit;

    return {
      items: transformedItems,
      totalCount,
      hasMore,
      currentPage: page
    };
  } catch (error) {
    console.error('Error in fetchBusinessMyLikes:', error);
    throw error;
  }
}
