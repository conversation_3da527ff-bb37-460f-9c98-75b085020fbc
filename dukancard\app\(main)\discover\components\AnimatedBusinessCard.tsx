"use client";

import Link from "next/link";
import Image from "next/image";
import { motion, useInView } from "framer-motion";
import { Heart, MapPin, Star, UserPlus } from "lucide-react";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { formatIndianNumberShort } from "@/lib/utils";
import { useState, useRef } from "react";

interface AnimatedBusinessCardProps {
  business: BusinessCardData;
  index: number;
}

export default function AnimatedBusinessCard({ business, index }: AnimatedBusinessCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: false, amount: 0.2 });

  // Format address components for better display
  const getAddressComponents = () => {
    // First line: address_line
    const addressLine = business.address_line || "";

    // Second line: locality, city
    const localityCity = [business.locality, business.city]
      .filter(Boolean)
      .join(", ");

    // Third line: state, pincode
    const statePin = [
      business.state,
      business.pincode ? `PIN: ${business.pincode}` : null
    ]
      .filter(Boolean)
      .join(", ");

    return {
      addressLine,
      localityCity,
      statePin,
      hasAddress: !!(addressLine || localityCity || statePin)
    };
  };

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.4, delay: index * 0.05 }}
      className="h-full"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Link href={`/${business.business_slug}`} className="block h-full">
        <motion.div
          className="relative h-full overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-sm transition-all duration-300"
          animate={{
            boxShadow: isHovered
              ? "0 10px 25px -5px rgba(var(--brand-gold-rgb), 0.2), 0 8px 10px -6px rgba(var(--brand-gold-rgb), 0.1)"
              : "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
            borderColor: isHovered
              ? "rgba(var(--brand-gold-rgb), 0.3)"
              : ""
          }}
        >
          {/* Card content */}
          <div className="p-3 sm:p-4 flex flex-col h-full">
            {/* Header with logo and name - centered design */}
            <div className="flex flex-col items-center text-center mb-4">
              <div className="relative flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 rounded-full overflow-hidden border-2 border-[var(--brand-gold)]/30 mb-2">
                {business.logo_url && business.logo_url.trim() !== "" ? (
                  <Image
                    src={business.logo_url}
                    alt={business.business_name || "Business logo"}
                    fill
                    className="object-cover"
                    sizes="(max-width: 640px) 48px, 64px"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] text-xl font-semibold">
                    {business.business_name?.charAt(0) || "B"}
                  </div>
                )}

                {/* Animated ring on hover */}
                {isHovered && (
                  <motion.div
                    className="absolute inset-0 rounded-full border-2 border-[var(--brand-gold)]"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                )}
              </div>

              <div className="min-w-0 w-full px-2">
                <motion.h3
                  className="font-semibold text-neutral-900 dark:text-white line-clamp-1 text-sm sm:text-base md:text-lg relative inline-block mx-auto"
                  animate={{ color: isHovered ? "var(--brand-gold)" : "" }}
                  transition={{ duration: 0.2 }}
                >
                  {business.business_name}

                  {/* Animated underline on hover */}
                  {isHovered && (
                    <motion.div
                      className="absolute -bottom-1 left-0 h-0.5 bg-[var(--brand-gold)]"
                      initial={{ width: 0 }}
                      animate={{ width: "100%" }}
                      transition={{ duration: 0.3 }}
                    />
                  )}
                </motion.h3>
              </div>
            </div>

            {/* Business details */}
            <div className="space-y-2 mb-3 flex-grow">
              {/* Location - improved design */}
              <div className="flex gap-1.5 sm:gap-2 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg p-2 sm:p-2.5 border border-neutral-100 dark:border-neutral-800">
                <MapPin className="h-4 w-4 text-neutral-500 dark:text-neutral-400 flex-shrink-0 mt-0.5" />
                <div className="flex-1 min-w-0">
                  {(() => {
                    const { addressLine, localityCity, statePin, hasAddress } = getAddressComponents();
                    return hasAddress ? (
                      <>
                        {addressLine && (
                          <p className="text-xs font-medium text-neutral-700 dark:text-neutral-300 line-clamp-1">
                            {addressLine}
                          </p>
                        )}
                        {localityCity && (
                          <p className="text-xs text-neutral-600 dark:text-neutral-400 line-clamp-1 mt-0.5">
                            {localityCity}
                          </p>
                        )}
                        {statePin && (
                          <p className="text-xs text-neutral-500 dark:text-neutral-500 line-clamp-1 mt-0.5">
                            {statePin}
                          </p>
                        )}
                      </>
                    ) : (
                      <p className="text-xs text-neutral-500 dark:text-neutral-500 italic">
                        Location not specified
                      </p>
                    );
                  })()}
                </div>
              </div>



              {/* Category badge - if available */}
              {/* Removed business_category as it's not in the BusinessCardData type */}
            </div>

            {/* Stats row */}
            <div className="flex items-center justify-between text-xs border-t border-neutral-100 dark:border-neutral-800 pt-2 mt-auto">
              <div className="flex items-center gap-1 text-rose-500 dark:text-rose-400">
                <Heart className="h-3.5 w-3.5" />
                <span>{formatIndianNumberShort(business.total_likes || 0)}</span>
              </div>

              <div className="flex items-center gap-1 text-blue-500 dark:text-blue-400">
                <UserPlus className="h-3.5 w-3.5" />
                <span>{formatIndianNumberShort(business.total_subscriptions || 0)}</span>
              </div>

              <div className="flex items-center gap-1 text-amber-500 dark:text-amber-400">
                <Star className="h-3.5 w-3.5" />
                <span>{(business.average_rating || 0).toFixed(1)}</span>
              </div>
            </div>
          </div>

          {/* Hover effect overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/5 to-blue-500/5 dark:from-[var(--brand-gold)]/10 dark:to-blue-500/10 pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ duration: 0.3 }}
          />
        </motion.div>
      </Link>
    </motion.div>
  );
}
