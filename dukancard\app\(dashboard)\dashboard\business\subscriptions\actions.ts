"use server";

import { createClient } from '@/utils/supabase/server';

// Define types for subscriber data (can be customer or business)
export interface SubscriberProfileData {
  id: string;
  name: string | null;
  slug: string | null;
  logo_url?: string | null;
  avatar_url?: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
  type: 'business' | 'customer';
}

export interface SubscriberWithProfile {
  id: string;
  profile: SubscriberProfileData | null;
}

export interface SubscribersResult {
  items: SubscriberWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

// Define types for businesses that this business follows
export interface BusinessFollowingData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

export interface BusinessFollowingWithProfile {
  id: string;
  business_profiles: BusinessFollowingData | null;
}

export interface BusinessFollowingResult {
  items: BusinessFollowingWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Fetch subscribers to a business (both customers and other businesses)
 * Note: Search functionality removed as per requirements
 */
export async function fetchBusinessSubscribers(
  businessId: string,
  page: number = 1,
  limit: number = 10
): Promise<SubscribersResult> {
  const supabase = await createClient();

  try {
    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get total count first
    const { count: totalCount, error: countError } = await supabase
      .from('subscriptions')
      .select('*', { count: 'exact', head: true })
      .eq('business_profile_id', businessId);

    if (countError) {
      throw new Error("Failed to fetch subscription count");
    }

    if (!totalCount || totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page
      };
    }

    // Get paginated subscriptions (database-level pagination)
    const { data: subscriptions, error: subsError } = await supabase
      .from('subscriptions')
      .select('id, user_id, created_at')
      .eq('business_profile_id', businessId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (subsError) {
      throw new Error("Failed to fetch subscriptions");
    }

    if (!subscriptions || subscriptions.length === 0) {
      return {
        items: [],
        totalCount: totalCount || 0,
        hasMore: false,
        currentPage: page
      };
    }

    // Get user IDs from the paginated subscriptions only
    const userIds = subscriptions.map((sub: { user_id: string }) => sub.user_id);

    // Fetch profiles for only the paginated user IDs (not all users)
    const [customerProfiles, businessProfiles] = await Promise.all([
      supabase
        .from('customer_profiles_public')
        .select('id, name, avatar_url')
        .in('id', userIds),
      supabase
        .from('business_profiles')
        .select('id, business_name, business_slug, logo_url, city, state, pincode, address_line')
        .in('id', userIds)
    ]);

    if (customerProfiles.error) {
      throw new Error("Failed to fetch customer profiles");
    }

    if (businessProfiles.error) {
      throw new Error("Failed to fetch business profiles");
    }

    // Create profile maps for efficient lookup
    const customerMap = new Map(customerProfiles.data?.map((p: { id: string; name: string | null; avatar_url: string | null }) => [p.id, p]) || []);
    const businessMap = new Map(businessProfiles.data?.map((p: { id: string; business_name: string | null; business_slug: string | null; logo_url: string | null; city: string | null; state: string | null; pincode: string | null; address_line: string | null }) => [p.id, p]) || []);

    // Transform subscriptions to include profile data
    const allSubscribers: SubscriberWithProfile[] = subscriptions
      .map((sub: { id: string; user_id: string; [key: string]: unknown }) => {
        const customerProfile = customerMap.get(sub.user_id);
        const businessProfile = businessMap.get(sub.user_id);

        if (customerProfile) {
          const profile = customerProfile as { id: string; name: string | null; avatar_url: string | null };
          return {
            id: sub.id,
            profile: {
              id: profile.id,
              name: profile.name,
              slug: null,
              avatar_url: profile.avatar_url,
              city: null,
              state: null,
              pincode: null,
              address_line: null,
              type: 'customer' as const,
            }
          };
        } else if (businessProfile) {
          const profile = businessProfile as {
            id: string;
            business_name: string | null;
            business_slug: string | null;
            logo_url: string | null;
            city: string | null;
            state: string | null;
            pincode: string | null;
            address_line: string | null;
          };
          return {
            id: sub.id,
            profile: {
              id: profile.id,
              name: profile.business_name,
              slug: profile.business_slug,
              logo_url: profile.logo_url,
              city: profile.city,
              state: profile.state,
              pincode: profile.pincode,
              address_line: profile.address_line,
              type: 'business' as const,
            }
          };
        }
        return null;
      })
      .filter((sub: SubscriberWithProfile | null): sub is SubscriberWithProfile => sub !== null);

    // Calculate hasMore based on database-level pagination
    const hasMore = totalCount > offset + limit;

    return {
      items: allSubscribers,
      totalCount: totalCount || 0,
      hasMore,
      currentPage: page
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Fetch businesses that this business follows
 */
export async function fetchBusinessFollowing(
  businessId: string,
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ""
): Promise<BusinessFollowingResult> {
  const supabase = await createClient();

  try {
    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build a complex query that joins subscriptions with business_profiles
    // This ensures proper database-level pagination with search filtering
    let query = supabase
      .from('subscriptions')
      .select(`
        id,
        business_profile_id,
        business_profiles!inner (
          id,
          business_name,
          business_slug,
          logo_url,
          city,
          state,
          pincode,
          address_line
        )
      `, { count: 'exact' })
      .eq('user_id', businessId);

    // Apply search filter if provided
    if (searchTerm && searchTerm.trim()) {
      query = query.ilike('business_profiles.business_name', `%${searchTerm.trim()}%`);
    }

    // Apply pagination at database level
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    const { data: subscriptionsWithProfiles, count: totalCount, error: queryError } = await query;

    if (queryError) {
      throw new Error("Failed to fetch business following data");
    }

    // Transform the data to match the expected interface
    let transformedSubscriptions: BusinessFollowingWithProfile[] = [];

    if (subscriptionsWithProfiles && subscriptionsWithProfiles.length > 0) {
      transformedSubscriptions = subscriptionsWithProfiles.map((sub: { id: string; business_profiles: unknown }) => ({
        id: sub.id,
        business_profiles: Array.isArray(sub.business_profiles) ? sub.business_profiles[0] : sub.business_profiles
      }));
    }

    // Calculate hasMore based on database-level pagination
    const hasMore = totalCount ? totalCount > offset + limit : false;

    return {
      items: transformedSubscriptions,
      totalCount: totalCount || 0,
      hasMore,
      currentPage: page
    };
  } catch (error) {
    throw error;
  }
}
