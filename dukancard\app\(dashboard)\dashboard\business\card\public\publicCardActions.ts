"use server";

import { createClient } from "@/utils/supabase/server";
import { BusinessCardData } from "../schema";
import { ProductServiceData } from "../../products/actions";
import { mapPublicCardData } from "../data/businessCardMapper";

/**
 * Fetches public card data by business slug
 * @param slug - The business slug to fetch data for
 * @returns Public card data with products/services or error
 */
export async function getPublicCardDataBySlug(slug: string): Promise<{
  data?: BusinessCardData & { products_services?: ProductServiceData[] };
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  const supabase = await createClient();

  // Fetch profile and related products, selecting only Phase 1 columns
  const { data, error } = await supabase
    .from("business_profiles")
    .select(
      `
      id, business_name, contact_email, has_active_subscription,
      trial_end_date, created_at, updated_at, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug,
      business_category, business_hours, delivery_info, established_year,
      products_services (
        id, name, description, base_price, is_available, image_url, created_at, updated_at
      )
    `
    )
    .eq("business_slug", slug)
    .eq("status", "online")
    .maybeSingle();

  if (error) {
    console.error("Public Fetch Error:", error);
    return { error: `Failed to fetch public profile: ${error.message}` };
  }

  if (!data) {
    return { error: "Profile not found or is not online." };
  }

  // Map data using the shared mapper
  const mappedData = mapPublicCardData(data);
  return { data: mappedData };
}
