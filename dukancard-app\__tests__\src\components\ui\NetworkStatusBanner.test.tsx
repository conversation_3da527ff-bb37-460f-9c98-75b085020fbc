import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import { NetworkStatusBanner } from '@/src/components/ui/NetworkStatusBanner';
import { useNetworkStatus } from '@/src/utils/networkStatus';
import { useTheme } from '@/src/hooks/useTheme';

// Mock hooks and timers
jest.mock('@/src/utils/networkStatus');
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      success: '#10B981',
      error: '#EF4444',
      warning: '#F59E0B',
      textSecondary: '#666666',
      shadow: '#000',
    },
    spacing: { md: 16, sm: 8, xs: 4 },
    typography: {
      fontSize: { sm: 14, xs: 12 },
      fontWeight: { semibold: '600', medium: '500' },
      lineHeight: { normal: 1.5 },
    },
    borderRadius: { sm: 6 },
  }),
}));

jest.useFakeTimers();

const mockUseNetworkStatus = useNetworkStatus as jest.Mock;

const mockOnRetry = jest.fn();

const renderComponent = (props: Partial<React.ComponentProps<typeof NetworkStatusBanner>> = {}) => {
  return render(<NetworkStatusBanner onRetry={mockOnRetry} {...props} />);
};

describe('<NetworkStatusBanner />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseNetworkStatus.mockReturnValue({ isConnected: true, isInternetReachable: true });
  });

  it('does not render when online by default', () => {
    const { queryByText } = renderComponent();
    expect(queryByText('No Internet Connection')).toBeNull();
    expect(queryByText('Back Online')).toBeNull();
  });

  it('renders when offline', () => {
    mockUseNetworkStatus.mockReturnValue({ isConnected: false, isInternetReachable: false });
    const { getByText } = renderComponent();
    expect(getByText('No Internet Connection')).toBeTruthy();
  });

  it('shows "Back Online" message when connection is restored', () => {
    // Start offline
    mockUseNetworkStatus.mockReturnValue({ isConnected: false, isInternetReachable: false });
    const { rerender, getByText } = renderComponent({ showWhenOnline: true });
    expect(getByText('No Internet Connection')).toBeTruthy();

    // Go online
    mockUseNetworkStatus.mockReturnValue({ isConnected: true, isInternetReachable: true });
    rerender(<NetworkStatusBanner onRetry={mockOnRetry} showWhenOnline={true} />);
    
    expect(getByText('Back Online')).toBeTruthy();
  });

  it('hides "Back Online" message after a delay', () => {
    mockUseNetworkStatus.mockReturnValue({ isConnected: false, isInternetReachable: false });
    const { rerender, getByText, queryByText } = renderComponent({ showWhenOnline: true });
    
    mockUseNetworkStatus.mockReturnValue({ isConnected: true, isInternetReachable: true });
    rerender(<NetworkStatusBanner onRetry={mockOnRetry} showWhenOnline={true} />);

    expect(getByText('Back Online')).toBeTruthy();

    act(() => {
      jest.runAllTimers();
    });

    expect(queryByText('Back Online')).toBeNull();
  });

  it('calls onRetry when the retry button is pressed', () => {
    mockUseNetworkStatus.mockReturnValue({ isConnected: false, isInternetReachable: false });
    const { getByText } = renderComponent();
    fireEvent.press(getByText('Retry'));
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it('renders a limited connection warning', () => {
    mockUseNetworkStatus.mockReturnValue({ isConnected: true, isInternetReachable: false });
    const { getByText } = renderComponent();
    expect(getByText('Limited Connection')).toBeTruthy();
  });

  it('matches snapshot when offline', () => {
    mockUseNetworkStatus.mockReturnValue({ isConnected: false, isInternetReachable: false });
    const { toJSON } = renderComponent();
    expect(toJSON()).toMatchSnapshot();
  });

  it('matches snapshot when back online', () => {
    mockUseNetworkStatus.mockReturnValue({ isConnected: false, isInternetReachable: false });
    const { rerender, toJSON } = renderComponent({ showWhenOnline: true });
    mockUseNetworkStatus.mockReturnValue({ isConnected: true, isInternetReachable: true });
    rerender(<NetworkStatusBanner onRetry={mockOnRetry} showWhenOnline={true} />);
    expect(toJSON()).toMatchSnapshot();
  });
});