import React from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>eft, ArrowRight, Loader2 } from "lucide-react";
import { PricingPlan } from "@/lib/PricingPlans";
import { ExistingBusinessProfileData } from "../types/onboarding";

interface NavigationButtonsProps {
  currentStep: number;
  isSubmitting: boolean;
  isCheckingSlug: boolean;
  slugAvailable: boolean | null;
  selectedPlan: PricingPlan | null;
  existingData: ExistingBusinessProfileData | null;
  onNextStep: () => void;
  onPreviousStep: () => void;
  onSubmitIntended: () => void;
}

export function NavigationButtons({
  currentStep,
  isSubmitting,
  isCheckingSlug,
  slugAvailable,
  selectedPlan,
  existingData,
  onNextStep,
  onPreviousStep,
  onSubmitIntended,
}: NavigationButtonsProps) {
  const router = useRouter();

  if (currentStep === 1) {
    return (
      <>
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push("/choose-role")}
          className="cursor-pointer border-border text-foreground hover:bg-muted dark:border-neutral-700 dark:hover:bg-neutral-800 dark:text-neutral-200 group transition-all text-xs sm:text-sm h-9 sm:h-10"
        >
          <ArrowLeft className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:-translate-x-1 transition-transform" />
          Back
        </Button>
        <Button
          type="button"
          onClick={onNextStep}
          disabled={isSubmitting}
          className="cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground dark:bg-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/90 dark:text-black group transition-all text-xs sm:text-sm h-9 sm:h-10"
        >
          Next
          <ArrowRight className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:translate-x-1 transition-transform" />
        </Button>
      </>
    );
  }

  return (
    <>
      <Button
        type="button"
        variant="outline"
        onClick={onPreviousStep}
        disabled={isSubmitting}
        className="cursor-pointer border-border text-foreground hover:bg-muted dark:border-neutral-700 dark:hover:bg-neutral-800 dark:text-neutral-200 group transition-all text-xs sm:text-sm h-9 sm:h-10"
      >
        <ArrowLeft className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:-translate-x-1 transition-transform" />
        Back
      </Button>
      {currentStep < 4 ? (
        <Button
          type="button"
          onClick={onNextStep}
          disabled={
            isSubmitting ||
            (currentStep === 2 && (isCheckingSlug || slugAvailable !== true))
          }
          className="cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground dark:bg-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/90 dark:text-black group transition-all text-xs sm:text-sm h-9 sm:h-10"
        >
          Next
          <ArrowRight className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:translate-x-1 transition-transform" />
        </Button>
      ) : (
        <Button
          type="submit"
          className="cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground dark:bg-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/90 dark:text-black relative overflow-hidden group transition-all text-xs sm:text-sm h-9 sm:h-10"
          disabled={isSubmitting || !selectedPlan}
          onClick={onSubmitIntended}
        >
          <span className="relative z-10 flex items-center">
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 animate-spin" />
                {existingData?.hasExistingSubscription
                  ? "Updating Profile..."
                  : selectedPlan?.id === "free"
                    ? "Creating Account..."
                    : "Starting Trial..."
                }
              </>
            ) : (
              <>
                {existingData?.hasExistingSubscription
                  ? "Complete Profile Setup"
                  : selectedPlan?.id === "free"
                    ? "Get Started for FREE"
                    : "Finish Setup & Start Trial"
                }{" "}
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 ml-1 sm:ml-2 group-hover:translate-x-1 transition-transform" />
              </>
            )}
          </span>
          <span className="absolute inset-0 bg-gradient-to-r from-primary/0 via-white/10 to-primary/0 dark:from-[var(--brand-gold)]/0 dark:via-white/20 dark:to-[var(--brand-gold)]/0 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-in-out" />
        </Button>
      )}
    </>
  );
}
