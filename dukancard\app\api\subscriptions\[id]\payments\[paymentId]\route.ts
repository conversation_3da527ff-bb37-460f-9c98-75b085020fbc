import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";

/**
 * GET handler for fetching a specific payment for a subscription
 *
 * This API endpoint fetches details of a specific payment for a subscription from Razorpay.
 * It verifies that the user has access to the subscription before making the API call.
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "payment_id": "pay_123456789",
 *     "amount": 499,
 *     "currency": "INR",
 *     "status": "captured",
 *     "method": "upi",
 *     "created_at": 1619090910,
 *     "subscription_id": "sub_123456789"
 *   }
 * }
 * ```
 */
export async function GET(
  _: NextRequest,
  { params }: { params: Promise<{ id: string; paymentId: string }> }
) {
  try {
    // Get the subscription ID and payment ID from the URL params
    const { id: subscriptionId, paymentId } = await params;

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the user's subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("id, business_profile_id, razorpay_subscription_id")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (subscriptionError || !subscription) {
      console.error("Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { error: "Could not fetch subscription details" },
        { status: 500 }
      );
    }

    // Verify that the subscription belongs to the user
    const { data: profile, error: profileError } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("id", subscription.business_profile_id)
      .single();

    if (profileError || !profile || profile.id !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized to access this subscription" },
        { status: 403 }
      );
    }

    // For now, return a placeholder response
    // In the future, implement the Razorpay API call to get payment details
    const result = {
      success: true,
      data: {
        payment_id: paymentId,
        subscription_id: subscriptionId,
        status: "Not implemented with Razorpay yet"
      }
    };

    // Since we're using a placeholder, we don't need to check for errors
    // This will be implemented properly when the Razorpay API is integrated

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in payment details API:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
