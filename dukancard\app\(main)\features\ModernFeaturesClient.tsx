"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import HeroFeatureSection from "./components/HeroFeatureSection";

import PlanComparisonSection from "./components/PlanComparisonSection";
import BusinessUseCasesSection from "./components/BusinessUseCasesSection";
import FeaturesCTASection from "./components/FeaturesCTASection";
import SectionDivider from "../components/landing/SectionDivider";
import { useRef } from "react";

export default function ModernFeaturesClient() {
  const [isLoaded, setIsLoaded] = useState(false);
  const pageRef = useRef(null);

  // Handle initial animation on page load
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div
      ref={pageRef}
      className="min-h-screen bg-white dark:bg-black text-black dark:text-white overflow-hidden"
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoaded ? 1 : 0 }}
        transition={{ duration: 0.5 }}
        className="w-full"
      >
        {/* Hero Section */}
        <HeroFeatureSection />

        {/* Section Divider */}
        <SectionDivider variant="gold" />

        {/* Plan Comparison */}
        <PlanComparisonSection />

        {/* Section Divider */}
        <SectionDivider variant="purple" />

        {/* Business Use Cases */}
        <BusinessUseCasesSection />

        {/* Section Divider */}
        <SectionDivider variant="gold" />

        {/* CTA Section */}
        <FeaturesCTASection />
      </motion.div>
    </div>
  );
}
