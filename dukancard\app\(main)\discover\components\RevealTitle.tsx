"use client";

import { useEffect, useState } from "react";
import { motion, stagger, useAnimate } from "framer-motion";

interface RevealTitleProps {
  title: string;
  className?: string;
}

export default function RevealTitle({ title, className = "" }: RevealTitleProps) {
  const [scope, animate] = useAnimate();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Only run animations when client-side and elements are available
  useEffect(() => {
    if (!isClient || !scope.current) return;

    // Check if elements exist before animating
    const wordElements = scope.current.querySelectorAll("span.word");
    if (wordElements.length > 0) {
      animate(
        wordElements,
        { opacity: 1, y: 0 },
        {
          duration: 0.5,
          delay: stagger(0.1),
          ease: "easeOut"
        }
      );
    }

    // Animate underlines if they exist
    const underlineElements = scope.current.querySelectorAll("span.underline");
    if (underlineElements.length > 0) {
      animate(
        underlineElements,
        { width: "100%" },
        {
          duration: 0.3,
          delay: stagger(0.1, { startDelay: 0.3 }),
          ease: "easeOut"
        }
      );
    }

    // Animate glows if they exist
    const glowElements = scope.current.querySelectorAll("span.glow");
    if (glowElements.length > 0) {
      animate(
        glowElements,
        { opacity: 0.5 },
        {
          duration: 0.5,
          delay: stagger(0.1, { startDelay: 0.5 }),
          ease: "easeOut"
        }
      );
    }

    // Animate search beam if it exists
    const searchBeamElement = scope.current.querySelector("span.search-beam");
    if (searchBeamElement) {
      animate(
        searchBeamElement,
        { left: "100%" },
        {
          duration: 1.5,
          delay: 0.8,
          ease: "easeInOut"
        }
      );
    }
  }, [animate, scope, isClient]);

  // Split the title into words
  const words = title.split(" ");

  return (
    <motion.h1
      ref={scope}
      className={`text-3xl md:text-4xl lg:text-5xl font-bold text-foreground relative ${className}`}
      initial={{ opacity: 0.8 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <span className="sr-only">{title}</span>
      {isClient && (
        <span className="inline-block relative">
          {words.map((word, i) => (
            <span
              key={`word-${i}`}
              className="word relative inline-block mr-[0.25em] opacity-0"
              style={{
                transform: "translateY(20px)",
              }}
            >
              {word}

              {/* Add a highlight effect to certain words */}
              {(word.toLowerCase().includes("business") ||
                word.toLowerCase().includes("products") ||
                word.toLowerCase().includes("find")) && (
                <span
                  className="underline absolute bottom-1 left-0 h-[3px] w-0 bg-[var(--brand-gold)]/50 rounded-full"
                />
              )}

              {/* Add a subtle glow effect to certain words */}
              {(word.toLowerCase().includes("business") ||
                word.toLowerCase().includes("products") ||
                word.toLowerCase().includes("find")) && (
                <span
                  className="glow absolute inset-0 opacity-0 rounded-lg blur-[8px] bg-[var(--brand-gold)]/10 -z-10"
                />
              )}
            </span>
          ))}

          {/* Add a search beam that moves across the title */}
          <span
            className="search-beam absolute top-0 left-0 h-full w-[30px] bg-gradient-to-r from-transparent via-[var(--brand-gold)]/20 to-transparent pointer-events-none"
          />
        </span>
      )}
    </motion.h1>
  );
}
