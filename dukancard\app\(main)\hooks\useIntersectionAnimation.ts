"use client";

import { useEffect, useRef, useState } from "react";
import { useAnimation } from "framer-motion";

interface UseIntersectionAnimationOptions {
  threshold?: number;
  once?: boolean;
}

export function useIntersectionAnimation({
  threshold = 1.0,
  once = false,
}: UseIntersectionAnimationOptions = {}) {
  const ref = useRef<HTMLDivElement>(null);
  const controls = useAnimation();
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isElementInView = entry.isIntersecting;
        setIsInView(isElementInView);
        
        if (isElementInView) {
          controls.start("visible");
          if (once) observer.unobserve(element);
        } else {
          controls.start("hidden");
        }
      },
      { threshold }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [controls, threshold, once]);

  return { ref, controls, isInView };
}
