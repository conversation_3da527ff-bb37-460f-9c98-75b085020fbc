import { NextRequest, NextResponse } from "next/server";
import { fetchWebhooks, FetchWebhooksParams } from "@/lib/razorpay/services/webhook";

/**
 * API route to list Razorpay webhooks
 *
 * This endpoint fetches all webhooks for a Razorpay account.
 *
 * Query parameters:
 * - account_id: The Razorpay account ID (required)
 * - from: Timestamp, in seconds, from when the webhooks are to be fetched (optional)
 * - to: Timestamp, in seconds, till when the webhooks are to be fetched (optional)
 * - count: Number of webhooks to be fetched (default: 10, max: 100) (optional)
 * - skip: Number of records to be skipped for pagination (optional)
 *
 * Response:
 * {
 *   "success": true,
 *   "data": [
 *     {
 *       "id": "HK890egfiItP3H",
 *       "created_at": **********,
 *       "updated_at": **********,
 *       "service": "beta-api-test",
 *       "owner_id": "H3kYHQ635sBwXG",
 *       "owner_type": "merchant",
 *       "context": [],
 *       "disabled_at": 0,
 *       "url": "https://en1mwkqo5ioct.x.pipedream.net",
 *       "alert_email": "<EMAIL>",
 *       "secret_exists": true,
 *       "entity": "webhook",
 *       "active": true,
 *       "events": [
 *         "payment.authorized",
 *         "payment.failed",
 *         "payment.captured",
 *         "payment.dispute.created",
 *         "refund.failed",
 *         "refund.created"
 *       ]
 *     }
 *   ]
 * }
 */
export async function GET(req: NextRequest) {
  try {
    // Get the query parameters
    const url = new URL(req.url);
    const accountId = url.searchParams.get("account_id");

    // Validate required parameters
    if (!accountId) {
      return NextResponse.json(
        { success: false, message: "Missing account_id parameter" },
        { status: 400 }
      );
    }

    // Extract optional query parameters
    const params: FetchWebhooksParams = {};

    const fromParam = url.searchParams.get("from");
    if (fromParam) {
      const fromTimestamp = parseInt(fromParam, 10);
      if (!isNaN(fromTimestamp)) {
        params.from = fromTimestamp;
      }
    }

    const toParam = url.searchParams.get("to");
    if (toParam) {
      const toTimestamp = parseInt(toParam, 10);
      if (!isNaN(toTimestamp)) {
        params.to = toTimestamp;
      }
    }

    const countParam = url.searchParams.get("count");
    if (countParam) {
      const count = parseInt(countParam, 10);
      if (!isNaN(count)) {
        params.count = count;
      }
    }

    const skipParam = url.searchParams.get("skip");
    if (skipParam) {
      const skip = parseInt(skipParam, 10);
      if (!isNaN(skip)) {
        params.skip = skip;
      }
    }

    // Fetch webhooks with parameters
    const result = await fetchWebhooks(accountId, Object.keys(params).length > 0 ? params : undefined);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return success response
    return NextResponse.json(
      { success: true, data: result.data },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK_LIST] Error listing webhooks:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Error listing webhooks",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
