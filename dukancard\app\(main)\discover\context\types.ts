import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { NearbyProduct } from "../actions/types";
import type { BusinessSortBy } from "@/lib/actions/businessProfiles/types";

// Define the product sort options for the discovery page
export type ProductSortOption =
  | "newest"
  | "price_low"
  | "price_high"
  | "name_asc"
  | "name_desc";

// Define the business sort options for the discovery page
export type BusinessSortOption =
  | "newest"
  | "name_asc"
  | "name_desc"
  | "most_liked"
  | "most_subscribed"
  | "highest_rated";

// Define the product filter options
export type ProductFilterOption = "all" | "physical" | "service";

// Define the view types
export type ViewType = "cards" | "products";

// Define the search result structure
export type DiscoverSearchResult = {
  location?: { city: string; state: string } | null;
  businesses?: BusinessCardData[];
  products?: NearbyProduct[];
  isAuthenticated: boolean;
  totalCount: number;
  hasMore: boolean;
  nextPage: number | null;
};

// Define the combined search form data
export type CombinedSearchFormData = {
  businessName?: string | null;
  pincode?: string | null;
  city?: string | null;
  locality?: string | null;
  category?: string | null;
};

// Define the context type
export type DiscoverContextType = {
  // State
  viewType: ViewType;
  sortBy: BusinessSortBy;
  selectedCategory: string | null;
  isSearching: boolean;
  isPending: boolean;
  isLoadingMore: boolean;
  searchError: string | null;
  productFilterBy: ProductFilterOption;
  productSortBy: ProductSortOption;
  searchResult: DiscoverSearchResult | null;
  businesses: BusinessCardData[];
  products: NearbyProduct[];
  currentPage: number;
  hasMore: boolean;
  totalCount: number;
  isAuthenticated: boolean;

  // Functions
  performSearch: (_data: CombinedSearchFormData) => void;
  handleViewChange: (_view: ViewType) => void;
  handleBusinessSortChange: (_sortOption: BusinessSortBy) => void;
  handleBusinessSearch: (_term: string) => void;
  handleProductSearch: (_term: string) => void;
  handleProductSortChange: (_sortOption: ProductSortOption) => void;
  handleProductFilterChange: (_filter: ProductFilterOption) => void;
  handleCategoryChange: (_category: string | null) => void;
  loadMore: () => Promise<void>;
};
