"use client";

import { useState, useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { toast } from "sonner";
import { onboardingPlans, PricingPlan } from "@/lib/PricingPlans";
import { getExistingBusinessProfileData } from "../actions";
import { OnboardingFormData, ExistingBusinessProfileData, User } from "../types/onboarding";

interface UseExistingDataOptions {
  user: User | null;
  form?: UseFormReturn<OnboardingFormData> | null;
  setSelectedPlan: (_plan: PricingPlan) => void;
}

export function useExistingData({ user, form, setSelectedPlan }: UseExistingDataOptions) {
  const [isLoadingExistingData, setIsLoadingExistingData] = useState(true);
  const [existingData, setExistingData] = useState<ExistingBusinessProfileData | null>(null);

  // Fetch existing business profile data when user is available
  useEffect(() => {
    const fetchExistingData = async () => {
      if (!user) return;

      setIsLoadingExistingData(true);
      try {
        const result = await getExistingBusinessProfileData();

        if (result.error) {
          console.error("Error fetching existing business profile data:", result.error);
          toast.error("Failed to load existing profile data");
        } else if (result.data) {
          setExistingData(result.data);

          // Pre-fill form fields with existing data (only if form is available)
          if (form) {
            Object.entries(result.data).forEach(([key, value]) => {
              // Skip non-form fields like hasExistingSubscription
              if (key === 'hasExistingSubscription') return;

              // Only pre-fill string values that are not empty
              if (typeof value === 'string' && value.trim() !== "") {
                form.setValue(key as keyof OnboardingFormData, value, { shouldValidate: false });
              }
            });

            // Handle existing subscription - pre-select plan if user already has one
            if (result.data?.hasExistingSubscription && result.data?.planId) {
              const existingPlan = onboardingPlans.find(_plan => _plan.id === result.data?.planId);
              if (existingPlan) {
                setSelectedPlan(existingPlan);
                form.setValue("planId", existingPlan.id, { shouldValidate: false });
              }
            }
          } else {
            // Handle existing subscription - pre-select plan even without form
            if (result.data?.hasExistingSubscription && result.data?.planId) {
              const existingPlan = onboardingPlans.find(_plan => _plan.id === result.data?.planId);
              if (existingPlan) {
                setSelectedPlan(existingPlan);
              }
            }
          }
        }
      } catch (error) {
        console.error("Unexpected error fetching existing data:", error);
        toast.error("Failed to load existing profile data");
      } finally {
        setIsLoadingExistingData(false);
      }
    };

    fetchExistingData();
  }, [user, form, setSelectedPlan]);

  return {
    isLoadingExistingData,
    existingData,
  };
}
