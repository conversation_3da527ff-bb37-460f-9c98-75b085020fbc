"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { ArrowR<PERSON>, <PERSON> } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function FeaturesCTASection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.3 });

  return (
    <section
      ref={sectionRef}
      className="py-16 px-4 md:px-6 lg:px-8 relative overflow-hidden"
    >
      <div className="max-w-7xl mx-auto">
        <motion.div
          className="bg-white/90 dark:bg-black/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 rounded-2xl p-8 md:p-12 relative overflow-hidden shadow-xl"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.7 }}
          whileHover={{ boxShadow: "0 0 30px rgba(var(--brand-gold-rgb), 0.2)" }}
        >
          {/* Animated background blobs */}
          <motion.div
            className="absolute -right-20 -top-20 w-64 h-64 bg-gradient-to-br from-[var(--brand-gold)]/20 to-amber-500/10 dark:from-[var(--brand-gold)]/30 dark:to-amber-500/20 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "mirror"
            }}
          />
          
          <motion.div
            className="absolute -left-20 -bottom-20 w-64 h-64 bg-gradient-to-tr from-blue-500/20 to-purple-500/10 dark:from-blue-500/30 dark:to-purple-500/20 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "mirror",
              delay: 1
            }}
          />

          {/* Content */}
          <div className="relative z-10 max-w-3xl mx-auto text-center">
            {/* Title */}
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-4"
              initial={{ opacity: 0, y: 10 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5 }}
            >
              Ready to{" "}
              <span className="text-[var(--brand-gold)] relative">
                Transform
                <motion.div
                  className="absolute -bottom-1 left-0 h-1 bg-[var(--brand-gold)]/30 rounded-full"
                  initial={{ width: 0 }}
                  animate={isInView ? { width: "100%" } : {}}
                  transition={{ duration: 0.7, delay: 0.3 }}
                />
              </span>{" "}
              Your Digital Presence?
            </motion.h2>

            <motion.p
              className="text-lg text-muted-foreground mb-8 relative"
              initial={{ opacity: 0, y: 10 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Join thousands of businesses that have elevated their digital
              identity with Dukancard. Start with our Basic plan today and
              upgrade as your business grows.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 10 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {/* Primary CTA Button */}
              <div className="relative group">
                {/* Button glow effect */}
                <motion.div
                  className="absolute -inset-1 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full blur-md"
                  animate={{ 
                    opacity: [0.5, 0.8, 0.5],
                    scale: [1, 1.05, 1]
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "mirror"
                  }}
                />

                <Link href="/login">
                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900 px-8 py-6 rounded-full font-medium text-lg relative overflow-hidden">
                      <span className="flex items-center">
                        Get Started Free
                        <motion.div
                          className="ml-2"
                          animate={{ x: [0, 5, 0] }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            repeatType: "loop",
                            ease: "easeInOut",
                          }}
                        >
                          <ArrowRight className="w-5 h-5" />
                        </motion.div>
                      </span>
                    </Button>
                  </motion.div>
                </Link>
              </div>

              {/* Secondary CTA Button */}
              <Link href="/pricing">
                <motion.div
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button variant="outline" className="px-8 py-6 rounded-full font-medium text-lg border-2">
                    View Pricing
                  </Button>
                </motion.div>
              </Link>
            </motion.div>
          </div>

          {/* Decorative elements */}
          <motion.div
            className="absolute top-6 left-6 text-[var(--brand-gold)]/20 dark:text-[var(--brand-gold)]/30"
            animate={{
              rotate: [0, 360],
              scale: [1, 1.1, 1],
            }}
            transition={{
              rotate: { duration: 20, repeat: Infinity, ease: "linear" },
              scale: { duration: 3, repeat: Infinity, repeatType: "reverse" }
            }}
          >
            <Star size={24} />
          </motion.div>

          <motion.div
            className="absolute bottom-6 right-6 text-blue-500/20 dark:text-blue-500/30"
            animate={{
              rotate: [0, -360],
              scale: [1, 1.1, 1],
            }}
            transition={{
              rotate: { duration: 20, repeat: Infinity, ease: "linear" },
              scale: { duration: 3, repeat: Infinity, repeatType: "reverse", delay: 1 }
            }}
          >
            <Star size={24} />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
