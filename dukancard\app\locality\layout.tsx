import React from "react";
import Header from "@/app/components/Header";
import Footer from "@/app/components/Footer";
import BottomNav from "@/app/components/BottomNav";
import MobileFooter from "@/app/components/MobileFooter";
import AdvertiseButton from "@/app/components/AdvertiseButton";
import FloatingAIButton from "@/app/components/FloatingAIButton";
import ScrollToTopButton from "@/components/ui/scroll-to-top-button";
import { ThemeProvider } from "next-themes";

export default function LocalityLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
      <div className="min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300">
        <Header />
        {/* Added pt-24 (padding-top) to create space between header and content */}
        <main className="flex-1 pt-24 pb-20 md:pb-0">{children}</main>
        <Footer />
        <MobileFooter />
        <BottomNav />
        <AdvertiseButton />
        <FloatingAIButton />
        <ScrollToTopButton excludePaths={["/dashboard"]} />
      </div>
    </ThemeProvider>
  );
}
