import {
  uploadFile,
  deleteFile,
  getFileUrl,
  getSignedUrl,
  listUserFiles,
  uploadProfileImage,
  deleteProfileImage,
  extractFilePathFromUrl,
} from '@/backend/supabase/services/storage/storageService';
import { supabase } from '@/lib/supabase';

// Mock dependencies
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
    },
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(),
        remove: jest.fn(),
        getPublicUrl: jest.fn(),
        createSignedUrl: jest.fn(),
        list: jest.fn(),
      })),
      listBuckets: jest.fn(),
    },
  },
}));

const mockUser = { id: 'user-123456', email: '<EMAIL>' };

describe('storageService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
  });

  describe('uploadFile', () => {
    it('should upload a file and return its public URL', async () => {
      const mockFile = new Blob(['test']);
      const from = supabase.storage.from('customers');
      (from.upload as jest.Mock).mockResolvedValue({ data: { path: 'path/to/file.txt', fullPath: 'customers/path/to/file.txt' }, error: null });
      (from.getPublicUrl as jest.Mock).mockReturnValue({ data: { publicUrl: 'http://public.url/file.txt' } });

      const result = await uploadFile(mockFile, 'customers', 'avatars', 'avatar.txt');

      expect(result.success).toBe(true);
      expect(result.data?.publicUrl).toBe('http://public.url/file.txt');
    });
  });

  describe('deleteFile', () => {
    it('should delete a file successfully', async () => {
        const from = supabase.storage.from('customers');
        (from.remove as jest.Mock).mockResolvedValue({ data: {}, error: null });
  
        const result = await deleteFile('customers', 'path/to/file.txt');
  
        expect(result.success).toBe(true);
      });
  });

  describe('getFileUrl', () => {
    it('should return a public URL for a file', async () => {
        const from = supabase.storage.from('customers');
        (from.getPublicUrl as jest.Mock).mockReturnValue({ data: { publicUrl: 'http://public.url/file.txt' } });
  
        const result = await getFileUrl('customers', 'path/to/file.txt');
  
        expect(result.success).toBe(true);
        expect(result.data?.publicUrl).toBe('http://public.url/file.txt');
      });
  });

  describe('extractFilePathFromUrl', () => {
    it('should extract the file path from a public URL', () => {
      const url = 'https://project.supabase.co/storage/v1/object/public/customers/users/us/er/user-123/avatar.png';
      const path = extractFilePathFromUrl(url);
      expect(path).toBe('users/us/er/user-123/avatar.png');
    });
  });

  // ... Add tests for other functions like getSignedUrl, listUserFiles, etc.
});