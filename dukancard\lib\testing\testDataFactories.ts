/**
 * Test data factories for creating consistent test data
 * Used across unit, integration, and e2e tests
 */

import { AuthError, User } from '@supabase/supabase-js';

// User data factories
export const UserFactory = {
  build: (overrides = {}) => ({
    id: 'user-' + Math.random().toString(36).substr(2, 9),
    email: '<EMAIL>',
    phone: '+911234567890',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    email_confirmed_at: new Date().toISOString(),
    phone_confirmed_at: new Date().toISOString(),
    last_sign_in_at: new Date().toISOString(),
    app_metadata: {},
    user_metadata: {},
    aud: 'authenticated',
    role: 'authenticated',
    ...overrides,
  }),

  buildWithEmail: (email: string, overrides = {}) => 
    UserFactory.build({ email, ...overrides }),

  buildWithPhone: (phone: string, overrides = {}) => 
    UserFactory.build({ phone, ...overrides }),

  buildUnconfirmed: (overrides = {}) => 
    UserFactory.build({ 
      email_confirmed_at: null, 
      phone_confirmed_at: null,
      ...overrides 
    }),
};

// Session data factories
export const SessionFactory = {
  build: (overrides = {}) => ({
    access_token: 'access-token-' + Math.random().toString(36).substr(2, 9),
    refresh_token: 'refresh-token-' + Math.random().toString(36).substr(2, 9),
    expires_in: 3600,
    expires_at: Math.floor(Date.now() / 1000) + 3600,
    token_type: 'bearer',
    user: UserFactory.build(),
    ...overrides,
  }),

  buildExpired: (overrides = {}) => 
    SessionFactory.build({
      expires_in: 0,
      expires_at: Math.floor(Date.now() / 1000) - 3600,
      ...overrides,
    }),

  buildWithUser: (user: User, overrides = {}) =>
    SessionFactory.build({ user, ...overrides }),
};

// Auth error factories
export const AuthErrorFactory = {
  build: (code: string, message?: string, overrides = {}): AuthError => ({
    name: 'AuthError',
    message: message || `Auth error: ${code}`,
    code,
    status: 400,
    __isAuthError: true,
    ...overrides,
  } as unknown as AuthError),

  // Common auth errors
  invalidCredentials: () => 
    AuthErrorFactory.build('invalid_credentials', 'Invalid email or password'),

  userNotFound: () => 
    AuthErrorFactory.build('user_not_found', 'User account not found'),

  otpExpired: () => 
    AuthErrorFactory.build('otp_expired', 'OTP has expired. Please request a new one'),

  emailRateLimit: () => 
    AuthErrorFactory.build('over_email_send_rate_limit', 'Email rate limit exceeded'),

  requestRateLimit: () => 
    AuthErrorFactory.build('over_request_rate_limit', 'Too many requests'),

  sessionExpired: () => 
    AuthErrorFactory.build('session_expired', 'Your session has expired'),

  weakPassword: () => 
    AuthErrorFactory.build('weak_password', 'Password does not meet security requirements'),

  emailExists: () => 
    AuthErrorFactory.build('email_exists', 'An account with this email already exists'),

  phoneExists: () => 
    AuthErrorFactory.build('phone_exists', 'An account with this phone number already exists'),

  invalidEmail: () => 
    AuthErrorFactory.build('email_address_invalid', 'Please enter a valid email address'),

  signupDisabled: () => 
    AuthErrorFactory.build('signup_disabled', 'New account registration is currently disabled'),
};

// Form data factories
export const FormDataFactory = {
  validEmailForm: () => ({
    email: '<EMAIL>',
  }),

  invalidEmailForm: () => ({
    email: 'invalid-email',
  }),

  validOTPForm: () => ({
    otp: '123456',
  }),

  invalidOTPForm: () => ({
    otp: '12345', // Too short
  }),

  validMobilePasswordForm: () => ({
    mobile: '**********',
    password: 'Test123!@#',
  }),

  invalidMobilePasswordForm: () => ({
    mobile: '123', // Too short
    password: '123', // Too weak
  }),

  // Edge cases
  emptyEmailForm: () => ({
    email: '',
  }),

  emptyOTPForm: () => ({
    otp: '',
  }),

  emptyMobilePasswordForm: () => ({
    mobile: '',
    password: '',
  }),

  longEmailForm: () => ({
    email: 'a'.repeat(100) + '@example.com',
  }),

  specialCharacterEmailForm: () => ({
    email: '<EMAIL>',
  }),

  borderlineMobileForm: () => ({
    mobile: '**********', // Exactly 10 digits, starts with 1
    password: 'Aa1!', // Minimum complexity
  }),
};

// API response factories
export const APIResponseFactory = {
  successfulOTPSend: () => ({
    data: {},
    error: null,
  }),

  failedOTPSend: (error: AuthError) => ({
    data: null,
    error,
  }),

  successfulOTPVerify: (user = UserFactory.build()) => ({
    data: {
      user,
      session: SessionFactory.buildWithUser(user),
    },
    error: null,
  }),

  failedOTPVerify: (error: AuthError) => ({
    data: null,
    error,
  }),

  successfulPasswordLogin: (user = UserFactory.build()) => ({
    data: {
      user,
      session: SessionFactory.buildWithUser(user),
    },
    error: null,
  }),

  failedPasswordLogin: (error: AuthError) => ({
    data: null,
    error,
  }),
};

// Test scenario builders
export const ScenarioFactory = {
  // Email OTP scenarios
  emailOTPSuccess: () => ({
    formData: FormDataFactory.validEmailForm(),
    otpData: FormDataFactory.validOTPForm(),
    user: UserFactory.build(),
    sendResponse: APIResponseFactory.successfulOTPSend(),
    verifyResponse: APIResponseFactory.successfulOTPVerify(),
  }),

  emailOTPSendFailure: (error = AuthErrorFactory.emailRateLimit()) => ({
    formData: FormDataFactory.validEmailForm(),
    sendResponse: APIResponseFactory.failedOTPSend(error),
  }),

  emailOTPVerifyFailure: (error = AuthErrorFactory.otpExpired()) => ({
    formData: FormDataFactory.validEmailForm(),
    otpData: FormDataFactory.validOTPForm(),
    sendResponse: APIResponseFactory.successfulOTPSend(),
    verifyResponse: APIResponseFactory.failedOTPVerify(error),
  }),

  // Mobile password scenarios
  mobilePasswordSuccess: () => ({
    formData: FormDataFactory.validMobilePasswordForm(),
    user: UserFactory.build(),
    loginResponse: APIResponseFactory.successfulPasswordLogin(),
  }),

  mobilePasswordFailure: (error = AuthErrorFactory.invalidCredentials()) => ({
    formData: FormDataFactory.validMobilePasswordForm(),
    loginResponse: APIResponseFactory.failedPasswordLogin(error),
  }),

  // Validation scenarios
  emailValidationFailure: () => ({
    formData: FormDataFactory.invalidEmailForm(),
  }),

  otpValidationFailure: () => ({
    otpData: FormDataFactory.invalidOTPForm(),
  }),

  mobilePasswordValidationFailure: () => ({
    formData: FormDataFactory.invalidMobilePasswordForm(),
  }),
};

// Test state factories
export const StateFactory = {
  initialLoginState: () => ({
    authMethod: 'email-otp' as const,
    step: 'email' as const,
    email: '',
    countdown: 0,
    isPending: false,
  }),

  emailStepState: (email = '<EMAIL>') => ({
    authMethod: 'email-otp' as const,
    step: 'email' as const,
    email,
    countdown: 0,
    isPending: false,
  }),

  otpStepState: (email = '<EMAIL>', countdown = 60) => ({
    authMethod: 'email-otp' as const,
    step: 'otp' as const,
    email,
    countdown,
    isPending: false,
  }),

  mobilePasswordState: () => ({
    authMethod: 'mobile-password' as const,
    step: 'email' as const,
    email: '',
    countdown: 0,
    isPending: false,
  }),

  loadingState: (authMethod = 'email-otp' as const, step = 'email' as const) => ({
    authMethod,
    step,
    email: '<EMAIL>',
    countdown: 0,
    isPending: true,
  }),
};

// Mock function factories
export const MockFactory = {
  createSupabaseClient: () => ({
    auth: {
      signInWithOtp: jest.fn(),
      verifyOtp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      getUser: jest.fn(),
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(() => ({
        data: { subscription: { unsubscribe: jest.fn() } }
      })),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn(),
      single: jest.fn(),
    })),
  }),

  createToastMock: () => ({
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
    loading: jest.fn(),
    dismiss: jest.fn(),
  }),

  createRouterMock: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),

  createSearchParamsMock: (params: Record<string, string> = {}) => ({
    get: jest.fn((key: string) => params[key] || null),
    has: jest.fn((key) => key in params),
    getAll: jest.fn(),
    keys: jest.fn(),
    values: jest.fn(),
    entries: jest.fn(),
    forEach: jest.fn(),
    toString: jest.fn(),
  }),
};
