"use client";

import { UseFormReturn } from "react-hook-form";
import { BusinessCardData } from "../../schema";
import { MapPin, Phone, Loader2, Globe, Building2, Info } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ContactLocationSectionProps {
  form: UseFormReturn<BusinessCardData>;
  isPincodeLoading: boolean;
  availableLocalities: string[];
  onPincodeChange: (_pincode: string) => void;
}

export default function ContactLocationSection({
  form,
  isPincodeLoading,
  availableLocalities,
  onPincodeChange,
}: ContactLocationSectionProps) {
  return (
    <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
          <MapPin className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            Contact & Location
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            Add your contact information and business location details
          </p>
        </div>
      </div>

      <div className="flex flex-col gap-4 sm:gap-6">
        {/* Phone and Address Line Fields - 2 columns on tablet and desktop */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          {/* Phone Field */}
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem className="space-y-1 sm:space-y-2">
                <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                  <Phone className="h-3.5 w-3.5 text-primary" />
                  Primary Phone
                  <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="9876543210"
                      type="tel"
                      pattern="[0-9]*"
                      inputMode="numeric"
                      {...field}
                      onChange={(e) => {
                        // Remove any +91 prefix if user enters it
                        let value = e.target.value.replace(/^\+91/, "");
                        // Only allow numeric input and limit to 10 digits
                        value = value.replace(/\D/g, "");
                        if (value.length <= 10) {
                          field.onChange(value);
                        }
                      }}
                      onKeyDown={(e) => {
                        // Prevent non-numeric input
                        const isNumeric = /^[0-9]$/.test(e.key);
                        const isControl = [
                          "Backspace",
                          "Delete",
                          "ArrowLeft",
                          "ArrowRight",
                          "Tab",
                        ].includes(e.key);
                        if (!isNumeric && !isControl) {
                          e.preventDefault();
                        }
                      }}
                      className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                    />
                  </div>
                </FormControl>
                <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                  Your primary contact number for customers
                </FormDescription>
                <FormMessage className="text-xs text-red-500" />
              </FormItem>
            )}
          />

          {/* Address Line Field */}
          <FormField
            control={form.control}
            name="address_line"
            render={({ field }) => (
              <FormItem className="space-y-1 sm:space-y-2">
                <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                  <Building2 className="h-3.5 w-3.5 text-primary" />
                  Address Line
                  <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="e.g., Shop No. 12, Main Road"
                      {...field}
                      className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                      maxLength={100}
                    />
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500">
                      {field.value?.length || 0}/100
                    </div>
                  </div>
                </FormControl>
                <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                  Your street address or landmark
                </FormDescription>
                <FormMessage className="text-xs text-red-500" />
              </FormItem>
            )}
          />
        </div>

        {/* Pincode Field */}
        <FormField
          control={form.control}
          name="pincode"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Globe className="h-3.5 w-3.5 text-primary" />
                Pincode
                <span className="text-red-500">*</span>
              </FormLabel>
              <div className="flex items-center gap-2 sm:gap-3">
                <FormControl className="flex-1">
                  <div className="relative">
                    <Input
                      placeholder="e.g., 751001"
                      {...field}
                      value={field.value ?? ""}
                      className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                      maxLength={6}
                      type="number"
                      onChange={(e) => {
                        field.onChange(e);
                        if (e.target.value.length === 6) {
                          onPincodeChange(e.target.value);
                        }
                      }}
                      onInput={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.value = target.value.replace(/[^0-9]/g, "");
                      }}
                    />
                  </div>
                </FormControl>
                {isPincodeLoading && (
                  <div className="p-1.5 rounded-md bg-neutral-100 dark:bg-neutral-800">
                    <Loader2 className="h-4 w-4 sm:h-5 sm:w-5 animate-spin text-primary" />
                  </div>
                )}
              </div>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                6-digit pincode to auto-fill city and state
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />

        {/* City and State fields in a responsive grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          {/* City Field */}
          <FormField
            control={form.control}
            name="city"
            render={({ field }) => (
              <FormItem className="space-y-1 sm:space-y-2">
                <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                  <MapPin className="h-3.5 w-3.5 text-primary/50" />
                  City
                  <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="Auto-filled from Pincode"
                      {...field}
                      value={field.value ?? ""}
                      className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-100/80 dark:bg-neutral-800/40 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 cursor-not-allowed"
                      readOnly
                    />
                  </div>
                </FormControl>
                <FormMessage className="text-xs text-red-500" />
              </FormItem>
            )}
          />

          {/* State Field */}
          <FormField
            control={form.control}
            name="state"
            render={({ field }) => (
              <FormItem className="space-y-1 sm:space-y-2">
                <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                  <MapPin className="h-3.5 w-3.5 text-primary/50" />
                  State
                  <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="Auto-filled from Pincode"
                      {...field}
                      value={field.value ?? ""}
                      className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-100/80 dark:bg-neutral-800/40 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 cursor-not-allowed"
                      readOnly
                    />
                  </div>
                </FormControl>
                <FormMessage className="text-xs text-red-500" />
              </FormItem>
            )}
          />
        </div>

        {/* Locality Field */}
        <FormField
          control={form.control}
          name="locality"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <MapPin className="h-3.5 w-3.5 text-primary" />
                Locality / Area
                <span className="text-red-500">*</span>
              </FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value ?? ""}
                disabled={availableLocalities.length === 0}
              >
                <FormControl>
                  <SelectTrigger
                    className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                    disabled={availableLocalities.length === 0}
                  >
                    <SelectValue
                      placeholder={
                        availableLocalities.length === 0
                          ? "Enter Pincode first"
                          : "Select your locality"
                      }
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent className="border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg">
                  {availableLocalities.map((loc) => (
                    <SelectItem
                      key={loc}
                      value={loc}
                      className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20"
                    >
                      {loc}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                Select the specific area within the pincode
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />
      </div>

      {/* Tip Section */}
      <div className="mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm">
        <div className="flex items-start gap-2 sm:gap-3">
          <div className="p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm">
            <Info className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </div>
          <div>
            <p className="text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300">
              Location Tip
            </p>
            <p className="text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed">
              Adding accurate location details helps customers find you easily.
              Pincode auto-fills city and state for consistency. Add your Google
              Maps URL to show a &quot;Get Directions&quot; button on your
              public card.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
