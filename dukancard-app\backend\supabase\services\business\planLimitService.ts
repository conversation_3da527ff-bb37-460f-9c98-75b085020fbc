import { supabase } from "@/src/config/supabase";
import { PLANS, PlanType, getProductLimit } from "@/lib/config/plans";
import { SUBSCRIPTION_STATUS } from "@/lib/constants/subscription";

export interface PlanLimitInfo {
  planId: PlanType;
  planLimit: number | null; // null means unlimited
  currentAvailableCount: number;
  totalCount: number;
  canAddMore: boolean;
  isAtLimit: boolean;
}

/**
 * Get user's plan limits and current product counts
 * Similar to Next.js implementation
 */
export async function getUserPlanLimits(): Promise<{
  success: boolean;
  error?: string;
  data?: PlanLimitInfo;
}> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    // Get user's subscription info (following established patterns)
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("plan_id, subscription_status")
      .eq("business_profile_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (subscriptionError) {
      console.error("Error fetching subscription:", subscriptionError);
      // Continue with free plan as fallback
    }

    // Determine plan ID based on subscription status (following established patterns)
    let planId: PlanType = "free"; // Default to free plan

    if (subscriptionData) {
      const status = subscriptionData.subscription_status;
      const subPlanId = subscriptionData.plan_id;

      // Only use subscription plan if it's in an active state
      if (status === SUBSCRIPTION_STATUS.ACTIVE ||
          status === SUBSCRIPTION_STATUS.AUTHENTICATED ||
          status === SUBSCRIPTION_STATUS.TRIAL) {
        planId = (subPlanId as PlanType) || "free";
      }
    }
    
    // Get plan details
    const plan = PLANS.find(p => p.id === planId);
    if (!plan) {
      return { success: false, error: "Invalid plan configuration." };
    }

    // Use the existing getProductLimit function for consistency
    const productLimitNumber = getProductLimit(planId);
    let planLimit: number | null = null;

    // Convert to our format (null for unlimited)
    if (productLimitNumber >= 999999) {
      planLimit = null; // unlimited
    } else {
      planLimit = productLimitNumber;
    }

    // Override for specific plans if needed
    if (planId === "enterprise") {
      planLimit = null; // unlimited for enterprise
    }

    // Count current available products
    const { count: availableCount, error: availableCountError } = await supabase
      .from("products_services")
      .select("id", { count: "exact", head: true })
      .eq("business_id", user.id)
      .eq("is_available", true);

    if (availableCountError) {
      return { success: false, error: "Failed to count available products." };
    }

    // Count total products
    const { count: totalCount, error: totalCountError } = await supabase
      .from("products_services")
      .select("id", { count: "exact", head: true })
      .eq("business_id", user.id);

    if (totalCountError) {
      return { success: false, error: "Failed to count total products." };
    }

    const currentAvailableCount = availableCount || 0;
    const currentTotalCount = totalCount || 0;
    
    const canAddMore = planLimit === null || currentAvailableCount < planLimit;
    const isAtLimit = planLimit !== null && currentAvailableCount >= planLimit;

    return {
      success: true,
      data: {
        planId,
        planLimit,
        currentAvailableCount,
        totalCount: currentTotalCount,
        canAddMore,
        isAtLimit,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get plan limits",
    };
  }
}

/**
 * Check if user can make a product available
 * Used before toggling availability
 */
export async function canMakeProductAvailable(
  productId?: string
): Promise<{
  success: boolean;
  error?: string;
  canMakeAvailable?: boolean;
}> {
  try {
    const planLimitsResult = await getUserPlanLimits();
    if (!planLimitsResult.success || !planLimitsResult.data) {
      return { success: false, error: planLimitsResult.error };
    }

    const { planLimit, currentAvailableCount } = planLimitsResult.data;

    // If unlimited plan, always allow
    if (planLimit === null) {
      return { success: true, canMakeAvailable: true };
    }

    // If editing existing product, check if it's currently available
    if (productId) {
      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return { success: false, error: "User not authenticated." };
      }

      const { data: product, error: productError } = await supabase
        .from("products_services")
        .select("is_available")
        .eq("id", productId)
        .eq("business_id", user.id)
        .single();

      if (productError) {
        return { success: false, error: "Product not found." };
      }

      // If product is already available, allow toggle
      if (product.is_available) {
        return { success: true, canMakeAvailable: true };
      }
    }

    // Check if we're at the limit
    const canMakeAvailable = currentAvailableCount < planLimit;
    
    return {
      success: true,
      canMakeAvailable,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to check availability",
    };
  }
}

/**
 * Get plan display name for UI
 */
export function getPlanDisplayName(planId: PlanType): string {
  const plan = PLANS.find(p => p.id === planId);
  return plan?.name || "Free";
}

/**
 * Get plan limit display text
 */
export function getPlanLimitDisplayText(planLimit: number | null): string {
  if (planLimit === null) {
    return "Unlimited";
  }
  return planLimit.toString();
}
