"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  LayoutDashboard,
  CreditCard,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import DashboardOverviewClient from "./DashboardOverviewClient";
import AnimatedSubscriptionStatus from "./AnimatedSubscriptionStatus";
import EnhancedQuickActions from "./EnhancedQuickActions";
import RecentActivities from "./RecentActivities";
import BusinessStatusAlert from "./BusinessStatusAlert";
import { PricingPlan } from "@/lib/PricingPlans";
import { getUnreadActivitiesCount } from "@/lib/actions/activities";

interface BusinessDashboardClientProps {
  initialProfile: {
    business_name: string;
    business_slug: string;
    plan_id: string | null;
    plan_cycle: string | null;
    has_active_subscription: boolean | null;
    trial_end_date: string | null;
    total_likes: number;
    total_subscriptions: number;
    average_rating: number;
    logo_url: string | null;
    title: string | null;
    status: string; // "online" or "offline"
  };
  userId: string;
  subscriptionStatus: "active" | "trial" | "inactive";
  planDetails: PricingPlan | undefined;
  subscription?: {
    subscription_status: string | null;
    plan_id: string | null;
    plan_cycle: string | null;
  } | null;
}

export default function BusinessDashboardClient({
  initialProfile,
  userId,
  subscriptionStatus,
  planDetails,
  subscription,
}: BusinessDashboardClientProps) {
  const [unreadActivitiesCount, setUnreadActivitiesCount] = useState(0);

  /**
   * Fetch Unread Activities Count
   *
   * This effect fetches the initial unread activities count and sets up an interval
   * to refresh it every minute. This ensures the dashboard shows accurate counts
   * even if the realtime subscription misses any events.
   *
   * Note: For realtime updates of the activities count, you need to enable realtime
   * for the business_activities table in Supabase:
   * 1. Go to Supabase Dashboard > Database > Replication
   * 2. Find the "business_activities" table in the list
   * 3. Enable realtime by toggling it on
   */
  useEffect(() => {
    const fetchUnreadCount = async () => {
      const { count } = await getUnreadActivitiesCount(userId);
      setUnreadActivitiesCount(count);
    };

    fetchUnreadCount();

    // Set up interval to refresh count every minute
    const intervalId = setInterval(fetchUnreadCount, 60000);

    return () => clearInterval(intervalId);
  }, [userId]);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Welcome Section - Full Width */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
        <div className="p-3 rounded-xl bg-muted hidden sm:block">
          <LayoutDashboard className="w-6 h-6 text-foreground" />
        </div>
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-foreground">
            Welcome, {initialProfile.business_name || 'Business Owner'}
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your business profile and track performance
          </p>
        </div>
        <Button
          asChild
          variant="outline"
          size="sm"
        >
          <Link href="/dashboard/business/card" className="flex items-center">
            <CreditCard className="mr-2 h-4 w-4" />
            Manage Card
          </Link>
        </Button>
      </div>

      {/* Business Status Alert - Show only when status is offline */}
      {initialProfile.status === "offline" && (
        <motion.div variants={itemVariants}>
          <BusinessStatusAlert />
        </motion.div>
      )}

      {/* Dashboard Overview Client - Full Width */}
      <motion.div variants={itemVariants}>
        <DashboardOverviewClient
          initialProfile={initialProfile}
          userId={userId}
          userPlan={initialProfile.plan_id}
        />
      </motion.div>

      {/* Subscription Status and Quick Actions - Full Width */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Animated Subscription Status */}
          <AnimatedSubscriptionStatus
            subscriptionStatus={subscriptionStatus}
            planDetails={planDetails}
            trialEndDate={initialProfile.trial_end_date}
            planCycle={initialProfile.plan_cycle}
            subscription={subscription}
          />

          {/* Enhanced Quick Actions */}
          <EnhancedQuickActions userPlan={initialProfile.plan_id} />
        </div>
      </motion.div>

      {/* Recent Activities - Full Width */}
      <motion.div variants={itemVariants}>
        <RecentActivities
          businessProfileId={userId}
          unreadCount={unreadActivitiesCount}
        />
      </motion.div>
    </motion.div>
  );
}
