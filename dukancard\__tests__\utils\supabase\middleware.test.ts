import { NextRequest } from "next/server";
import { updateSession } from "../../../utils/supabase/middleware";
import { createServerClient } from "@supabase/ssr";

// Mock the Supabase client
jest.mock("@supabase/ssr", () => ({
  createServerClient: jest.fn(),
}));

const createMockRequest = (pathname: string, searchParams: URLSearchParams = new URLSearchParams()) => {
  const url = new URL(`http://localhost:3000${pathname}`);
  url.search = searchParams.toString();
  const headers = new Headers();
  headers.append('cookie', '');
  const request = new Request(url.toString(), { headers });
  return new NextRequest(request);
};

describe("updateSession middleware", () => {
  let mockSupabaseClient: any;

  beforeEach(() => {
    mockSupabaseClient = {
      auth: {
        getUser: jest.fn(),
      },
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn(),
    };
    (createServerClient as jest.Mock).mockReturnValue(mockSupabaseClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Test case 1: Unauthenticated user accessing a protected route
  test("should redirect unauthenticated user from protected route to /login", async () => {
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user: null } });
    const request = createMockRequest("/dashboard/business");
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("Location")).toBe("http://localhost:3000/login?next=%2Fdashboard%2Fbusiness");
  });

  // Test case 2: Authenticated user accessing a protected route
  test("should allow authenticated user to access protected route", async () => {
    const user = { id: "user-123" };
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user } });
    mockSupabaseClient.from("customer_profiles").maybeSingle.mockResolvedValue({ data: { id: user.id }, error: null });
    mockSupabaseClient.from("business_profiles").maybeSingle.mockResolvedValue({ data: null, error: null });

    const request = createMockRequest("/dashboard/customer");
    const response = await updateSession(request);

    // Expect to continue to the next middleware
    expect(response.status).toBe(200);
    // A real NextResponse.next() doesn't have a simple body to check,
    // but we can check that it's not a redirect.
    expect(response.headers.get("Location")).toBeNull();
  });

  // Test case 3: New user without a profile should be redirected to /choose-role
  test("should redirect new user to /choose-role", async () => {
    const user = { id: "new-user" };
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user } });
    mockSupabaseClient.from("customer_profiles").maybeSingle.mockResolvedValue({ data: null, error: null });
    mockSupabaseClient.from("business_profiles").maybeSingle.mockResolvedValue({ data: null, error: null });

    const request = createMockRequest("/dashboard"); // Any protected route
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("Location")).toBe("http://localhost:3000/choose-role");
  });

  // Test case 4: Business user with no slug should be redirected to /onboarding
  test("should redirect business user without slug to /onboarding", async () => {
    const user = { id: "business-user-no-slug" };
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user } });
    mockSupabaseClient.from("customer_profiles").maybeSingle.mockResolvedValue({ data: null, error: null });
    mockSupabaseClient.from("business_profiles").maybeSingle.mockResolvedValue({ data: { id: user.id, business_slug: null }, error: null });

    const request = createMockRequest("/dashboard/business");
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("Location")).toBe("http://localhost:3000/onboarding");
  });

  // Test case 5: Logged-in user should be redirected from /login to their dashboard
  test("should redirect logged-in user from /login to dashboard", async () => {
    const user = { id: "customer-123" };
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user } });
    mockSupabaseClient.from("customer_profiles").maybeSingle.mockResolvedValue({ data: { id: user.id }, error: null });
    mockSupabaseClient.from("business_profiles").maybeSingle.mockResolvedValue({ data: null, error: null });

    const request = createMockRequest("/login");
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("Location")).toBe("http://localhost:3000/dashboard/customer");
  });

  // Test case 6: User who just logged out should be able to access /login
  test("should allow user who just logged out to access /login", async () => {
    const user = { id: "customer-123" };
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user } });
    mockSupabaseClient.from("customer_profiles").maybeSingle.mockResolvedValue({ data: { id: user.id }, error: null });
    mockSupabaseClient.from("business_profiles").maybeSingle.mockResolvedValue({ data: null, error: null });

    const request = createMockRequest("/login", new URLSearchParams({ logged_out: "true" }));
    const response = await updateSession(request);

    expect(response.status).toBe(200);
    expect(response.headers.get("Location")).toBeNull();
  });

  // Test case 7: Customer trying to access business dashboard should be redirected
  test("should redirect customer from business dashboard", async () => {
    const user = { id: "customer-123" };
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user } });
    mockSupabaseClient.from("customer_profiles").maybeSingle.mockResolvedValue({ data: { id: user.id }, error: null });
    mockSupabaseClient.from("business_profiles").maybeSingle.mockResolvedValue({ data: null, error: null });

    const request = createMockRequest("/dashboard/business");
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("Location")).toBe("http://localhost:3000/dashboard/customer");
  });

  // Test case 8: Business user trying to access customer dashboard should be redirected
  test("should redirect business user from customer dashboard", async () => {
    const user = { id: "business-123" };
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user } });
    mockSupabaseClient.from("customer_profiles").maybeSingle.mockResolvedValue({ data: null, error: null });
    mockSupabaseClient.from("business_profiles").maybeSingle.mockResolvedValue({ data: { id: user.id, business_slug: "my-biz" }, error: null });
    mockSupabaseClient.from("payment_subscriptions").maybeSingle.mockResolvedValue({ data: { plan_id: "premium" }, error: null });


    const request = createMockRequest("/dashboard/customer");
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("Location")).toBe("http://localhost:3000/dashboard/business");
  });

  // Test case 9: Free tier business user trying to access analytics should be redirected
  test("should redirect free tier business user from analytics", async () => {
    const user = { id: "free-biz-user" };
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user } });
    mockSupabaseClient.from("customer_profiles").maybeSingle.mockResolvedValue({ data: null, error: null });
    mockSupabaseClient.from("business_profiles").maybeSingle.mockResolvedValue({ data: { id: user.id, business_slug: "free-biz" }, error: null });
    mockSupabaseClient.from("payment_subscriptions").maybeSingle.mockResolvedValue({ data: { plan_id: "free" }, error: null });

    const request = createMockRequest("/dashboard/business/analytics");
    const response = await updateSession(request);

    expect(response.status).toBe(307);
    expect(response.headers.get("Location")).toBe("http://localhost:3000/dashboard/business/plan?upgrade=analytics");
  });
});