"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Form } from "@/components/ui/form";
import { PricingPlan } from "@/lib/PricingPlans";
import { useUserData } from "./hooks/useUserData";
import { useExistingData } from "./hooks/useExistingData";
import { useOnboardingForm } from "./hooks/useOnboardingForm";
import { useSlugAvailability } from "./hooks/useSlugAvailability";
import { usePincodeDetails } from "./hooks/usePincodeDetails";
import { StepProgress } from "./components/StepProgress";
import { NavigationButtons } from "./components/NavigationButtons";
import { LoadingOverlay } from "./components/LoadingOverlay";
import { BusinessDetailsStep } from "./components/steps/BusinessDetailsStep";
import { CardInformationStep } from "./components/steps/CardInformationStep";
import { AddressStep } from "./components/steps/AddressStep";
import { PlanSelectionStep } from "./components/steps/PlanSelectionStep";
import { OnboardingClientProps } from "./types/onboarding";

export default function OnboardingClient({ redirectSlug, message }: OnboardingClientProps = {}) {
  // State for plan selection and UI
  const [showPlans, setShowPlans] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);

  // Initialize hooks
  const { user } = useUserData();

  // Initialize existing data first
  const { isLoadingExistingData, existingData } = useExistingData({ user, form: null, setSelectedPlan });

  // Initialize slug availability
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [slugToCheck, setSlugToCheck] = useState<string>("");

  // Initialize form with existing data
  const {
    form,
    isSubmitting,
    currentStep,
    handleNextStep,
    handlePreviousStep,
    onSubmitHandler,
    setIsSubmitIntended,
  } = useOnboardingForm({
    redirectSlug,
    message,
    user,
    existingData,
    slugAvailable,
    selectedPlan,
  });

  // Initialize slug availability hook after form is ready
  const { isCheckingSlug } = useSlugAvailability({
    form,
    slugToCheck,
    setSlugAvailable
  });

  // Initialize pincode details
  const { isPincodeLoading, availableLocalities, handlePincodeChange } =
    usePincodeDetails({
      form,
      initialPincode: existingData?.pincode,
      initialLocality: existingData?.locality,
    });

  // Auto-check business slug when on step 2 and slug is pre-filled
  useEffect(() => {
    const checkPrefilledSlug = async () => {
      // Only check when we're on step 2, not loading existing data, and have a business slug
      if (currentStep === 2 && !isLoadingExistingData && existingData?.businessSlug) {
        const slug = existingData.businessSlug.trim();

        // Only check if slug is valid and at least 3 characters
        if (slug.length >= 3 && /^[a-z0-9-]+$/.test(slug)) {
          console.log("Auto-checking pre-filled slug:", slug);
          setSlugToCheck(slug);
        }
      }
    };

    checkPrefilledSlug();
  }, [currentStep, isLoadingExistingData, existingData]);



  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <BusinessDetailsStep
            form={form}
            isSubmitting={isSubmitting}
            user={user}
            existingData={existingData}
          />
        );
      case 2:
        return (
          <CardInformationStep
            form={form}
            isSubmitting={isSubmitting}
            user={user}
            existingData={existingData}
            slugAvailable={slugAvailable}
            isCheckingSlug={isCheckingSlug}
            setSlugToCheck={setSlugToCheck}
            setSlugAvailable={setSlugAvailable}
          />
        );
      case 3:
        return (
          <AddressStep
            form={form}
            isSubmitting={isSubmitting}
            user={user}
            existingData={existingData}
            availableLocalities={availableLocalities}
            isPincodeLoading={isPincodeLoading}
            handlePincodeChange={handlePincodeChange}
          />
        );
      case 4:
        return (
          <PlanSelectionStep
            form={form}
            isSubmitting={isSubmitting}
            user={user}
            existingData={existingData}
            selectedPlan={selectedPlan}
            setSelectedPlan={setSelectedPlan}
            showPlans={showPlans}
            setShowPlans={setShowPlans}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full flex items-center justify-center relative">
      {/* Decorative elements */}
      <div className="fixed -top-24 -left-24 w-64 h-64 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl pointer-events-none" />
      <div className="fixed -bottom-32 -right-32 w-96 h-96 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl pointer-events-none" />

      <div className="w-full max-w-[90%] sm:max-w-md md:max-w-lg">
        <Card className="w-full border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl shadow-lg relative overflow-hidden backdrop-blur-sm">
          {/* Loading overlay */}
          <LoadingOverlay isLoading={isLoadingExistingData} />

          {/* Step progress */}
          <StepProgress
            currentStep={currentStep}
            existingData={existingData}
            _isLoadingExistingData={isLoadingExistingData}
          />

          {/* Form */}
          <CardContent className="p-0">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-6">
                <div key={currentStep} className="space-y-6">
                  {renderStepContent()}
                </div>

                <Separator className="bg-border/50 dark:bg-neutral-700/50 my-6 sm:my-8" />

                <div className="flex justify-between items-center">
                  <NavigationButtons
                    currentStep={currentStep}
                    isSubmitting={isSubmitting}
                    isCheckingSlug={isCheckingSlug}
                    slugAvailable={slugAvailable}
                    selectedPlan={selectedPlan}
                    existingData={existingData}
                    onNextStep={handleNextStep}
                    onPreviousStep={handlePreviousStep}
                    onSubmitIntended={() => setIsSubmitIntended(true)}
                  />
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
