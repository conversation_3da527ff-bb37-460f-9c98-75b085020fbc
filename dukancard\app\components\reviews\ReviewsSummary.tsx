"use client";

import React from "react";
import { motion } from "framer-motion";
import { Star, SortAsc } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ReviewSortBy } from "@/lib/actions/reviews";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ReviewsSummaryProps {
  averageRating: number;
  totalReviews: number;
  _sortBy: ReviewSortBy;
  onSortChange: (_sortBy: ReviewSortBy) => void;
}

export default function ReviewsSummary({
  averageRating,
  totalReviews,
  _sortBy,
  onSortChange,
}: ReviewsSummaryProps) {
  // No rating distribution calculation needed

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="rounded-xl border border-neutral-200 dark:border-neutral-700 bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-900 dark:to-neutral-800 p-4 sm:p-6 shadow-md mb-6 sm:mb-8 overflow-hidden"
    >
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 sm:gap-6 overflow-hidden">
        <div className="flex items-start gap-4">
          {/* Animated rating icon */}
          <motion.div
            initial={{ scale: 0.8, rotate: -10 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{
              type: "spring",
              stiffness: 260,
              damping: 20,
              delay: 0.1,
            }}
            className="p-2 sm:p-3 rounded-full bg-gradient-to-br from-yellow-100 to-amber-200 dark:from-yellow-800/80 dark:to-amber-900/80 text-amber-600 dark:text-amber-300 shadow-md flex-shrink-0"
          >
            <Star className="w-5 h-5 sm:w-6 sm:h-6" />
          </motion.div>

          <div className="space-y-3">
            <div className="space-y-1">
              <h3 className="text-lg sm:text-xl font-bold text-neutral-800 dark:text-neutral-100">
                Customer Reviews
              </h3>
              <div className="flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map((star, index) => (
                    <motion.div
                      key={star}
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: index * 0.05 + 0.2 }}
                    >
                      <Star
                        className={cn(
                          "w-4 h-4 mx-0.5",
                          star <= Math.round(averageRating)
                            ? "text-yellow-400 fill-yellow-400"
                            : "text-neutral-300 dark:text-neutral-600"
                        )}
                      />
                    </motion.div>
                  ))}
                </div>
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="font-medium"
                >
                  {averageRating.toFixed(1)} out of 5
                </motion.span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3">
          {/* Total reviews badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.3 }}
            className="flex items-center justify-center px-4 py-2 bg-neutral-100 dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 shadow-sm"
          >
            <span className="text-lg font-bold text-neutral-800 dark:text-neutral-200">
              {totalReviews}
            </span>
            <span className="ml-2 text-sm text-neutral-600 dark:text-neutral-400">
              {totalReviews === 1 ? "Review" : "Reviews"}
            </span>
          </motion.div>

          {/* Sort dropdown with enhanced styling */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.4 }}
          >
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1.5 w-full bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200"
                >
                  <SortAsc className="h-4 w-4" />
                  <span>Sort by: </span>
                  <span className="font-medium text-[var(--brand-gold)]">
                    {_sortBy === "newest"
                      ? "Newest"
                      : _sortBy === "oldest"
                      ? "Oldest"
                      : _sortBy === "highest_rating"
                      ? "Highest rated"
                      : "Lowest rated"}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48 p-1">
                <DropdownMenuItem
                  onClick={() => onSortChange("newest")}
                  className={cn(
                    "cursor-pointer transition-colors duration-200",
                    _sortBy === "newest"
                      ? "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]"
                      : ""
                  )}
                >
                  <motion.div
                    whileTap={{ scale: 0.97 }}
                    className="flex items-center w-full"
                  >
                    Newest first
                  </motion.div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onSortChange("oldest")}
                  className={cn(
                    "cursor-pointer transition-colors duration-200",
                    _sortBy === "oldest"
                      ? "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]"
                      : ""
                  )}
                >
                  <motion.div
                    whileTap={{ scale: 0.97 }}
                    className="flex items-center w-full"
                  >
                    Oldest first
                  </motion.div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onSortChange("highest_rating")}
                  className={cn(
                    "cursor-pointer transition-colors duration-200",
                    _sortBy === "highest_rating"
                      ? "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]"
                      : ""
                  )}
                >
                  <motion.div
                    whileTap={{ scale: 0.97 }}
                    className="flex items-center w-full"
                  >
                    Highest rating
                  </motion.div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onSortChange("lowest_rating")}
                  className={cn(
                    "cursor-pointer transition-colors duration-200",
                    _sortBy === "lowest_rating"
                      ? "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]"
                      : ""
                  )}
                >
                  <motion.div
                    whileTap={{ scale: 0.97 }}
                    className="flex items-center w-full"
                  >
                    Lowest rating
                  </motion.div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}
