"use client";

import { motion } from "framer-motion";
import { Package, CheckCircle2, XCircle, TrendingUp } from "lucide-react";
import { useProducts } from "../../context/ProductsContext";

export default function ProductStats() {
  const { products, totalCount, planLimit } = useProducts();

  const availableCount = products.filter(p => p.is_available).length;
  const unavailableCount = products.filter(p => !p.is_available).length;
  const availablePercentage = products.length > 0 ? Math.round((availableCount / products.length) * 100) : 0;

  const stats = [
    {
      title: "Total Products",
      value: totalCount,
      subtitle: planLimit === Infinity ? "Unlimited plan" : `${totalCount} of ${planLimit} used`,
      icon: Package,
      color: "primary",
      progress: planLimit === Infinity ? 15 : Math.min(100, Math.round((totalCount / planLimit) * 100)),
    },
    {
      title: "Available",
      value: availableCount,
      subtitle: `${availablePercentage}% of inventory active`,
      icon: CheckCircle2,
      color: "emerald",
      progress: availablePercentage,
    },
    {
      title: "Unavailable",
      value: unavailableCount,
      subtitle: unavailableCount === 0 ? "All products active" : `${100 - availablePercentage}% inactive`,
      icon: XCircle,
      color: "rose",
      progress: 100 - availablePercentage,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: index * 0.1 }}
          className="group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300"
        >
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40" />

          <div className="relative p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400 tracking-wide">
                  {stat.title}
                </p>
                <div className="flex items-baseline gap-2">
                  <h3 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 tracking-tight">
                    {stat.value.toLocaleString()}
                  </h3>
                  {stat.title === "Total Products" && (
                    <TrendingUp className="w-4 h-4 text-primary opacity-60" />
                  )}
                </div>
              </div>

              <div className={`
                flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300
                ${stat.color === 'primary' ? 'bg-primary/10 text-primary group-hover:bg-primary/15' : ''}
                ${stat.color === 'emerald' ? 'bg-emerald-50 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400 group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/30' : ''}
                ${stat.color === 'rose' ? 'bg-rose-50 text-rose-600 dark:bg-rose-900/20 dark:text-rose-400 group-hover:bg-rose-100 dark:group-hover:bg-rose-900/30' : ''}
              `}>
                <stat.icon className="w-6 h-6" />
              </div>
            </div>

            <div className="space-y-3">
              <p className="text-sm text-neutral-500 dark:text-neutral-400 leading-relaxed">
                {stat.subtitle}
              </p>

              {/* Progress bar */}
              <div className="space-y-2">
                <div className="flex justify-between items-center text-xs text-neutral-500 dark:text-neutral-400">
                  <span>Progress</span>
                  <span>{stat.progress}%</span>
                </div>
                <div className="w-full bg-neutral-200 dark:bg-neutral-800 rounded-full h-2 overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${stat.progress}%` }}
                    transition={{ duration: 1, delay: index * 0.2 + 0.5, ease: "easeOut" }}
                    className={`
                      h-full rounded-full transition-all duration-300
                      ${stat.color === 'primary' ? 'bg-gradient-to-r from-primary to-primary/80' : ''}
                      ${stat.color === 'emerald' ? 'bg-gradient-to-r from-emerald-500 to-emerald-400' : ''}
                      ${stat.color === 'rose' ? 'bg-gradient-to-r from-rose-500 to-rose-400' : ''}
                    `}
                  />
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
